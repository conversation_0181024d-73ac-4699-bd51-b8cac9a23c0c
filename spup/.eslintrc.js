module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true
  },
  extends: [
    'eslint:recommended',
    '@vue/typescript/recommended'
  ],
  parserOptions: {
    ecmaVersion: 2021,
    sourceType: 'module'
  },
  rules: {
    // 允许console语句
    'no-console': 'off',
    // 允许debugger语句
    'no-debugger': 'off',
    // 允许未使用的变量（UniApp中常见）
    '@typescript-eslint/no-unused-vars': 'off',
    // 允许any类型
    '@typescript-eslint/no-explicit-any': 'off'
  },
  overrides: [
    {
      files: ['pages.json', 'manifest.json'],
      parser: 'jsonc-eslint-parser',
      rules: {
        // 允许JSON中的注释
        'jsonc/no-comments': 'off'
      }
    }
  ],
  globals: {
    uni: 'readonly',
    wx: 'readonly',
    getCurrentPages: 'readonly',
    getApp: 'readonly'
  }
}
