<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Viewport Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .info {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .viewport-content {
            font-family: monospace;
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <h1>Viewport Dynamic Setting Test</h1>
    
    <div class="info">
        <h3>Current Viewport Meta Content:</h3>
        <div class="viewport-content" id="viewport-display">Loading...</div>
    </div>
    
    <div class="info">
        <h3>CSS Support Detection:</h3>
        <div id="css-support">Checking...</div>
    </div>
    
    <div class="info">
        <h3>Screen Information:</h3>
        <div id="screen-info">Loading...</div>
    </div>

    <script>
        // 复制main.js中的viewport设置逻辑
        function setupDynamicViewport() {
            // 检测是否支持CSS安全区域
            const coverSupport = typeof CSS !== 'undefined' && 
                typeof CSS.supports === 'function' && 
                (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'));
            
            // 构建viewport内容
            const viewportContent = 'width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
                (coverSupport ? ', viewport-fit=cover' : '');
            
            // 查找现有的viewport meta标签并更新
            const existingViewportMeta = document.querySelector('meta[name="viewport"]');
            if (existingViewportMeta) {
                existingViewportMeta.setAttribute('content', viewportContent);
                console.log('Viewport updated:', viewportContent);
            } else {
                // 如果没有找到，则创建新的meta标签
                const meta = document.createElement('meta');
                meta.name = 'viewport';
                meta.content = viewportContent;
                document.head.appendChild(meta);
                console.log('Viewport created:', viewportContent);
            }
            
            return { viewportContent, coverSupport };
        }

        // 显示信息的函数
        function displayInfo() {
            const { viewportContent, coverSupport } = setupDynamicViewport();
            
            // 显示viewport内容
            document.getElementById('viewport-display').textContent = viewportContent;
            
            // 显示CSS支持信息
            document.getElementById('css-support').innerHTML = `
                <strong>CSS.supports available:</strong> ${typeof CSS !== 'undefined' && typeof CSS.supports === 'function'}<br>
                <strong>env(a) support:</strong> ${typeof CSS !== 'undefined' && CSS.supports && CSS.supports('top: env(a)')}<br>
                <strong>constant(a) support:</strong> ${typeof CSS !== 'undefined' && CSS.supports && CSS.supports('top: constant(a)')}<br>
                <strong>Cover support:</strong> ${coverSupport}
            `;
            
            // 显示屏幕信息
            document.getElementById('screen-info').innerHTML = `
                <strong>Screen size:</strong> ${screen.width} x ${screen.height}<br>
                <strong>Window size:</strong> ${window.innerWidth} x ${window.innerHeight}<br>
                <strong>Device pixel ratio:</strong> ${window.devicePixelRatio}<br>
                <strong>User agent:</strong> ${navigator.userAgent.substring(0, 100)}...
            `;
        }

        // 页面加载完成后执行
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', displayInfo);
        } else {
            displayInfo();
        }
    </script>
</body>
</html>
