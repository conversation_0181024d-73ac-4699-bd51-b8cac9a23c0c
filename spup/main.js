/**
 * 动态设置viewport meta标签，支持安全区域 (CSP兼容版本)
 * 这个函数替代了原本在HTML中的内联脚本
 */
function setupDynamicViewport() {
  // 检测是否支持CSS安全区域
  const coverSupport = typeof CSS !== 'undefined' &&
    typeof CSS.supports === 'function' &&
    (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'));

  // 构建viewport内容
  const viewportContent = 'width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
    (coverSupport ? ', viewport-fit=cover' : '');

  // 查找现有的viewport meta标签并更新
  const existingViewportMeta = document.querySelector('meta[name="viewport"]');
  if (existingViewportMeta) {
    existingViewportMeta.setAttribute('content', viewportContent);
    console.log('Viewport updated:', viewportContent);
  } else {
    // 如果没有找到，则创建新的meta标签
    const meta = document.createElement('meta');
    meta.name = 'viewport';
    meta.content = viewportContent;
    document.head.appendChild(meta);
    console.log('Viewport created:', viewportContent);
  }
}

// 立即执行viewport设置（在DOM加载前）
if (typeof document !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', setupDynamicViewport);
  } else {
    setupDynamicViewport();
  }
}

// UniApp Vue3 应用
import { createSSRApp } from 'vue'
import App from './App.vue'
import { createPinia } from 'pinia'
import piniaPersist from 'pinia-plugin-persist-uni'

export function createApp() {
  try {
    const app = createSSRApp(App)
    //pinia持久化，参考：https://juejin.cn/post/7081275565008748552/
    // https://blog.csdn.net/weixin_44728473/article/details/125616404
    const pinia = createPinia()
    pinia.use(piniaPersist)
    app.use(pinia)
    return {
      app
    }
  } catch (error) {
    console.error('UniApp Vue3应用初始化失败:', error)
    throw error // 向上抛出错误以便上层处理
  }
}