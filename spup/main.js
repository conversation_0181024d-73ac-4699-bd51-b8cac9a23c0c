// main.js
import { createSSRApp } from 'vue'
import App from './App.vue'
import { createPinia } from 'pinia'
import piniaPersist from 'pinia-plugin-persist-uni'

/**
 * 动态设置viewport meta标签，支持安全区域
 */
function setupViewport() {
  const coverSupport = typeof CSS !== 'undefined' && 
    typeof CSS.supports === 'function' && 
    (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'));
  
  const viewportContent = `width=device-width, user-scalable=no, initial-scale=1.0, 
                           maximum-scale=1.0, minimum-scale=1.0${
    coverSupport ? ', viewport-fit=cover' : ''
  }`;
  
  const meta = document.querySelector('meta[name="viewport"]');
  if (meta) {
    meta.setAttribute('content', viewportContent);
  } else {
    const newMeta = document.createElement('meta');
    newMeta.name = 'viewport';
    newMeta.content = viewportContent;
    document.head.appendChild(newMeta);
  }
}


/**
 * 创建Vue3应用实例
 * @returns {Object} 包含app和pinia实例的对象
 */
export function createApp() {
  try {
    // 创建SSR应用实例
    const app = createSSRApp(App)
    
    // 配置pinia状态管理并启用持久化
    // 持久化存储插件参考: 
    // https://juejin.cn/post/7081275565008748552
    // https://blog.csdn.net/weixin_44728473/article/details/125616404
    const pinia = createPinia()
    pinia.use(piniaPersist)
    
    // 注册pinia插件
    app.use(pinia)
    
    return {
      app,
      // pinia // 显式返回pinia实例供外部使用
    }
  } catch (error) {
    console.error('Vue3应用初始化失败:', error)
    throw error // 向上抛出错误以便上层处理
  }
}

/**
 * 初始化应用：设置viewport并挂载Vue应用
 */
async function initApplication() {
  try {
    setupViewport();
    
    const { app } = createApp();
    
    // 防止重复挂载
    const container = document.getElementById('app');
    if (!container.__vue_app__) {
      app.mount('#app');
      console.log('应用初始化完成');
    } else {
      console.warn('应用已挂载，跳过重复挂载');
    }
  } catch (error) {
    console.error('应用初始化错误:', error);
  }
}

// 确保只初始化一次
let isAppInitialized = false;
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    if (!isAppInitialized) {
      initApplication();
      isAppInitialized = true;
    }
  });
} else {
  if (!isAppInitialized) {
    initApplication();
    isAppInitialized = true;
  }
}