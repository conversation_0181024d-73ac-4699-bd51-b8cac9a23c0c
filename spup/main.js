// #ifndef VUE3
import Vue from 'vue'
import App from './App'

Vue.config.productionTip = false

App.mpType = 'app'

const app = new Vue({
    ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import App from './App.vue'
import { createPinia } from 'pinia'
import piniaPersist from 'pinia-plugin-persist-uni'

export function createApp() {
	try {
	  const app = createSSRApp(App)
	  //pinia持久化，参考：https://juejin.cn/post/7081275565008748552/
	  // https://blog.csdn.net/weixin_44728473/article/details/125616404
	  const pinia = createPinia()
	  pinia.use(piniaPersist)
	  app.use(pinia)
	  return {
		app
	  }
	} catch (error) {
		console.error('Vue3应用初始化失败:', error)
		throw error // 向上抛出错误以便上层处理
	  }

}
// #endif