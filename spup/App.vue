<script>
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import {useCommonStore} from '/stores/common.js'
	import {login} from "@/common/api/server.js";
	import dayjs from "dayjs";
		
	export default {
		onLaunch: function() {
			console.warn('当前组件仅支持 uni_modules 目录结构 ，请升级 HBuilderX 到 3.1.0 版本以上！')
			const commonStore = useCommonStore()
			commonStore.lifeMsg +=  "【"+dayjs().format("hh:mm:ss")+"-APP onLaunch:"+ window.location.href+"】\n"
			if(SPUP_GLOBAL_CONFIG.env=="dev"){
				commonStore.userInfo.unionid = "TEST"
			}
			/* #ifndef MP-WEIXIN */
			if(window.location.href.indexOf("/pages/index/ssbIndex")!=-1||window.location.href.indexOf("/pages/index/ssbLogin")!=-1){
				SPUP_GLOBAL_CONFIG.plant = "H5-SSB"
			}else{
				SPUP_GLOBAL_CONFIG.plant = "H5-WX"
				if(SPUP_GLOBAL_CONFIG.env=="pro" && !commonStore.userInfo.unionid  && !commonStore.isMpLoginRedirect && window.location.href.indexOf(SPUP_GLOBAL_CONFIG.mpLoginRedirectUrl)==-1){//不是重定向，需要公众号登录
					//登录
					commonStore.isMpLoginRedirect = true
					login()
				}else{
					commonStore.isMpLoginRedirect = false	//下次打开还是得重新登录
				}
			}
			commonStore.lifeMsg +=  "【"+dayjs().format("hh:mm:ss")+"-APP onLaunch plant:"+ SPUP_GLOBAL_CONFIG.plant+"】\n"
			/* #endif */
			
			// 检查存储的版本号
			let nowTime = dayjs().valueOf();
			const savedVersion = localStorage.getItem('SPUP_MP_TVERSION');
			console.log("SPUP_MP_TVERSION:", savedVersion);
			if(!savedVersion || nowTime - parseInt(savedVersion) > SPUP_GLOBAL_CONFIG.refreshInterval) {
			  localStorage.setItem('SPUP_MP_TVERSION', nowTime);
			  window.location.reload(true);			  // 强制刷新（避免缓存）
			}
		},
		onShow: function() {
			console.log('App Show')
		
		},
		onHide: function() {
			console.log('App Hide')
		},
		onPageNotFound: function (res) {
			// 跳转到 404 页面：
			uni.redirectTo({
				url: "pages/common/404", // 404 页面的路径
			});
		}
		
		
	}
</script>

<style lang="scss">
	/*每个页面公共css */
	@import '@/uni_modules/uni-scss/index.scss';
	@import '@/pages/common/ckeditor.content-style.css';
	/* #ifndef APP-NVUE */
	@import '@/static/customicons.css';
	// 设置整个项目的背景色
	page {
		background-color: #f2f2f2;
		font-size: 14px;
		font-weight: 400;
		line-height: 24px;
		-webkit-user-select: text;
	}

	/* #endif */
	.example-info {
		font-size: 14px;
		color: #333;
		padding: 10px;
	}
	
	// H5页面需要在此处定义
	:root{
		--button-color:#815462;
		--assist-color:#815462;
		--button-radius:6px;
		--button-small-radius:8px;
		--button-solid-color:#999;
		--button-n:#EDEFF0;
		--button-n-text:#8c8c8c;
		--bt-color:#da2424;
	}
	.uni-datetime-picker--btn {
		background-color: var(--button-color) !important;
	}
	.uni-calendar-item__weeks-box .uni-calendar-item--checked {
		background-color: var(--button-color) !important;
	}
	.uni-toast__content {
	    margin: 0 10px 15px;
	    font-size: 15px;
	}
</style>

