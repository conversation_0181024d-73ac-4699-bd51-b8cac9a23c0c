<template>
  <!-- 开发环境显示首页内容 -->
  <div id="app-container">
    <div v-if="isDevelopment" class="dev-container">
      <h1>UniApp 开发环境</h1>
      <p>当前平台: {{ SPUP_GLOBAL_CONFIG.plant }}</p>
      <p>环境: {{ SPUP_GLOBAL_CONFIG.env }}</p>
      <div class="page-links">
        <h3>可用页面:</h3>
        <ul>
          <li><a href="#" @click="loadPage('home')">首页 (pages/home/<USER>/a></li>
          <li><a href="#" @click="loadPage('buyPerson')">个人预约 (pages/buyPerson/buyPerson)</a></li>
          <li><a href="#" @click="loadPage('orderList')">我的预约 (pages/order/orderList)</a></li>
          <li><a href="#" @click="loadPage('about')">关于我们 (pages/about/about_page)</a></li>
        </ul>
      </div>
      <div v-if="currentPage" class="current-page">
        <h3>当前页面: {{ currentPage }}</h3>
        <component :is="currentPageComponent" v-if="currentPageComponent" />
      </div>
    </div>
  </div>
</template>

<script>
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import {useCommonStore} from '/stores/common.js'
	import {login} from "@/common/api/server.js";
	import dayjs from "dayjs";
	import { ref, computed, defineAsyncComponent } from 'vue';
		
	export default {
		setup() {
			const currentPage = ref('home')
			const isDevelopment = computed(() => import.meta.env.DEV)

			// 动态加载页面组件
			const currentPageComponent = computed(() => {
				if (!currentPage.value) return null

				const pageMap = {
					home: defineAsyncComponent(() => import('./pages/home/<USER>')),
					buyPerson: defineAsyncComponent(() => import('./pages/buyPerson/buyPerson.vue')),
					orderList: defineAsyncComponent(() => import('./pages/order/orderList.vue')),
					about: defineAsyncComponent(() => import('./pages/about/about_page.vue'))
				}

				return pageMap[currentPage.value] || null
			})

			const loadPage = (pageName) => {
				currentPage.value = pageName
				console.log('Loading page:', pageName)
			}

			// 初始化应用逻辑
			const initApp = () => {
				console.warn('当前组件仅支持 uni_modules 目录结构 ，请升级 HBuilderX 到 3.1.0 版本以上！')
				const commonStore = useCommonStore()
				commonStore.lifeMsg +=  "【"+dayjs().format("hh:mm:ss")+"-APP onLaunch:"+ window.location.href+"】\n"
				if(SPUP_GLOBAL_CONFIG.env=="dev"){
					commonStore.userInfo.unionid = "TEST"
				}

				if(window.location.href.indexOf("/pages/index/ssbIndex")!=-1||window.location.href.indexOf("/pages/index/ssbLogin")!=-1){
					SPUP_GLOBAL_CONFIG.plant = "H5-SSB"
				}else{
					SPUP_GLOBAL_CONFIG.plant = "H5-WX"
					if(SPUP_GLOBAL_CONFIG.env=="pro" && !commonStore.userInfo.unionid  && !commonStore.isMpLoginRedirect && window.location.href.indexOf(SPUP_GLOBAL_CONFIG.mpLoginRedirectUrl)==-1){
						commonStore.isMpLoginRedirect = true
						login()
					}else{
						commonStore.isMpLoginRedirect = false
					}
				}
				commonStore.lifeMsg +=  "【"+dayjs().format("hh:mm:ss")+"-APP onLaunch plant:"+ SPUP_GLOBAL_CONFIG.plant+"】\n"

				// 检查存储的版本号
				let nowTime = dayjs().valueOf();
				const savedVersion = localStorage.getItem('SPUP_MP_TVERSION');
				console.log("SPUP_MP_TVERSION:", savedVersion);
				if(!savedVersion || nowTime - parseInt(savedVersion) > SPUP_GLOBAL_CONFIG.refreshInterval) {
				  localStorage.setItem('SPUP_MP_TVERSION', nowTime);
				  // 在开发环境中不要自动刷新
				  if (!isDevelopment.value) {
				    window.location.reload(true);
				  }
				}
			}

			// 在组件挂载时初始化
			initApp()

			return {
				currentPage,
				currentPageComponent,
				isDevelopment,
				loadPage,
				SPUP_GLOBAL_CONFIG
			}
		},

		// UniApp 生命周期方法（用于实际UniApp环境）
		onLaunch: function() {
			console.log('UniApp onLaunch')
		},
		onShow: function() {
			console.log('UniApp onShow')
		},
		onHide: function() {
			console.log('UniApp onHide')
		},
		onPageNotFound: function (res) {
			console.log('UniApp onPageNotFound', res)
		}
	}
</script>

<style lang="scss">
	/*每个页面公共css */
	@import '@/uni_modules/uni-scss/index.scss';
	@import '@/pages/common/ckeditor.content-style.css';
	/* #ifndef APP-NVUE */
	@import '@/static/customicons.css';
	// 设置整个项目的背景色
	page {
		background-color: #f2f2f2;
		font-size: 14px;
		font-weight: 400;
		line-height: 24px;
		-webkit-user-select: text;
	}

	/* #endif */
	.example-info {
		font-size: 14px;
		color: #333;
		padding: 10px;
	}
	
	// H5页面需要在此处定义
	:root{
		--button-color:#815462;
		--assist-color:#815462;
		--button-radius:6px;
		--button-small-radius:8px;
		--button-solid-color:#999;
		--button-n:#EDEFF0;
		--button-n-text:#8c8c8c;
		--bt-color:#da2424;
	}
	.uni-datetime-picker--btn {
		background-color: var(--button-color) !important;
	}
	.uni-calendar-item__weeks-box .uni-calendar-item--checked {
		background-color: var(--button-color) !important;
	}
	.uni-toast__content {
	    margin: 0 10px 15px;
	    font-size: 15px;
	}

	/* 开发环境样式 */
	.dev-container {
		padding: 20px;
		font-family: Arial, sans-serif;
		max-width: 1200px;
		margin: 0 auto;
	}

	.dev-container h1 {
		color: #815462;
		border-bottom: 2px solid #815462;
		padding-bottom: 10px;
	}

	.page-links {
		background: #f7f9fa;
		padding: 15px;
		border-radius: 8px;
		margin: 20px 0;
	}

	.page-links ul {
		list-style: none;
		padding: 0;
	}

	.page-links li {
		margin: 10px 0;
	}

	.page-links a {
		color: #815462;
		text-decoration: none;
		padding: 8px 15px;
		border: 1px solid #815462;
		border-radius: 4px;
		display: inline-block;
		transition: all 0.3s;
	}

	.page-links a:hover {
		background: #815462;
		color: white;
	}

	.current-page {
		border: 1px solid #ddd;
		border-radius: 8px;
		padding: 20px;
		margin-top: 20px;
		background: white;
	}
</style>

