<template>
	<view class="visitor-note-wrap" >
		<view class="flex-row justify-between">
			<view class="popup-title">预约须知</view>
		</view>
		<view class="content" >
			{{configStore.yyxz}}<br>
		<!-- 	•《参观指南》(<text style="text-decoration:underline;" @click="visitorNoteClick">{{visitorNoteVisible?"点击关闭详情":"点击查看详情"}}</text>）。<br>
			<view v-if="visitorNoteVisible" class="visit-note-wrap">
				{{configStore.visitNote}}
			</view>
			• 预约成功即视为已认可上述须知，感谢您的支持与配合，祝您参观愉快！<br> -->
		</view>
		<view class="flex-row justify-between">
			<view class="popup-title">文明参观须知</view>
		</view>
		<view class="content" >
			<view v-if="configStore.wmcgxz.indexOf('《禁带物品清单》')!=-1">
				{{configStore.wmcgxz.split("《禁带物品清单》")[0]}}《禁带物品清单》(<text style="text-decoration:underline;color:#8d5b6e;" @click="visitorNoteClick">{{visitorNoteVisible?"点击关闭详情":"点击查看详情"}}</text>）。
				<view v-if="visitorNoteVisible" class="visit-note-wrap">
					{{configStore.jdwpqd}}
				</view>
				{{configStore.wmcgxz.split("《禁带物品清单》")[1]}}
			</view>
			<view v-else>{{configStore.wmcgxz}}</view>
		</view>
	</view>
</template>

<script setup>
	import {ref, onMounted} from 'vue'
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {configRefStore} from '@/stores/configRef.js'
	const configStore = configRefStore();
	import {getNotes} from "@/common/api/server.js";
	
	const visitorNoteVisible = ref(false)
	const goto = (url) =>{
		uni.navigateTo({
			url: url
		})
	}
	
	const visitorNoteClick = ()=>{
		visitorNoteVisible.value = !visitorNoteVisible.value
	}
	
	onMounted(()=>{
		getNotes();
	})
</script>

<style>
page{
	background: #fff;
}
.visitor-note-wrap{
	white-space: pre-wrap;
	padding: 10px 15px;
}
.visit-note-wrap{
	margin: 20rpx;
	background: #9999992e;
	padding: 20rpx; 
	border-radius: 10rpx;
}
.popup-title {
	width: 100%;
	text-align: center;
	margin: auto;
	font-size: 18px;
	font-weight: 500;
	color: rgba(0, 0, 0, 0.85);
	line-height: 24px;
	padding: 15px 0;
}
</style>
