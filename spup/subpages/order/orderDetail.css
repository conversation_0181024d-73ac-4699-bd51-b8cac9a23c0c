uni-button:after {
		content: none;
	}

	.info-content {
		position: relative;
	}

	.info-page {
		position: absolute;
		position: absolute;
		width: 100%;
		top: 0;
		margin-top: 70px;
	}
	.head-line {
		width: 100%;
		height: 20rpx;
		background: #955565;
		border-radius: 6px;
	}

	.head-status {
		color: #fff;
		text-align: center;
		font-size: 16px;
		line-height: 18px;
		margin-bottom: 20px;
		/* margin-top: -15px; */
	}

	.status-img {
		width: 24px;
		height: 18px;
		margin-right: 13px;
	}

	.mini-color {
		color: #8c8c8c;
	}

	.text-color {
		color: #000;
	}
	.page-top {
		padding: 0 5px;
		margin-top: -3px;
	}
	.page-top-color {
		width: 100%;
		height: 14px;
		background-image: linear-gradient(rgba(210, 196, 200, 1), rgba(255, 255, 255, 1) );
	}
	.grad {
		width: 100%;
		padding-top: 6px;
		padding-bottom: 10px;
		margin: auto;
		background: radial-gradient(circle at left, transparent 10px, #fff 0) top left /70% 180% no-repeat,
			radial-gradient(circle at right, transparent 10px, #fff 0) top right /70% 180% no-repeat;
		/* filter: drop-shadow(1px 0 5px rgba(158, 158, 158, .5)); */
	}
	.grad-text{
		padding: 10px  16px  0 16px;
		line-height: 30px;
		
	}
	.line-bottom{
		padding-bottom: 10px;
		border-bottom: 1px dashed #D9D9D9;
	}
	/* 下面的内容 */
	.information-box {
		background: #fff;
		padding: 0 16px 18px 16px;
	}
	.size-left{
		font-size: 13px;
		font-weight: 500;
		color: #000;
	}
	.size-span2{
		font-size: 12px;
		font-weight: 300;
		color: #8c8c8c;
	}
	.btn-s{
		text-align: center;
		padding: 1px 10px;
		border: 1px solid var(--button-color);
		border-radius: 6px;
		font-size: 12px;
		font-weight: 400;
		color: var(--button-color);
	}
	.content-bottom{
		padding: 20px 0;
	}
	
	.card-box{
		border-bottom: 1px solid #D9D9D9;
	}
	.card-box:last-child{
		border-bottom:none;
	}
	
