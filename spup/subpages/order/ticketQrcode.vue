<template>
	<view class="qr-box">
		<image :mode="scaleToFill" class="qrcode-img" :src="base64QrCodeImg"></image>
		<!-- <image src="../../static/lizi.png" class="qrcode-img"></image> -->
	</view>
	<view style="text-align: center;">
		<view>场次：{{qrCodeData.batchNo}}</view>
		<view>待使用</view>
		<view style="color: #999;">出示二维码扫描后进入</view>
	</view>
</template>

<script setup>
	import {ref,reactive} from 'vue';
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import dayjs from "dayjs";
	/**
	 * vue3在小程序中生成二维码，试了很多方法都不行，最终用base64生成图片的方式才OK，
	 * 参考：https://juejin.cn/post/7062175064967348237
	 * 参考：https://github.com/Pudon/weapp-qrcode-base64/blob/master/demo/weapp-demo/pages/index/index.js
	 * 安装方式：yarn add qrcode-base64
	 */
	import QR from 'qrcode-base64'
	
	const qrCodeData = reactive({
		category:"",
		orderNo:"",
		suborderNo:"",
		time:0,
		batchNo:"",
	});
	
	const base64QrCodeImg = ref("");
	const beautifiedBatchNo = ref();
	
	onLoad(options => {
		console.log("options:", options);	//接收页面传来的参数
		qrCodeData.batchNo = dayjs(options.batchNo).format("YYYY年MM月DD日HH时mm分")
		qrCodeData.category = options.category
		qrCodeData.orderNo = options.orderNo
		qrCodeData.suborderNo = options.suborderNo
		// qrCodeData.batchNo = options.batchNo
		// qrCodeData.batchNo = dayjs('202506261000').format("YYYY年MM月DD日HH时mm分")
		// beautifiedBatchNo = qrCodeData.batchNo
		// const customFormat = dayjs(beautifiedBatchNo).format("YYYYMMDDHHmm")
		
		function randomString(e) {    
		    e = e || 32;
		    var t = "ABCDEFGHJKMNQRSTWXYZabcdefhijkmnprstwxyz012345678",
		    a = t.length,
		    n = "";
		    for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a));
		    return n
		}
		
		let qrcodeStr = randomString(8)+"sPUp2000"+qrCodeData.category+"sPUp2000"+qrCodeData.orderNo+"sPUp2000"+qrCodeData.suborderNo+"sPUp2000"+randomString(8)
		base64QrCodeImg.value = QR.drawImg(qrcodeStr, {
		  typeNumber: 4,
		  errorCorrectLevel: 'M',
		  size: 500
		})
		// base64QrCodeImg.value = QR.drawImg(JSON.stringify(qrCodeData), {
		//   typeNumber: 4,
		//   errorCorrectLevel: 'M',
		//   size: 500
		// })
	});
</script>

<style>
	.qr-box {
		padding: 40rpx;
		margin: 100rpx 100rpx 60rpx 100rpx;
		background-color: #ffffff;
		border-radius: 20rpx;
		box-shadow: 0 0 40rpx rgba(0, 0, 0, 0.1);
	}
	.qrcode-img{
		width: 100%;
		/* height: 480rpx; */
	}
	
</style>
