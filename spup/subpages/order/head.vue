<template>
	<view >
		<view class="StatusBarTop" :style="{'height':data.StatusBarHeight,'color':'#000'}"></view>
		<!-- 顶部的状态栏，不应该放入信息，仅用于占位 -->
		<!-- <view class="TitleBarStance" tits="站位块" :style="{'height':data.TitleBarHeight}"></view> -->
		<!-- 占位块元素，仅用来占据位置，内部也不应该放入信息，因为状态栏与导航标题栏都是fiexd定位，
所以需要它占据一定的位置，方便下面的正文正常显示， -->

		<view class="TitleBarTop" :style="{'background-color':data.navbarBgColor, 'height':data.TitleBarHeight,'padding-top':data.StatusBarHeight}">
			<!-- 自定义导航栏，可以根据需要在其内部放入自己需要的样式等 -->
			<!-- 以下三个view为简洁标题导航栏的样式 -->
			<view class="TitleBarTopLeft " style="display: flex;">
				<!-- 左边的一般为返回按钮或者返回主页，可以自己在 methods 内添加方法决定此处用法，此处放置的是图片，可修改 -->
			<uni-icons type="back" size="22" color="#fff" @click="gotoBack"></uni-icons>
			</view>
			<!-- <view class="TitleBarTopMiddle">
				导航标题栏的正中位置显示内容
				<text>世界你好</text>
			</view> -->
			<view class="StatusBarTopRight">
				<!-- 右侧的，一般为空，用来方便flex布局，而且小程序一般右边自带胶囊按钮会处于这个位置 -->
			</view>
		</view>
	</view>
</template>

<script setup>
	import { ref,reactive,computed, onMounted,getCurrentInstance} from 'vue'
	import {onShow,onHide,onLoad} from '@dcloudio/uni-app';

	//扫一扫
	const gotoBack = ()=>{
		uni.navigateBack({
		    delta: 1
		});
	}
	const data = reactive({
		StatusBarHeight: 0,// 状态栏的高度
		TitleBarHeight: 0,				// 导航标题栏的高度
		navbarBgColor:'rgba(0, 0, 0, 0)',
		navbarcolor:'#ffffff',
		slotTextStatus: "slot-text-1"
	})
	
	onLoad(()=>{
		// 在进入页面时就应该获取到设备的状态栏高度，所以使用onload
		// 设置this指向，避免出现问题
		uni.getSystemInfo({
			// uniapp提供的判断平台的api，具体参考：https://uniapp.dcloud.io/api/system/info?id=getsysteminfo
			success: function(res) {
				// 调用成功的回调函数
				console.log(res);
				if (res.model.indexOf('iPhone') !== -1) {
					// 判断设备型号,此处仅判断手机型号是不是iPhone,
					// 因为iPhone的导航标题栏高度是44px,而其他设备多为48px（注意，这里是 px 而不是 rpx）
					data.TitleBarHeight = 44 + 'px';
					// 根据判断改变导航标题栏的高度，iPhone手机
				} else {
					data.TitleBarHeight = 48 + 'px';
					// 非iPhone手机
				}
				data.StatusBarHeight = res.statusBarHeight + 'px';
				// 获取到的设备状态栏高度，然后将值赋予想对应的对象，以改变对应的状态栏高度

				console.log(data.StatusBarHeight);
				console.log(data.TitleBarHeight);
				//console查看获取到的值
			}
		})
	})
	
	const scrollCallback = (scrollTop)=> { //nvue暂不支持滚动监听，可用bindingx代替
		console.log("子--滚动距离为：" + scrollTop);
		if(scrollTop>50){
			console.log("触 发", data.navbarcolor)
			//设置状态栏和导航栏的文字颜色变成黑色
			data.navbarBgColor = '#ffffff'
			data.navbarcolor = '#000000 !important'
		}else{
			console.log("没 触 发", data.navbarcolor)
			//设置状态栏和导航栏的文字颜色变成默认色
			data.navbarBgColor = 'rgba(0, 0, 0, 0)'
			data.navbarcolor = '#ffffff !important'
		}
	}
	
	defineExpose({
	    scrollCallback,
	})
		
		
</script>


<style>
	.StatusBarTop {
		/* // 状态栏,定位，保证背景与状态栏文字（颜色仅白与黑）的对比色 */

		width: 750rpx ;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 99999;
		/* //这个应该放到最高的地方，不能让别的元素将他遮住 */

		background: transparent;
	}

	.TitleBarStance {
		/* // 站位块，因为导航标题栏定位，所以需要一块等高块元素占据位置， */
		/* // 使下方的元素排列好，不造成塌陷 */

		width: 750rpx;
	}

	.TitleBarTop {
		/* // 标题导航栏,定位，上下居中，微信小程序对应右上角胶囊上下居中 */

		width: 750rpx;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 9999;
		display: flex;
		align-items: center;
		/* 上下必须居中 */

		justify-content: space-between;
		/* 左右向两边靠近，可以通过后面向内部缩进 */
	}

	.TitleBarTopLeft {
		/* width: 80rpx; */
		/* 三个view设置相同宽度，左右两边的view宽度一定要相同，这样中间的view才会在正中间*/

		height: 48rpx;
		/* 高度可以根据所需进行调整，不超过获取的设备状态栏高度即可*/
		display: flex;
		align-items: center;
		justify-content: flex-start;
		margin-left: 15px;
		/* 左边向增加左外边距，那右边一定要加右外边距 */
	}

	.TitleBarTopLeft image {
		width: 48rpx;
		height: 48rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.TitleBarTopMiddle {
		width: 180rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		/* 使用flex布局居中，*/
	}

	.TitleBarTopMiddle text {
		color: #FF0000;
		font-size: 28rpx;
	}

	.StatusBarTopRight {
		width: 80rpx;
		margin-right: 35rpx;
	}

	.mainBody {
		/* 正文部分 */
		height: 1600px;
	}
	.slot-wrap {
		display: flex;
		align-items: center;
		/* 如果您想让slot内容占满整个导航栏的宽度 */
		/* flex: 1; */
		/* 如果您想让slot内容与导航栏左右有空隙 */
		padding: 0 24rpx;
	
	}
	.titleBarTopItem{
		color: #ffffff;
	}
	.slot-text{
		font-size: 16px;
		padding-left: 5px;
	}
	.self-list_line {
		position: relative;
	}

	.self-list_line::after {
		content: '';
		position: absolute;
		left: auto;
		top: auto;
		bottom: 2px;
		right: 0;
		height: 80%;
		width: 1px;
		background-color: rgba(0, 0, 0, 0.5);
	}
		
</style>
