<template>
	<!-- 自定义导航栏 -->
	<Head ref="headRef"></Head>
	<view class="info-content">
		<view style="width: 750rpx;">
			<image mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + '/order/top-bg.png'"></image>
		</view>
		<!-- 预约信息 -->
		<view class="info-page">
			<view style="padding: 0 10px;">
				<!-- 左箭头 -->
				<!-- <uni-icons type="back" size="20" color="#fff" @click="gotoBack"></uni-icons> -->
				<!-- 使用状态 -->
				<view class="head-status">
					<image mode="widthFix" src="../../static/detail/icon-ticket.png" class="status-img"></image>
					<text >待使用</text>
				</view>
				<view class="head-line"></view>
				<view class="page-top">
					<view class="page-top-color"></view>
					<view class="size-mini mini-color grad">
						<view class="grad-text">
							<view class="size-medium text-color">{{title}}</view>
							<view><text class="text-color">预约编号：</text>{{orderData.orderNo}}</view>
							<view><text class="text-color">参观时间：</text>{{dayjs(orderData.batchDate).format('YYYY-MM-DD') }}
								&nbsp;{{orderData.batchStartTime.substr(0,2)}}:{{orderData.batchStartTime.substr(2,4)}}~{{orderData.batchEndTime.substr(0,2)}}:{{orderData.batchEndTime.substr(2,4)}}
							</view>
							<view v-if="orderData.orderCategory==SPUP_GLOBAL_CONFIG.orderType.person"><text class="text-color">参观地点：</text>{{SPUP_GLOBAL_CONFIG.permanentExhibition.exhibitionAddress}}</view>
							<view v-if="orderData.exhibition"><text class="text-color">参观地点：</text>{{orderData.exhibition.exhibitionAddress}}</view>
							<view class="line-bottom"><text class="text-color">下单时间：</text>{{orderData.createTime}}</view>
						</view>
					</view>
					<!-- 预约人信息 -->
					<view class="information-box">
						<template v-for="(item, index) in orderData.suborders" :key="item.id" :orderData='item'>
							<view class="card-box">
								<view class="fol-row content-bottom">
									<view class="fol-col size-left">
										<view style="white-space: normal; overflow-wrap: break-word;max-width: 240px;">{{item.contactsName}}</view>
										<view v-if="item.seatNo">
											<text class="size-span2">座位号：{{item.seatNo}}</text>
										</view>
										<view>
											<text class="size-span2">手机号：{{item.contactsPhone.substr(0,3)}}****{{item.contactsPhone.substr(7,11)}}</text>
										</view>
									</view>
									<view style="margin-top: 10px;">
										<view v-if="item.suborderStatus===SPUP_GLOBAL_CONFIG.orderStatus.reserved"
											style="text-align: right;" @click="showQrcode(item)">
											<view class="btn-s">
												显示核验码</view>
										</view>
										<view v-if="item.suborderStatus===SPUP_GLOBAL_CONFIG.orderStatus.completed"
											style="text-align: right;">
											<view
												style="background-color:#e5e5e5;text-align: center;padding: 2px 10px;color: #999;border-radius: 6px;">
												显示核验码</view>
										</view>
									</view>
									<!-- <view v-if="item.suborderStatus===SPUP_GLOBAL_CONFIG.orderStatus.reserved"
										class="fol-col" style="text-align: right;color: #cc6c27;">
										待使用
									</view> -->
									<!-- <view v-if="item.suborderStatus===SPUP_GLOBAL_CONFIG.orderStatus.completed"
										class="fol-col" style="text-align: right;color: #999999;">
										已使用
									</view> -->
								</view>
							</view>
						</template>
					</view>
					<image mode="widthFix" src="../../static/detail/bottom-bottom.png" style="display: block;"></image>
				</view>
			</view>
		</view>
	</view>
</template>
<script setup>
	import { ref,reactive,onMounted,computed} from 'vue'
	import Head from "./head.vue"
	import {useCommonStore} from '../../stores/common.js'
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import {formatName} from '@/common/myUtil.js'
	const commonStore = useCommonStore();
	import dayjs from "dayjs";
	
	const orderData = computed(()=>{
		return commonStore.curOrder
	})
	
	const title = computed(()=>{
		if(commonStore.curOrder.orderCategory==SPUP_GLOBAL_CONFIG.orderType.person||commonStore.curOrder.orderCategory==SPUP_GLOBAL_CONFIG.orderType.team){
			return SPUP_GLOBAL_CONFIG.permanentExhibition.exhibitionTitle;
		}else if(commonStore.curOrder.exhibition){
			return commonStore.curOrder.exhibition.exhibitionTitle;
		}
		return ""
	})
	
	const showQrcode = (subOrder)=> {
		console.log("subOrder:", orderData, subOrder);
		console.log('batchNo:', subOrder.batchNo )
		uni.navigateTo({
			url: '/subpages/order/ticketQrcode?category='+ orderData.value.orderCategory + '&orderNo=' + subOrder.orderNo+'&suborderNo='+subOrder.suborderNo+'&batchNo='+subOrder.batchNo
		});
	}
	const gotoBack = ()=>{
		uni.navigateBack({
		    delta: 1
		});
	}
</script>
<style>
	page {
		background: ##F7F9FA;
	}
</style>
<style scoped>
	@import "./orderDetail.css";
	@import "../../pages/common/common.css";
</style>