<template>
	<view class="tips-box">
		<view class="tips-icon">
			<image mode="widthFix" :src="data.errorCode?'../../static/tips/tips-fail.png':'../../static/tips/tips-success.png'"></image>
		</view>
		<text class="size-medium">{{data.message}}</text>
		<view class="tips-span">
			<text>您可在“个人中心-我的预约”中查看</text>
		</view>


		<!-- 按钮 -->
		<view class="btn-box fol-row">
			<view class="fol-col">
				<button class="btn-n btn-box-width" @click="buyAgainClick">再次预约</button>
			</view>
			<view class="fol-col">
				<button class="btn-s btn-box-width" @click="seeOrderListClick">查看预约</button>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {ref,reactive,onMounted,computed,watch} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	
	const data = reactive({
		errorCode:0,
		message:"预约成功"
	})
	
	const buyAgainClick = ()=>{
		uni.redirectTo({
			url: "/pages/tmpExhibition/tmpExhibition_list"
		})
	}
	const seeOrderListClick = ()=>{
		uni.redirectTo({
			url: "/pages/order/orderList"
		})
	}
	
	onLoad(options => {
		if(options.message){
			data.errorCode = 1
			data.message = options.message
		}
	})
</script>
<style scoped>
	@import "../../pages/common/common.css";
	@import "buyOk.css";
</style>
<style>
	page{
		background-color: #ffffff;
		font-weight: 400;
	}
</style>
