<template>
	<view style="border-bottom: 0.8px solid #000;">
		<view class="add-title-hr"><text>{{costomer.id?'修改':'新增'}}预约人</text></view>
	</view>
	
	<view class="contacts-box">
		<view class="uni-form-item uni-column fol-row fol-row_box">
			<text class="uni-form-item__title"><text class="bt-color">*</text>姓名</text>
			<view class="uni-input-wrapper">
				<input class="uni-input" type="text" v-model="costomer.name" placeholder="请输入姓名" />
			</view>
		</view>
		<view class="uni-form-item uni-column fol-row fol-row_box">
			<text class="uni-form-item__title"><text class="bt-color">*</text>手机号码</text>
			<view class="uni-input-wrapper">
				<input class="uni-input" type="number" v-model="costomer.phone" maxlength="11" placeholder="请输入手机号码" />
			</view>
		</view>
		<view class="uni-form-item uni-column fol-row fol-row_box">
			<text class="uni-form-item__title">证件类型</text>
			<view class="uni-input-wrapper">
				<picker mode="selector" @change="bindPickerChange" :value="idTypeIndex" :range="SPUP_GLOBAL_CONFIG.idType0" range-key="name">
					<view class="uni-input fol-row input-box">
						{{SPUP_GLOBAL_CONFIG.idType0[idTypeIndex].name}}
					</view>
				</picker>
			</view>
			<view style="text-align: right;padding-right:10px;">
				<uni-icons type="right" size="14"></uni-icons>
			</view>
		</view>
		<view class="uni-form-item uni-column fol-row fol-row_box">
			<text class="uni-form-item__title"><text class="bt-color">*</text>证件号码</text>
			<view class="uni-input-wrapper">
				<input class="uni-input" type="idcard" v-model="costomer.idcardNo" placeholder="请输入证件号码" />
			</view>
		</view>
		<view class="uni-form-item uni-column fol-row fol-row_box">
			<text class="uni-form-item__title">备注</text>
			<view class="uni-input-wrapper">
				<input class="uni-input" type="text" v-model="costomer.remark" placeholder="请填写单位名称" />
			</view>
		</view>
	</view>
	<!-- 按钮 -->
	<view class="btn-box fol-row">
		<view class="fol-col" ></view>
		<view v-if="costomer.id" class="btn-right-site complete">
			<button class="btn-n" @click="deleteClick">删除</button>
		</view>
		<view class="btn-right-site complete">
			<button class="btn-s" @click="okClick">提交</button>
		</view>
	</view>
	<!-- 按钮 -->
	<!-- <view class="btn-box">
		<button class="btn-s" @click="okClick">确认添加</button>
	</view> -->
</template>

<script setup>
	import {ref,reactive,onMounted,computed} from 'vue'
	import {onShow,onHide} from '@dcloudio/uni-app';
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import {addContacts, modifyContacts, deleteContacts} from "@/common/api/server.js";
	import {backOrGotoPage} from '@/common/myUtil.js'
	
	const idTypeIndex = ref(0) 
	const bindPickerChange = (e)=>{
		idTypeIndex.value = e.detail.value;
		costomer.idcardCategory = SPUP_GLOBAL_CONFIG.idType0[idTypeIndex.value].id
	}
	const costomer =  reactive({
		id: 0,
		name:"",
		phone: "",
		idcardCategory: 1,
		idcardNo : "",
		remark:""
	})
	
	const deleteClick = ()=>{
		uni.showModal({
			title: '删除预约人',
			content: '确定要删除'+costomer.name+'吗？',
			confirmText: "确认删除",
			cancelText:"再想一想",
			success: function(res) {
				if(res.confirm){
					deleteContacts(costomer.id).then((res)=>{
						console.log("deleteContacts", res);
						if(res.code==0){
							for(let i=commonStore.customers.length-1; i>=0; i--){
								if(commonStore.customers[i].id==costomer.id){
									commonStore.customers.splice(i, 1)
									break
								}
							}
							// uni.showToast({icon: "success",title:  '删除成功'})
							backOrGotoPage('删除成功', '/subpages/buy/customerList?maxCount=5&orderType=1');
							// uni.navigateBack({
							// 	delta: 1
							// });
						}
					})
				}
			}
		})
	}
	
	const okClick = ()=>{
		if(!costomer.name){
			uni.showToast({icon: "none",title:  '请输入姓名'})
			return;
		}
		if(!/^[1][0-9]{10}$/.test(costomer.phone)){
			uni.showToast({icon: "none",title:  '请输入正确的手机号码'})
			return;
		}
		if(!costomer.idcardNo){
			uni.showToast({icon: "none",title:  '请输入证件号码'})
			return;
		}
		if(costomer.id){	//修改联系人
			modifyContacts(costomer.id, costomer.name, costomer.phone, costomer.idcardCategory, costomer.idcardNo, costomer.remark).then((res)=>{
				if(res.code==0){
					for(let i=commonStore.customers.length-1; i>=0; i--){
						if(commonStore.customers[i].id==costomer.id){
							commonStore.customers[i] = {...costomer, value:costomer.id}
							break
						}
					}
					uni.showToast({icon: "success",title:  '修改成功'})
					uni.navigateBack({
						delta: 1
					});
				}
			})
		}else{		//新增联系人
			addContacts(costomer.name, costomer.phone, costomer.idcardCategory, costomer.idcardNo, costomer.remark).then((res)=>{
				if(res.code==0){
					costomer.id = res.data.id
					commonStore.customers.unshift({...costomer, value:costomer.id})
					uni.showToast({icon: "success",title:  '添加成功'})
					uni.navigateBack({
						delta: 1
					});
				}
			})
		}
	}
	
	onLoad(options => {
		console.log("options:", options);
		if(options.id){
			costomer.id = options.id
			let curCostomer = commonStore.customers.find(item=>item.id==costomer.id)
			costomer.name = curCostomer.name
			costomer.phone = curCostomer.phone
			costomer.idcardCategory = curCostomer.idcardCategory
			for(let i in SPUP_GLOBAL_CONFIG.idType0){
				if(SPUP_GLOBAL_CONFIG.idType0[i].id==costomer.idcardCategory){
					idTypeIndex.value = i;
				}
			}
			costomer.idcardNo = curCostomer.idcardNo
			costomer.remark = curCostomer.remark
		}
	})
</script>

<style scoped>
	@import "./customerEdit.css";
	@import "../../pages/common/common.css";
	
</style>
<style>
	page{
		background: #f7f9fa;
	}
</style>
