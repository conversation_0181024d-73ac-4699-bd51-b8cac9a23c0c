uni-button:after {
	    content:none;
	}

	/* 信息 */
	.tips-box {
		text-align: center;
		margin-top: 60px;
	}

	.tips-icon {
		width:132px ;
		margin: auto;
		padding-bottom: 10px;
	}

	.tips-span {
	font-size: 14px;
	margin-top: 10px;
	color: #999999;
	}

	/* 按钮 */
	.btn-box {
		margin: auto;
		width: 100%;
		margin-top: 30px;
	}
    .btn-box-width{
		width: 130px;
		font-size: 13px;
		border-radius: 4px;
	}
	.btn-s {
		
		margin-left: 15px;
		color: #000;
		background: #fff;
		border: 1px solid #000;
	}
	.btn-n{
		color: #000;
		margin-right: 15px;
		background-color: #f2f2f2;
		border: 1px solid #cccccc;
	}

	uni-button:after {
		content: none;
		width: 200%;
		height: 200%;
		position: absolute;
		top: 0;
		left: 0;
		border: 1px solid rgba(0, 0, 0, 0.2);
		transform: scale(0.5);
		transform-origin: 0 0;
		box-sizing: border-box;
		border-radius: 10px;
	}