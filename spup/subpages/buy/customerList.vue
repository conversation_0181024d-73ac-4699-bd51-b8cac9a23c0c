<template>
	<view class="complete">
		<view class="fol-row" >
			<view class="fol-col add-title" style="padding-bottom: 14px;">
				添加预约人
			</view>
			<view>
				<view class="btn-right-site fol-row"  @click="addClick" v-if="(
				data.orderType!=SPUP_GLOBAL_CONFIG.orderType.exhibition
				&&data.orderType!=SPUP_GLOBAL_CONFIG.orderType.fypdTmpBatch)">
					<view style="width: 16px;height: 38px;padding-top: 3px;">
						<image mode="widthFix" src="../../static/btn-add.png"></image>
					</view>
					<text class="btn-top-s" >新增</text>
				</view>
			</view>
		</view>
		<view class="hr-box">
			<view class="hr-twill"></view>
			<view class="hr-edge-weak"></view>
		</view>
		<!-- <view class="list-box">
			<button class="new-list" @click="addClick">  添加 </button>
		</view> -->
		<view>
			<checkbox-group  @change="checkboxChange">
				<label class="uni-list-cell uni-list-cell-pd" v-for="item in commonStore.customers" :key="item.value" style="border-bottom: 1px solid #000;padding: 10px 10px 10px 0">
					<view style="padding-top: 12px;">
						<checkbox :value="item.value" :checked="item.checked" color="#815462"  style="transform:scale(0.7);padding-right: 10px;" />
					</view>
					<view class="fol-col">{{formatName(item.name)}}
					  <view class="size-span">{{item.phone.substr(0,3)}}****{{item.phone.substr(7,11)}}</view>
					</view>
					<view v-if="(data.orderType!=SPUP_GLOBAL_CONFIG.orderType.exhibition&&data.orderType!=SPUP_GLOBAL_CONFIG.orderType.fypdTmpBatch)" @click.stop="editClick(item.id)" style="margin: 10px 0 0 0;">
						<uni-icons  type="compose" size="20" ></uni-icons>
					</view>
					
				<!-- 	<view @click="goto('../ticket/ticket_contacts-edit')" class="fa fa-pencil"
						style="font-size: 18px;color:#999;padding-top: 12px;"></view> -->
				</label>
				
			</checkbox-group>
		</view>
		<view v-if="commonStore.customers.length==0" style="color: #999;">
			暂无预约人
		</view>
	</view>
	<!-- 按钮 -->
	<view class="btn-box fol-row">
		<view class="fol-col" ></view>
		<view class="btn-right-site">
			<button class="btn-s" @click="okClick">确定</button>
		</view>
	</view>
	
</template>
<script setup>
	import {ref,reactive,onMounted,computed} from 'vue'
	import {onShow,onHide} from '@dcloudio/uni-app';
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import {useCommonStore} from '@/stores/common.js'
	import {formatName} from '@/common/myUtil.js'
	const commonStore = useCommonStore();
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import {getContacts, getAvaildContacts} from "@/common/api/server.js";
	
	// Define props
	const props = defineProps({
	    maxCount: {
	        type: Number,
	        default: 1
	    },
	    orderType: {
	        type: Number,
	        default: 1
	    }
	})
	const data = reactive({
	    orderType: props.orderType,
	    maxCount: props.maxCount
	})
	// const data = reactive({
	// 	orderType:1,
	// 	maxCount:1,	//最多可选择几个人
	// })
	
	const checkboxChange = (e) => {
		console.log(e);
		let values = e.detail.value
		commonStore.customers.forEach(item=>{
			if(values.find(value=>value==item.id)){
				item.checked = 1
			}else{
				item.checked = 0
			}
		})
		console.log(commonStore.customers);
	}
		
	const addClick = ()=> {
		uni.navigateTo({
			url: "/subpages/buy/customerEdit"
		})
	}
	const editClick = (id)=> {
		console.log("editClick:", id)
		uni.navigateTo({
			url: "/subpages/buy/customerEdit?id="+id
		})
	}
	
	const okClick =()=>{
		if(commonStore.customers.filter(item=>item.checked).length>data.maxCount){
			uni.showToast({icon: "none", title:'每次最多选择'+data.maxCount+'人'})
		}else{
			uni.navigateBack({
				delta: 1
			});
		}
	}
	
	// onShow(()=>{
	// 	getContacts().then((res) => {
	// 		console.log("取得联系人：", res.data);
	// 		res.data.forEach(item=>{
	// 			let sameItem = commonStore.customers.find(oldItem=>oldItem.id===item.id)
	// 			item.checked = (sameItem && sameItem.checked) ? 1: 0
	// 			item.value = item.id
	// 		})
	// 		commonStore.customers = res.data
	// 	})
	// })
	
	onLoad(options => {
		const maxCount = parseInt(options.maxCount) || props.maxCount
		const orderType = parseInt(options.orderType) || props.orderType
		console.log("option order type ---------->" + options.orderType)
		console.log("option max count ----------->" + options.maxCount)
		console.log("data order type ----------> " + data.orderType)
		let parseData = (res)=>{
			console.log("取得联系人：", res.data);
			res.data.forEach(item=>{
				let sameItem = commonStore.customers.find(oldItem=>oldItem.id===item.id)
				item.checked = (sameItem && sameItem.checked) ? 1: 0
				item.value = item.id
			})
			commonStore.customers = res.data
		}
		if(data.orderType==SPUP_GLOBAL_CONFIG.orderType.exhibition||data.orderType==SPUP_GLOBAL_CONFIG.orderType.fypdTmpBatch){
			getAvaildContacts().then(parseData)
		}else{
			getContacts().then(parseData)
		}
	})
</script>

<style scoped> 
    @import "../../pages/common/common.css";
	@import "./customerList.css";
</style>

<style >
	page{
		background-color: #f7f9fa;
	}
	.uni-list-cell {
		justify-content: flex-start;
		display: flex;
	}

	.label-3 {
		border-bottom: 1px solid #f0f0f0;
		padding: 10px 15px;
	}
	::v-deep uni-checkbox:not([disabled]) .uni-checkbox-input:hover {
	    border-color: var(--button-color);
	}
</style>
