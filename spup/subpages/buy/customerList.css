/*  */
.hr-box{
		position: relative;
		padding: 0 0 16px 0;
	}
	.hr-twill{
		position: absolute;
		border: 0;
		width: 54px;
		height: 6px;
		margin-top: -2px;
		margin-left: 18px;
		background: linear-gradient(-45deg, #000 25%, #F7F9FA 0, #F7F9FA 50%, #000 0, #000 75%, #F7F9FA 0);
		
		background-size: 8px 8px;
	}
	.hr-edge-weak{
		border: 0;
		width: 100%;
		height: 1px;
		background: #000;
	}
/*  */
	.txt-color {
		color: #26111a;
	}
	.box{
		padding: 0 15px;
	}
	
	.size-mini {
		font-size: 12px;
	}
	
	.size-regular {
		font-size: 14px;
	}
	
	.size-medium {
		font-size: 16px;
	}
	.size-big {
		font-size: 18px;
	}
	
	.size-span {
		font-size: 12px;
		color: #999999;
	}
	.size-span2 {
		font-size: 14px;
		color: #999999;
	}
	.size-txt{
		overflow-wrap: break-word;
		white-space: nowrap;
	}
	uni-button:after {
	    content:none;
	}
	
	.list-box {
		margin: 20px 15px;
	}

	.new-list {
		margin: 10px 0;
		background-color: #fff;
		font-size: 14px;
		border-radius: 2px;
		padding: 1px 0;
		color: #80616e;
		box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
	}
	.complete {
		padding: 10px 15px;
		padding-bottom: 100px;
		overflow: hidden;
	}
	/* 按钮 */
	.btn-box {
		width: 100%;
		border-top: 1px solid #000;
		display: flex;
		position: fixed;
		padding: 15px 0;
		/* #ifdef H5 */
		left: var(--window-left);
		right: var(--window-right);
		/* #endif */
		bottom: 0;
		background: #f7f9fa;
		font-weight: 400;
	}
	
	.btn-right-site {
		text-align: right;
	}
	
	.btn-s {
		width: 94px;
		margin-bottom: 10px;
		margin-right: 15px;
		/* padding: 3px 0; */
		font-size: 14px;
		color: #fff;
		background: var(--button-color);
		border-radius: var(--button-small-radius);
	}
	.add-title{
		font-size: 19px;
	}
	.btn-top-s{
		text-align: center;
		padding-left: 5px;
		font-size: 14px;
	}
	.hr-bottom{
		border-bottom: 1px solid #000;
	}
	.hr-top{
		border: 0;
		width: 65px;
		height:5px;
		background: #000;
	}