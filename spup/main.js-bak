// // 动态添加viewport meta标签
// const addViewportMeta = () => {
//   const coverSupport = 'CSS' in window && 
//     typeof CSS.supports === 'function' && 
//     (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'));
  
//   const viewportContent = `width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0${
//     coverSupport ? ', viewport-fit=cover' : ''
//   }`;
  
//   // 查找并更新现有的viewport meta标签
//   const existingViewportMeta = document.querySelector('meta[name="viewport"]');
//   if (existingViewportMeta) {
//     existingViewportMeta.setAttribute('content', viewportContent);
//   } else {
//     // 如果没有找到，则创建新的meta标签
//     const meta = document.createElement('meta');
//     meta.name = 'viewport';
//     meta.content = viewportContent;
//     document.head.appendChild(meta);
//   }
// };

// // 页面加载后执行
// if (document.readyState === 'loading') {
//   document.addEventListener('DOMContentLoaded', () => {
//     addViewportMeta();
//     initVueApp(); // 页面加载完成后初始化Vue应用
//   });
// } else {
//   addViewportMeta();
//   initVueApp(); // 如果页面已经加载完成，直接初始化Vue应用
// }

// // 初始化Vue应用
// function initVueApp() {
//   // #ifndef VUE3
//   import('vue').then(Vue => {
//     import('./App').then(App => {
//       // 设置生产环境提示
//       Vue.default.config.productionTip = false;

//       // 定义应用类型
//       App.default.mpType = 'app';

//       // 创建Vue2应用实例
//       try {
//         const app = new Vue.default({
//           ...App.default
//         });
//         app.$mount('#app'); // 指定挂载点
//       } catch (error) {
//         console.error('Vue2应用初始化失败:', error);
//       }
//     });
//   });
//   // #endif

//   // #ifdef VUE3
//   import('vue').then(({ createSSRApp }) => {
//     import('./App.vue').then(App => {
//       import('pinia').then(({ createPinia }) => {
//         import('pinia-plugin-persist-uni').then(piniaPersist => {
//           try {
//             // 创建SSR应用实例
//             const app = createSSRApp(App.default);
            
//             // 配置pinia状态管理并启用持久化
//             const pinia = createPinia();
//             pinia.use(piniaPersist.default);
            
//             // 注册pinia插件
//             app.use(pinia);
            
//             // 挂载应用
//             app.mount('#app');
            
//             console.log('Vue3应用初始化成功');
//           } catch (error) {
//             console.error('Vue3应用初始化失败:', error);
//             throw error;
//           }
//         });
//       });
//     });
//   });
//   // #endif
// }



// #ifndef VUE3
import Vue from 'vue'
import App from './App'

Vue.config.productionTip = false

App.mpType = 'app'

const app = new Vue({
    ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import App from './App.vue'
import { createPinia } from 'pinia'
import piniaPersist from 'pinia-plugin-persist-uni'

export function createApp() {
	try {
	  const app = createSSRApp(App)
	  //pinia持久化，参考：https://juejin.cn/post/7081275565008748552/				
	  // https://blog.csdn.net/weixin_44728473/article/details/125616404
	  const pinia = createPinia()
	  pinia.use(piniaPersist)	
	  app.use(pinia)
	  return {
		app
	  }
	} catch (error) {
		console.error('Vue3应用初始化失败:', error)
		throw error // 向上抛出错误以便上层处理
	  }

}
// #endif