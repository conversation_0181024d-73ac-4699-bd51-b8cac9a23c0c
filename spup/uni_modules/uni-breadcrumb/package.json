{"id": "uni-breadcrumb", "displayName": "uni-breadcrumb 面包屑", "version": "0.1.2", "description": "Breadcrumb  面包屑", "keywords": ["uni-breadcrumb", "breadcrumb", "uni-ui", "面包屑导航", "面包屑"], "repository": "", "engines": {"HBuilderX": "^3.1.0"}, "directories": {"example": "../../temps/example_temps"}, "dcloudext": {"category": ["前端组件", "通用组件"], "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"Vue": {"vue2": "y", "vue3": "y"}, "App": {"app-vue": "y", "app-nvue": "n"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "u", "百度": "u", "字节跳动": "u", "QQ": "u", "京东": "u"}, "快应用": {"华为": "u", "联盟": "u"}}}}}