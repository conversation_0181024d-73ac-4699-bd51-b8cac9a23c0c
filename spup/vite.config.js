import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue({
    template: {
      compilerOptions: {
        isCustomElement: (tag) => tag.startsWith('uni-') || tag.includes('swiper')
      }
    }
  })],
  resolve: {
    alias: {
      '@': resolve(__dirname, '.'),
      // 配置Vue别名以使用包含模板编译器的完整版本
      'vue': 'vue/dist/vue.esm-bundler.js'
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        // 抑制SCSS弃用警告
        quietDeps: true,
        silenceDeprecations: ['import', 'global-builtin', 'color-functions']
      }
    }
  },
  server: {
    port: 8080,
    host: true,
    open: true
  },
  build: {
    outDir: 'dist',
    assetsDir: 'static'
  },
  define: {
    // 定义全局变量以支持UniApp的条件编译
    'process.env.VUE_APP_PLATFORM': JSON.stringify('h5'),
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
    // 启用Vue的选项API和模板编译器
    '__VUE_OPTIONS_API__': true,
    '__VUE_PROD_DEVTOOLS__': false,
    // 修复 crypto 兼容性
    global: 'globalThis'
  },
  optimizeDeps: {
    exclude: ['@vitejs/plugin-vue']
  }
})
