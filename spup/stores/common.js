import {defineStore} from 'pinia'
import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'

export const useCommonStore = defineStore({
	id: 'pudongMiniCommon',
	state: () => ({
		lifeMsg:"",
		isMpLoginRedirect: false, //是否是微信公众号登录的回调
		urlBeforeMpLogin: "", 	  //公众号登录前用户打开的URL，主要是为了登录成功后恢复现场，跳转到原来的URL
		curShopServiceId: 'SH0001',
		curShop:{},
		userInfo:{
			isNew:false,
			unionid: SPUP_GLOBAL_CONFIG.env=="dev"?"TEST":"",
			"wechatAvatarSrc":"https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0",
			"wechatNickname":"",
			"wechatPhone":"",
			"userName":"",
		},
		buyData:{
			time:{}
		},
		curOrder:{},
		customers:[
			// {'idcardCategory': 1, 'idcardNo': '310122198610225858', 'name': '张三', 'phone': '18502168168', checked:1},
			// {'idcardCategory': 1, 'idcardNo': '310122200301016666', 'name': '王五六', 'phone': '18502888888', checked:1}
		],
		tmpBatch:{
			category:32,	//临时场次类型
			menuCode:[],//用户权限
		},
		curActiveRound:{
			
		}
		
	}),
	getters: {

	},
	actions: {

	},
	// 开启数据缓存
	persist: {
		enabled: true,
		strategies: [
			{
			  key: 'spup_web',
			  storage: localStorage,
			}
	    ]
    }
	
})
