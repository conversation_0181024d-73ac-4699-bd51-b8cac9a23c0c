{
    "name" : "浦东新区城市规划和公共艺术中心",
    "appid" : "__UNI__9DF9860",
    "description" : "",
    "versionName" : "1.0.0",
    "versionCode" : 104,
    "transformPx" : false,
    "networkTimeout" : {
        "request" : 60000,
        "connectSocket" : 60000,
        "uploadFile" : 60000,
        "downloadFile" : 60000
    },
    "app-plus" : {
        /* 5+App特有相关 */
        "usingComponents" : true,
        "nvueCompiler" : "uni-app",
        "nvueStyleCompiler" : "uni-app",
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        "modules" : {},
        /* 模块配置 */
        "distribute" : {
            /* 应用发布信息 */
            "android" : {
                /* android打包配置 */
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ]
            },
            "ios" : {},
            /* ios打包配置 */
            "sdkConfigs" : {}
        },
        "error" : {
            "url" : ""
        }
    },
    /* SDK配置 */
    "quickapp" : {},
    /* 快应用特有相关 */
    "mp-weixin" : {
        "appid" : "wx85365381338b9a5e",
        "setting" : {
            "urlCheck" : false,
            "es6" : true
        },
        "usingComponents" : true
    },
    "vueVersion" : "3",
    "h5" : {
        "publicPath" : "./",
        "filenameHashing" : true,
        "router" : {
            "mode" : "hash",
            "base" : "./"
        },
        "devServer" : {
            "port" : ""
        },
        "optimization" : {
            "treeShaking" : {
                "enable" : true
            }
        }
    },
    "locale" : "zh-Hans"
}
