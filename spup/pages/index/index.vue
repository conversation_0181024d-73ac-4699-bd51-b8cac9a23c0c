<template>
	<view class="bgImg-wrap">
		<image class="bgImg" mode="widthFix" src="/static/index/start_logo.jpg"></image>
		<!-- <image class="bgImg" mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + '/logo/start_logo.jpg'"></image> -->
	</view>
	<uni-popup ref="popup" background-color="#fff" @change="change" :mask-click="false">
		<view class="popup-content">
			<view class="">
				<view class="flex-row justify-between">
					<view class="popup-title">参观须知</view>
				</view>
				<view class="content">
					<rich-text style="word-break: break-all;" :nodes="detail">
					</rich-text>
						<text>{{note}}</text>
				</view>
				<!-- 按钮 -->
				<view class="btns">
					<!-- <button class="btn-n" type="default" @click="rejectClick">不同意</button> -->
					<navigator style="margin: 0 5%;font-size: 16px;" class="btn-n" open-type="exit" target="miniProgram">不同意</navigator>
					<button :class="countdown>0?'btn-n':'btn-s'" type="default"
						@click="agreeClick">同意{{countdownLabel}}</button>
					<!-- <button class="btn-s" :type="default" @click="agreeClick">同意{{countdownLabel}}</button> -->
				</view>
			</view>
		</view>
	</uni-popup>
</template>

<script setup>
	import {ref,onMounted,computed} from 'vue'
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {login} from "@/common/api/server.js";
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	//倒计时
	const countdown = ref(3)
	const countdownLabel = computed(() => {
		return countdown.value <= 0 ? '' : '(' + countdown.value + 's)'
	});
	
	const note = ref("1、观众须在入口处完成体温检测、上海市（健康宝）健康码检查、携带本人二代身份证验证预约信息，方可进入馆区。如存在体温≧37.3℃、上海市（健康宝）健康码显示异常、未预约三种情况中任一种情况的观众，暂不得进入馆区。\n2、观众入馆参观须全程正确佩戴口罩，文明有序观展，未戴口罩将谢绝参观。若您参观时出现身体不适或其他突发情况，请及时联系现场工作人员。工作人员将帮助您前往“隔离区”进行休息隔离。幼、老年等特殊游客请由亲友陪同参观，并注意安全。\n3、对衣着不整、言行不规者，谢绝参观。\n4、管制械具、打火机等易燃易爆品以及宠物不允许带入场馆。\n5、游客参观时，应听从工作人员的管理，请勿大声喧哗和入拦触摸展品。\n6、爱护馆区的各类设施和花草树木。\n7、讲究卫生，保持环镜清洁，请勿将食品、饮料等带入馆内食用，严禁吸烟。\n8、互动项目（飞阅浦东），在成功预约展馆参观后，核验进场后扫描场馆内动态二维码，根据当日可选时段进行预约，选择项目体验场次。\n9、服务台免费为游客提供寄存物品、咨询服务，并热忱接受游客的建议和监督（热线电话：021- 58856900）。如遇重大接待或不可抗力因素造成的特殊情况，请遵从本馆工作人员的统一协调安排，已预约的个人和团队将另行安排参观时间。\n10、在参观日前7天（含当日）上午9:30开放预约服务，场馆及馆内各展项将根据防疫办要求，动态调整限定参观人数。");
	
	const popup = ref();
	const rejectClick = () => {
		popup.value.close()
	}
	const agreeClick = () => {
		if(countdown.value<=0){
			uni.redirectTo({
				url: '/pages/home/<USER>'
			})
		}
	}
	
	
	onMounted(() => {
		
		
		// popup.value.open("bottom");
		// let timer = setInterval(() => {
		// 	if (countdown.value <= 0) {
		// 		clearInterval(timer)
		// 	} else {
		// 		countdown.value--
		// 	}
		// }, 1000)
		
		login().then((res)=>{
			if (commonStore.userInfo.isNew || true) {
				popup.value.open("bottom");
				let timer = setInterval(() => {
					if (countdown.value <= 0) {
						clearInterval(timer)
					} else {
						countdown.value--
					}
				}, 1000)
			} else {
				countdown.value = 0;
				agreeClick()
			}
		});
		
		
		// uni.navigateTo({
		// 	url: '/pages/home/<USER>'
		// })
	})
</script>

<style>
	page{
		background-color: #f7f9fa;
	}
</style>
<style lang="scss" scoped>
	@import './index.scss';
	@import '../common/common.css';

	
</style>
