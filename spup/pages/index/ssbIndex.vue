<template>
	<!-- <view>我的随申办应用首页</view>
	<view><text>{{commonStore.lifeMsg}}</text></view> -->
</template>

<script setup>
	import { ref,reactive,onMounted,computed} from 'vue'
	import {onLoad} from '@dcloudio/uni-app'
	import {useCommonStore} from '../../stores/common.js'
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	const commonStore = useCommonStore();
	import dayjs from "dayjs";
	import {login} from "@/common/api/server.js";
	
	onLoad(options=>{
		login()
		// commonStore.lifeMsg +=  "【"+dayjs().format("hh:mm:ss")+"- ssbIndex onLoad】\n"
		// let YOUR_CLIENT_ID = SPUP_GLOBAL_CONFIG.ssbAppId
		// let YOUR_REGISTERED_REDIRECT_URI = SPUP_GLOBAL_CONFIG.baseUrl + "/spup-mp/#" + SPUP_GLOBAL_CONFIG.ssbLoginRedirectUrl
		// let timestamp = dayjs().valueOf()
		// let url = "http://api.eshimin.com/api/oauth/authorize?client_id="+YOUR_CLIENT_ID
		// 			+"&response_type=code&redirect_uri="+encodeURIComponent(YOUR_REGISTERED_REDIRECT_URI)
		// 			+"&scope=read&timestamp="+timestamp
		// commonStore.lifeMsg +=  "【"+dayjs().format("hh:mm:ss")+"- ssbIndex redirect url: "+encodeURIComponent(YOUR_REGISTERED_REDIRECT_URI)+"】\n"
		// window.location.href = url
	})
	
</script>

<style>
</style>