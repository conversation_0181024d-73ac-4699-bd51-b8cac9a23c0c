<template>
	<!-- <view>随申办登录</view> -->
	<!-- <view class="msg-wrap"><text class="msg">{{commonStore.lifeMsg}}</text></view> -->
</template>

<script setup>
	import { ref,reactive,onMounted,computed} from 'vue'
	import {onLoad} from '@dcloudio/uni-app'
	import {useCommonStore} from '../../stores/common.js'
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	const commonStore = useCommonStore();
	import dayjs from "dayjs";
	import {ssbLoginByCode} from "@/common/api/server.js";
	
	const data = reactive({
	})
	onLoad(options=>{
		commonStore.lifeMsg +=  "【"+dayjs().format("hh:mm:ss")+"ssbLogin options:"+ JSON.stringify(options)+"】\n"
		if(options.code){
			uni.showLoading({
				title: "正在登录"  
			})
			ssbLoginByCode(options.code).then(res=>{
				uni.hideLoading()
				commonStore.lifeMsg +=  "【"+dayjs().format("hh:mm:ss")+"ssbLogin ssbLoginByCode:"+ JSON.stringify(res)+"】\n"
				commonStore.lifeMsg +=  "【"+dayjs().format("hh:mm:ss")+"ssbLogin userInfo:"+ JSON.stringify(commonStore.userInfo)+"】\n"
				
				uni.navigateTo({
					url: "/"
				})
			}).catch(err=>{
				uni.showModal({
					confirmText: "重新登录",
					title:err.title ? err.title : "",
					content: err.message ? err.message: '随申办登录失败',
					showCancel: false,
					complete:()=>{
						uni.reLaunch({
							url: "/pages/index/ssbIndex"
						})
					}
				});
			});
		}else{
			uni.showModal({
				confirmText: "重新登录",
				title:"登录失败",
				content: '参数异常',
				showCancel: false,
				complete:()=>{
					uni.reLaunch({
						url: "/pages/index/ssbIndex"
					})
				}
			});
		}
	})
	
</script>

<style>
	.msg-wrap{
		overflow: auto;
	}
	.msg{
		width:80%;
		display:inline-block;
		white-space: pre-wrap; 
		word-wrap: break-word;
		height: auto;
	}
</style>