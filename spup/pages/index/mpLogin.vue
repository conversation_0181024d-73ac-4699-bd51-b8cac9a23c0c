<template>
	<view class="bgImg-wrap">
		<image class="bgImg" mode="widthFix" src="/static/index/start_logo.jpg"></image>
		<!-- <image class="bgImg" mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + '/logo/start_logo.jpg'"></image> -->
	</view>
</template>

<script setup>
	import {ref,onMounted,computed} from 'vue'
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {mpLoginByCode} from "@/common/api/server.js";
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	
	onLoad(options => {
		function getQueryString(name) {
		    let reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
		    let r = window.location.search.substr(1).match(reg);
		    if (r != null) {
		        return unescape(r[2]);
		    }
		    return null;
		}
		let code = getQueryString("code");
		let state = getQueryString("state");
		if(code){
			uni.showLoading({
				title: "正在登录"  
			})
			// mpLoginByCode(code, commonStore.urlBeforeMpLogin.indexOf("/pages/my/my_index")!=-1).then((res)=>{
			mpLoginByCode(code, true).then((res)=>{
				uni.hideLoading()
				console.log("公众号登录res:", res);
				// uni.redirectTo({
				// 	url: commonStore.urlBeforeMpLogin
				// });
				location.href = commonStore.urlBeforeMpLogin
			}).catch(err=>{
				uni.showModal({
					confirmText: "重新登录",
					title:err.title ? err.title : "",
					content: err.message ? err.message: '登录失败',
					showCancel: false,
					complete:()=>{
						uni.reLaunch({
							url: "/"
						})
					}
				});
			});
			
		}else{
			uni.showToast({
				icon: "error",
				title: "参数异常！" 
			})
		}
		console.log("code:", code);
		console.log("state:", state);
		console.log("options:", options,  window.location.href);	//接收页面传来的参数
		
	});
</script>

<style>
	page{
		background-color: #fff;
	}
</style>
<style lang="scss" scoped>
	@import './index.scss';
	@import '../common/common.css';

	
</style>
