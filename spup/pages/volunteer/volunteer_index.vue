<template>
	<view>
		<view class="subTitle">志愿者招募</view>
		<image mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + '/volunteer/vo-title.png'"></image>
	</view>
	<view class="container size-normal">
		<view class="info-box top20 text-indent">
			<view>上海市浦东新区城市规划和公共艺术中心（PUDONG UPPAC，以下简称“规划馆”）坐落于浦东锦绣文化公园板块内，与浦东图书馆、浦东新区青少年活动中心及群艺馆相邻。</view>
			<view>
				规划馆定位打造集城市规划和公共艺术展示、规划评审、发布及研讨、教育及知识普及、会议举办、文化创意、旅游等一体的城市公共活动空间。同时将致力于成为“浦东城市会客厅”和市民公共艺术文化中心、美学教育基地，成为浦东复合型文化地标。
			</view>
		</view>
		<!-- 报名条件/招募岗位 -->
		<view class="info-box top15">
			<view class="subTitle">报名条件</view>
			<view>1.热心公益，品行端正，身心健康，自愿义务为公众服务。<br>
				2.需参加本馆组织的面试、培训与考核，考核合格后成为正式志愿者。<br>
				3.遵守国家法律法规和上海市浦东新区城市规划和公共艺术中心的各项规章制度。
			</view>
			<view class="top15">
				<view class="subTitle">招募岗位</view>
				<view class="top15">
					<view style="font-weight: 500;">一、场馆指引</view>
					<view>
						岗位描述：为游客提供场馆咨询、引导等服务。<br>
						岗位条件：<br>
						1.18至40周岁，热情耐心，有亲和力。<br>
						2.具有良好的表达能力、普通话标准、吐字清楚、善于沟通交流。<br>
						3.服务热情、耐心细致，有较高的礼仪修养。<br>
						4.应变反应能力强，遇到突发情况能沉着应对，妥善处理。
					</view>
					<view class="btn" @click="mycat()"> >>> 详情</view>
				</view>
				<view class="top15">
					<view style="font-weight: 500;">二、展览讲解</view>
					<view>
						岗位描述：定时定点为公众提供展览讲解服务。<br>
						岗位条件：<br>
						1.18至35周岁，热情耐心，有亲和力。<br>
						2.具有良好的表达能力、普通话标准、吐字清楚、善于沟通交流。<br>
						3.服务热情、耐心细致，有较高的礼仪修养。<br>
						4.应变反应能力强，遇到突发情况能沉着应对，妥善处理。<br>
						5.对国土空间规划和城市人文等知识有一定的了解，能熟悉并讲解展厅的展示内容。<br>
						6.善于总结及信息反馈收集，为展馆工作提出好的建议和意见。<br>
						7.对展馆展示教育有热情且愿提升此方面能力的。<br>
						8.有相关讲解经验者优先。
					</view>
					<view class="btn" @click="mycat()"> >>> 详情</view>
				</view>
			</view>
		</view>
		<!--  -->
		<view class="info-box top15 text-indent">
			<view>
				上海市浦东新区城市规划和公共艺术中心志愿服务队将作为宣传浦东规划和公共艺术的窗口之一，立足于传播城市公共艺术展览理念、宣传解读城市规划展览两大功能，带领社会公众接受家园教育、参与城乡规划等。
			</view>
			<view>快快加入我们吧！</view>
		</view>
	</view>
</template>

<script setup>
	import {ref,reactive,onMounted,computed} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
    const mycat = ()=> {
		let url = 'https://sh.zhiyuanyun.com/app/opp/view.php?id=nkhZnvmxXecYN'
		location.href = url
		// uni.navigateTo({
		// 	url: '/pages/volunteer/webview?url=' + encodeURIComponent(url) 

		// })
	}
</script>

<style>
	page {
		background: linear-gradient(55deg, #e2c2ce, #f6e4eb);
	}

	.container {
		background: linear-gradient(55deg, #e2c2ce, #f6e4eb);
		padding: 0 15px 36px 15px;
	}

	.subTitle {
		background: var(--button-color);
		width: fit-content;
		padding: 3px 25px;
		color: #fff;
		font-size: 13px;
		border: 1px solid #fff;
		border-radius: 25px;
		margin: 15px auto;
	}

	.info-box {
		background-color: rgba(255, 255, 255, 0.5);
		border-radius: 5px;
		padding: 10px 20px 20px 20px;
		box-shadow: inset 0px 0px 10px 0px rgba(206, 184, 192, 0.7);
	}

	.top20 {
		margin-top: -30px;
	}

	.top15 {
		margin-top: 15px;
	}

	.text-indent {
		text-indent: 2em;
	}

	.btn {
		text-align: right;
		font-weight: 500;
		font-size: 15px;
		color: var(--button-color);
	}
</style>
<style scoped>
	@import url("../common/common.css");
</style>
