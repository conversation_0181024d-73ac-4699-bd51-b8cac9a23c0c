<template>
	<view class="card-box">
		<view class="fol-row card-line size-mini">
			<view class="size-txt" v-if="itemData.batchStartTime==itemData.batchEndTime">参观时间：{{dayjs(itemData.batchDate).format('YYYY/MM/DD') }} &nbsp;{{itemData.batchStartTime.substr(0,2)}}:{{itemData.batchStartTime.substr(2,4)}}</view>
			<view class="size-txt" v-else>参观时间：{{dayjs(itemData.batchDate).format('YYYY/MM/DD') }} &nbsp;{{itemData.batchStartTime.substr(0,2)}}:{{itemData.batchStartTime.substr(2,4)}}-{{itemData.batchEndTime.substr(0,2)}}:{{itemData.batchEndTime.substr(2,4)}}</view>
			<view style="text-align: right;" :class="'fol-col card-line-'+itemData.orderStatus">{{SPUP_GLOBAL_CONFIG.orderStatusLabel[itemData.orderStatus]}}</view>
		</view>
		
		<view class="txt-box size-normal">
			<view v-if="itemData.orderCategory==SPUP_GLOBAL_CONFIG.orderType.person||itemData.orderCategory==SPUP_GLOBAL_CONFIG.orderType.team" class="size-regular bold">{{SPUP_GLOBAL_CONFIG.permanentExhibition.exhibitionTitle}}</view>
			<view v-if="itemData.exhibition" class="size-regular bold">{{itemData.exhibition.exhibitionTitle}}</view>
			<view>下单时间：{{itemData.createTime}}</view>
			<view>预约编号：{{itemData.orderNo}}</view>
		
		</view>
			<view v-if="itemData.orderStatus===SPUP_GLOBAL_CONFIG.orderStatus.reserved" class="common-btn fol-row">
				<view class="fol-col btn-site" @click="orderItemClick('取消预约')">
					<view class="btn-img"><image mode="widthFix" src="../../static/btn/order-time.png"></image></view>
					<text class="size-normal mini-btn">取消预约</text>
				</view>
				<view v-if="itemData.orderCategory!=SPUP_GLOBAL_CONFIG.orderType.team&&itemData.orderCategory!=SPUP_GLOBAL_CONFIG.orderType.tmpExhibitionTeam" class="fol-col btn-site btn-hr"  @click="orderItemClick('去使用')">
					<view class="btn-img"><image mode="widthFix" src="../../static/btn/order-go.png"></image></view>
					<text class="size-normal mini-btn">去使用</text>
				</view>
			</view>
		
		<view v-if="itemData.orderStatus===SPUP_GLOBAL_CONFIG.orderStatus.canceled" class="common-btn fol-row">
			<view class="fol-col btn-site" @click="orderItemClick('重新预约')" >
				<view class="btn-img"><image mode="widthFix" src="../../static/btn/order-time.png"></image></view>
				<text class="size-normal mini-btn">重新预约</text>
			</view>
			<view class="fol-col btn-site btn-hr" @click="orderItemClick('删除')" >
			    <view class="btn-img"><image mode="widthFix" src="../../static/btn/order-de.png"></image></view>
				<text class="size-normal mini-btn">删除</text>
			</view>
		</view>
	</view>
	<!-- 弹窗 -->
</template>

<script setup>
	import { ref,reactive,onMounted,computed} from 'vue'
	import {useCommonStore} from '../../stores/common.js'
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	const commonStore = useCommonStore();
	import dayjs from "dayjs";
	const props = defineProps(['itemData']);
	const emit = defineEmits(['orderItemClick']);
	
	const orderItemClick = (type)=>{
		emit("orderItemClick", type, props.itemData);
	}
	
	const goto = (url)=> {
		uni.navigateTo({
			url: url
		})
	}
			
</script> 
<style>
	page{
		background: #f7f9fa;
	}
</style>
<style scoped>
	@import url("../common/common.css");
    @import "order.css";

</style>
