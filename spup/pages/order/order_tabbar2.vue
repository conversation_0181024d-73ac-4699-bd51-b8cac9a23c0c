<template>
	<!-- 顶部选项卡 -->
	<view class="tabs">
		<view class="uni-tab-item" v-for="(item, index) in data" :key="index">
			<text class="uni-tab-item-title" :class="{tabActive: item.type==selectType}" @click="itemClick(item)">
				{{item.name}}
			</text>
			<view v-if="item.type==selectType" class="tab-item-title-line" ></view>
		</view>
	</view>
</template>
	<script setup>
		import { ref,reactive,onMounted,computed} from 'vue'
		import {useCommonStore} from '../../stores/common.js'
		import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
		const commonStore = useCommonStore();
		const props = defineProps(['modelValue']);
		const emit = defineEmits(['update:modelValue', 'itemClick']);
		const selectType = ref(0);
		const data = reactive( [
				{name: "已预约",type: SPUP_GLOBAL_CONFIG.orderStatus.reserving+SPUP_GLOBAL_CONFIG.orderStatus.reserved},
				{name: "已取消",type: SPUP_GLOBAL_CONFIG.orderStatus.canceled},
				{name: "已完成",type: SPUP_GLOBAL_CONFIG.orderStatus.completed}
			]);
			

		const itemClick = (item) =>{
			selectType.value = item.type;
			emit("update:modelValue", item.type);
			// emit("itemClick", item.type);
		};
		
		onMounted(()=>{
			selectType.value  = props.modelValue;
			// let all = 0;
			// for(let key in SPUP_GLOBAL_CONFIG.orderStatus){
			// 	all += SPUP_GLOBAL_CONFIG.orderStatus[key];
			// }
			// console.log("order tabbar onmouted:" + all);
			// data[0].type = all;
			// selectType.value = all;
			// emit("update:modelValue", all);
		})

	</script>

 
<style>
	.tabs{
		display: flex;
		flex: 1;
		flex-direction: row;
		overflow-x: scroll;
		height: 100%;
		background: #FFFFFF;
		box-shadow: 0px 2px 4px 1px rgba(152,152,152,0.16);
	}
	.uni-tab-item{
		width: 100%;
		white-space: nowrap;
		line-height: 100rpx;
		height: 100rpx;
		border-bottom: 1px solid #eee;
		text-align: center;
	}
	.uni-tab-item-title{
		color:#888888;
		font-size: 14px;
		width: 150rpx;
		display: inline-block;
		text-align: center;
	}
	.tabActive{
		font-size: 16px;
		font-weight: 500;
		color: #80616e;
	}
	.tab-item-title-line{
		display: block;
		border-bottom: 3rpx solid #80616e;
		border-top: 3rpx solid #80616e;
		width: 86rpx;
		height: 1rpx;
		line-height: 1rpx;
		margin: 0 auto;
		border-radius: 40rpx;
		margin-top:-2px;
		background-color: #80616e;
		box-sizing: border-box;
	}
</style>