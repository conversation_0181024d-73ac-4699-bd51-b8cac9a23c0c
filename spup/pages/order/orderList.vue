<template>
	<orderTabbar1 v-model="orderType.bigType" ></orderTabbar1>
	<!-- <orderTabbar2 v-model="orderType.type" ></orderTabbar2> -->
	<view style="padding-bottom: 20px;">
		<template v-for="(item, index) in orders.list" :key="item.id" :itemData='item'>
			<orderItem :itemData="item" @orderItemClick='orderItemClick'/>
		</template>
	</view>
	<view v-if="!orders.list.length" class="empty-box" style="top: 20%;">
		<image class="empty-img" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl+'/empty/empty.png'"></image>
		<text class="empty-txt" style="">{{orders.init?"数据加载中～":"暂无预约～"}}</text>
	</view>
</template>

<script setup>
	import {ref,reactive,onMounted,computed,watch} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import orderTabbar1 from './order_tabbar1.vue';
	import orderTabbar2 from './order_tabbar2.vue';
	import orderItem from './orderItem.vue';
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {getOrderList, cancelOrder, deleteOrder, getTeamOrderList, cancelTeamOrder, deleteTeamOrder, getExhibitionOrderList, cancelExhibitionOrder, deleteExhibitionOrder} from "@/common/api/server.js";
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'

	const orderType = reactive({//订单类型
		bigType : 1,
		type: 3
	});	
	
	let rawOrderArr = {}
	const orders = reactive({//订单类型
		list : [],
		init : true
	});	
	
	
	// reactive，默认深度监听，可以不设置deep:true, 新旧值一样
	watch(orderType, (newValue, oldValue) => {
		console.log('newArrayReactiveWatch', newValue, 'oldArrayReactiveWatch', oldValue)
		getRawOrderList()
	})
	
	
	const orderItemClick = (type, itemData)=>{
		console.log("parents orderItemClick:", type, itemData);
		commonStore.curOrder = itemData;
		switch(type){
			case '去使用':
				uni.navigateTo({
					url: '/subpages/order/orderDetail'
				})
				break;
			case '取消预约':
				uni.showModal({
					title: '取消预约',
					content: '确定要取消该预约吗？',
					confirmText: "取消预约",
					cancelText:"再想一想",
					success: function(res) {
						if(res.confirm){
							// allOrders.list.splice(allOrders.list.indexOf(itemData), 1);
							if(itemData.orderCategory==SPUP_GLOBAL_CONFIG.orderType.person||itemData.orderCategory==SPUP_GLOBAL_CONFIG.orderType.tmpExhibition){
								cancelOrder(itemData.orderNo).then((res)=>{
									itemData.orderStatus = SPUP_GLOBAL_CONFIG.orderStatus.canceled;
								})
							}else if(itemData.orderCategory==SPUP_GLOBAL_CONFIG.orderType.team||itemData.orderCategory==SPUP_GLOBAL_CONFIG.orderType.tmpExhibitionTeam){
								cancelTeamOrder(itemData.orderNo).then((res)=>{
									itemData.orderStatus = SPUP_GLOBAL_CONFIG.orderStatus.canceled;
								})
							}else if(itemData.orderCategory==SPUP_GLOBAL_CONFIG.orderType.exhibition){
								cancelExhibitionOrder(itemData.orderNo).then((res)=>{
									itemData.orderStatus = SPUP_GLOBAL_CONFIG.orderStatus.canceled;
								})
							}
						}
					}
				})
			  break;
			case '重新预约':
				uni.redirectTo({
					url: '/pages/tmpExhibition/tmpExhibition_list'
				})
				break;
			case '删除':
				uni.showModal({
					title: '删除订单',
					content: '确定要删除该预约吗？',
					confirmText: "删除预约",
					cancelText:"再想一想",
					success: function(res) {
						if(res.confirm){
							if(itemData.orderCategory==SPUP_GLOBAL_CONFIG.orderType.person||itemData.orderCategory==SPUP_GLOBAL_CONFIG.orderType.tmpExhibition){
								deleteOrder(itemData.orderNo).then((res)=>{
									itemData.deleted = 1
									refreshList();
								})
							}else if(itemData.orderCategory==SPUP_GLOBAL_CONFIG.orderType.team||itemData.orderCategory==SPUP_GLOBAL_CONFIG.orderType.tmpExhibitionTeam){
								deleteTeamOrder(itemData.orderNo).then((res)=>{
									itemData.deleted = 1
									refreshList();
								})
							}else if(itemData.orderCategory==SPUP_GLOBAL_CONFIG.orderType.exhibition){
								deleteExhibitionOrder(itemData.orderNo).then((res)=>{
									itemData.deleted = 1
									refreshList();
								})
							}
						}
					}
				})
				break;
			
	  }
	};
	//刷新列表
	const refreshList = ()=>{
		// orders.list = (rawOrderArr[orderType.bigType] || []).filter(item=>(item.orderStatus&orderType.type)>0&&!item.deleted)
		orders.init = false
		orders.list = (rawOrderArr[orderType.bigType] || []).filter(item=>!item.deleted)
	} 
	
	//取得原始订单数据
	let getRawOrderList = ()=>{
		if(!rawOrderArr[orderType.bigType]){
			let parseData = (res)=>{
				rawOrderArr[orderType.bigType] = res.data
				refreshList()
			}
			if(orderType.bigType==SPUP_GLOBAL_CONFIG.orderType.person){
				getOrderList().then(parseData);
			}else if(orderType.bigType==SPUP_GLOBAL_CONFIG.orderType.team){
				getTeamOrderList().then(parseData);
			}else if(orderType.bigType==SPUP_GLOBAL_CONFIG.orderType.exhibition){
				getExhibitionOrderList().then(parseData);
				refreshList()
			}
			
		}else{
			refreshList()
		}
	}
	onLoad(options => {
		if(options.bigType){
			orderType.bigType = options.bigType
		}
		if(options.type){
			orderType.type = options.type
		}
		getRawOrderList();
	})
	
</script>

<style>
page{
	background-color: #f7f9fa;
}
</style>
<style scoped>
	@import "order.css";
	@import "../common/common.css";
</style>
