page{
	font-weight: 400;
}
/* 按钮 */
	.btn-box {
		display: flex;
		padding: 16px 0;
		border-bottom: 1px solid #000;
	}

	.btn-line {
		font-size: 14px;
		width: 100%;
		height: 36px;
		margin: 10px 6px;
		border-radius:0 25px 25px 0;
		background: none;
		border: 1px solid var(--button-solid-color);
	}
	.btn-sincere{
		font-size: 14px;
		width: 100%;
		height: 36px;
		margin: 10px 6px;
		border-radius:25px;
		border: 1px solid var(--button-color);
		color:var(--button-color);
		background: #F4F2F3;
	}

	/*  */
	.complete {
		padding: 10px 15px;
		position: relative;
		font-size: 14px;
		border-radius: 12px 12px 0 0;
	}

	.uni-noticebar[data-v-a1596656] {
		margin-top: 8px;
		padding: 3px 12px;
		border-radius: 5px;
		margin-bottom: 10px;
		background-color: #f2f2f2;
		color: #000;
	}

	.content-text {
		margin-top: 20px;
		border-radius: 8px;
		line-height: 32px;
		padding: 20px 8px;
		border: 1px solid #80616e;
		background-color: #f6f4f5;
	}

	.uni-line {
		border-bottom: 1px solid #cccccc;
	}
	
	
	/* // 上部 */
	.pd-title{
		font-size: 17px;
	}
	.hr-box{
		position: relative;
		padding: 16px 0;
	}
	.hr-twill{
		position: absolute;
		border: 0;
		width: 175px;
		height: 6px;
		margin-top: -2px;
		margin-left: 40px;
		background: linear-gradient(-45deg, #000 25%, #F7F9FA 0, #F7F9FA 50%, #000 0, #000 75%, #F7F9FA 0);
		
		background-size: 8px 8px;
	}
	.hr-edge-weak{
		border: 0;
		width: 100%;
		height: 1px;
		background: #000;
	}
	.pd-text{
		text-indent: 2em;
		font-size: 13px;
		line-height: 24px;
		letter-spacing:0.2px;
		padding-bottom: 17px;
		border-bottom: 1px solid #000;
	}
	/* // 底部 */
	.time-box{
		border-bottom: 1px solid #000;
	}
	.left-time{
		width: 30%;
		padding:26px 0 16px 0;
		border-right: 1px solid #000;
	}
	.right-time{
		padding:16px;
	}
	.right-site{
		width: 30%;
		padding:15px 0 16px 20px;
		border-left: 1px solid #000;
	}
	.address-border{
		border-bottom: 1px solid #000;
	}
	.left-site{
		padding:15px 20px 16px 0;
	}
	.left-btn{
		flex: 1;
		padding-top: 4px;
	}
	.right-btn{
		text-align: center;
		font-size: 13px;
		width: 102px;
		height: 32px;
		line-height: 32px;
		border: 1px solid #000000;
		border-radius: var(--button-small-radius);
	}
	.bottom-logo{
		width: 100px;
		margin: auto;
		margin-top: 30px;
		position: fixed;
		  left: 50%;
		  bottom: 20px; /* 设置按钮离底部的距离 */
		  transform: translateX(-50%); /* 使按钮水平居中 */
	}
	.bottom-line{
		padding: 16px 0;
		border-bottom: 1px solid #000;
	}
	
	.left15{
		padding-left: 15px;
	}