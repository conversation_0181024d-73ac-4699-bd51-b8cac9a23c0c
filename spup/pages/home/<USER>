<template>
	<view class="content">
		<view style="padding:15px 15px 0 15px;">
			<swiper :display-multiple-items="2" next-margin="110rpx" @change="swiperchange" :current="current" :indicator-dots="false"  :circular="true" :interval="3000" :duration="1000">
					<swiper-item v-for="item,index in data.banner" :key="index">
						<view  :class="['swiper-item',index==current ? 'active' : '']">
							<image mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + item.src" style="width: 100%;height: 100%;border-radius: 3px;" alt="" srcset=""></image>
						</view>
					</swiper-item>
			</swiper>
			<!-- <image src="../../static/banner-bg.jpg" mode="widthFix" style="width: 100%;height: 100%;border-radius: 3px;" alt="" srcset=""></image>
		 -->
		</view>
		<view class="complete">
			<!-- 场馆文字介绍 -->
			<view>
				<text class="pd-title">浦东城市规划展</text>
				<view class="hr-box">
					<view class="hr-twill"></view>
					<view class="hr-edge-weak"></view>
				</view>
				<view class="pd-text" style="text-align: justify;">
					常设规划主题展览“浦东城市规划展”布展面积近5千平方米。展陈布局三层“世界的浦东”、二层“人民的浦东”两个主题展厅，以浦东打造社会主义现代化建设引领区的历史使命和未来发展战略定位为纲领，立体式解读浦东“十四五”规划，重点展示浦东立足新发展阶段，贯彻新发展理念，构建新发展格局，全面呈现“全球典范、璀璨明珠”的城市形象，充分展现建设开放、创新、高品质的卓越浦东的愿景目标。
				</view>
			</view>
			<!-- 按钮 -->
			<view class="btn-box">
				<button class="btn-line" @click="goto('/pages/buyTeam/buyTeam')">团体预约</button>
				<button class="btn-sincere" @click="goto('/pages/buyPerson/buyPerson')">个人预约</button>
			</view>
			<!-- 场馆时间 -->
			<!-- <view class="fol-row time-box">
				<view class="left-time size-normal"><text>开放时间</text></view>
				<view class="fol-col right-time size-normal">
					<text>{{configStore.openTime}}</text>
				</view>
			</view> -->
			<!-- 场馆时间 -->
			<!-- <view class="fol-row time-box">
				<view class="left-time size-normal" style="padding-top: 15px;"><text>地址</text></view>
				<view class="fol-col left-site size-normal" style="padding: 15px;">
					<text >上海市浦东新区高科西路2499号</text>
				</view>
			</view> -->
			<!-- 交通方式 -->
			<!-- <view class="fol-row time-box">
				<view class="left-time size-normal" ><text>交通指南</text></view>
				<view class="fol-col left-site size-normal">
					<view class="fol-col left-time-site ">
						<view class="left15">地铁</view>
						<view class="size-mini" style="padding-left: 15px;">近7号线锦绣路站1号口</view>
					</view>
					<view class="left15">公交</view>
					<view class="size-mini" style="padding:0 15px 0 15px;">
						<text>
							184路、607路、639路、792路<br>
							969路、花木1路、北蔡2路
						</text>
					</view>
				</view>
			</view> -->
			<!-- 参观指南 -->
			<view class="fol-row bottom-line">
				<view class="left-btn size-normal">参观指南</view>
				<view style="text-align: right;">
					<view class="right-btn" @click="goto('/subpages/home/<USER>')" >点击查看 >></view>
				</view>
			</view>
			<!-- 个人中心 -->
			<!-- <view class="fol-row bottom-line">
				<view class="left-btn size-normal">个人中心｜PERSONAL</view>
				<view style="text-align: right;">
					<view class="right-btn" @click="goto('/pages/order/orderList')">点击查看 >></view>
				</view>
			</view> -->
			<view class="bottom-logo">
				<image mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + '/logo/logo.png'" ></image>
			</view>
		</view>
	</view>
	<NotePanel></NotePanel>
</template>

<script setup>
	import {ref, reactive, onMounted, computed} from 'vue'
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {configRefStore} from '@/stores/configRef.js'
	const configStore = configRefStore();
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import NotePanel from '/components/note_panel.vue'
	import {getNotes} from "@/common/api/server.js";
	
	const goto = (url) => {
		uni.navigateTo({
			url: url
		})
	}
	const onClickItem = (e) => {
		if (data.current !== e.currentIndex) {
			data.current = e.currentIndex
		}
	}
	const styleChange = (e) => {
		if (data.styleType !== e.detail.value) {
			data.styleType = e.detail.value
		}
	}
	const colorChange = (e) => {
		if (data.styleType !== e.detail.value) {
			console.log(e.detail.value);
			data.activeColor = e.detail.value
		}
	}
	const data = reactive({data:{current:1},
							banner:[{src:"/banner/home-banner.png?v=1"},
								{src:"/banner/home-banner2.png?v=1"},
								{src:"/banner/home-banner3.png?v=1"}
							]})
	const swiperchange = (e) =>{this.current=e.detail.current}
	
	onMounted(()=>{
		getNotes();
	})
</script>

<style>
	page {
		background-color: #f7f9fa;
		padding-bottom: 36px;
	}
	swiper {
	    display: block;
	    height: 120px;
	}
	.swiper-item{
			height: 100%;
			transform: scale(0.95);
			transition:all 0.5s ease;
			text-align: center;
			transition: all 0.5s ease-in-out;
		}
		/* &.active{
			transform: scale(1);
		} */
	.content{
		 position: relative;
		  min-height: 100vh; /* 确保页面的高度至少为视窗高度 */
		  padding-bottom: 60px; /* 留出空间给底部按钮 */
	}
	
</style>
<style lang="scss" scoped>
	@import "../common/common.css";
	@import "./home.scss";
</style>
