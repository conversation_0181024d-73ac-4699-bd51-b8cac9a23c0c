<template>
	<view>
		<view style="padding: 15px 0 0 15px;">
			<swiper :display-multiple-items="2" next-margin="110rpx" @change="swiperchange" :current="current" :indicator-dots="false"  :circular="true" :interval="3000" :duration="1000">
					<swiper-item v-for="(item,index) in data.form.picInfo" :key="index">
						<view  :class="['swiper-item',index==current ? 'active' : '']">
							<image mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + item.url" style="width: 100%;height: 100%;border-radius: 3px;" alt="" srcset=""></image>
						</view>
					</swiper-item>
				</swiper>
		</view>
		<view class="complete">
			<!-- 场馆文字介绍 -->
			<view>
				<text class="pd-title">浦东城市规划和公共艺术中心</text>
				<view class="hr-box">
					<view class="hr-twill"></view>
					<view class="hr-edge-weak"></view>
				</view>
				<view class="pd-text">
					{{data.form.introduction}}
				</view>
			</view>
			<!-- 按钮 -->
			<view class="btn-box">
				<button class="btn-line" @click="goto('/pages/buyTeam/buyTeam')">团体预约</button>
				<button class="btn-sincere" @click="goto('/pages/buyPerson/buyPerson')">个人预约</button>
			</view>
			<!-- 场馆时间 -->
			<view class="fol-row time-box">
				<view class="left-time size-normal"><text>时间｜TIME</text></view>
				<view class="fol-col right-time size-normal">
					<text>{{data.form.openTime}}</text>
				</view>
			</view>
			<!-- 场馆时间 -->
			<view class="fol-row time-box">
				<view class="left-time size-normal" style="padding-top: 15px;"><text>地点｜PLACE</text></view>
				<view class="fol-col left-site size-normal" style="padding: 15px;">
					<text >{{data.form.address}}</text>
				</view>
			</view>
			<!-- 交通方式 -->
			<view class="fol-row time-box">
				<view class="left-time size-normal" ><text>交通｜TRAFFIC</text></view>
				<view class="fol-col left-site size-normal">
					<view class="fol-col left-time-site ">
						<view class="left15">地铁</view>
						<view class="size-mini" style="padding-left: 15px;">{{data.form.metro}}</view>
					</view>
					<view class="left15">公交</view>
					<view class="size-mini" style="padding:0 15px 0 15px;"><text>{{data.form.traffic}}</text></view>
				</view>
			</view>
			<!-- 参观指南 -->
			<view class="fol-row bottom-line">
				<view class="left-btn size-normal">参观指南｜NOTICE TO VISITORS</view>
				<view style="text-align: right;">
					<view class="right-btn" @click="goto('/subpages/home/<USER>')" >点击查看 >></view>
				</view>
			</view>
			<!-- 个人中心 -->
			<!-- <view class="fol-row bottom-line">
				<view class="left-btn size-normal">个人中心｜PERSONAL</view>
				<view style="text-align: right;">
					<view class="right-btn" @click="goto('/pages/order/orderList')">点击查看 >></view>
				</view>
			</view> -->
			<view class="bottom-logo">
				<image mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + '/logo/logo.png'" ></image>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {ref, reactive, onMounted, computed} from 'vue'
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {configRefStore} from '@/stores/configRef.js'
	const configStore = configRefStore();
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import NotePanel from '/components/note_panel.vue'
	import {noteGet} from "@/common/api/server.js";
	
	const goto = (url) => {
		uni.showToast({
			icon: "none",
			title: '预览模式下不能进行该操作'
		})
	}
	
	const onClickItem = (e) => {
		if (data.current !== e.currentIndex) {
			data.current = e.currentIndex
		}
	}
	const styleChange = (e) => {
		if (data.styleType !== e.detail.value) {
			data.styleType = e.detail.value
		}
	}
	const colorChange = (e) => {
		if (data.styleType !== e.detail.value) {
			console.log(e.detail.value);
			data.activeColor = e.detail.value
		}
	}
	const data = reactive({data:{current:1},
							banner:[{src:"/banner/home-banner.png"},
								{src:"/banner/home-banner2.png"},
								{src:"/banner/home-banner3.png"}
							],
							form:{
								
							},
						})
	const swiperchange = (e) =>{this.current=e.detail.current}
	
	onMounted(()=>{
		noteGet("APPONINTMENT").then(res=>{
			data.form = {...res.data}
		});
	})
</script>

<style>
	page {
		background-color: #f7f9fa;
		padding-bottom: 36px;
	}
	swiper {
	    display: block;
	    height: 120px;
	}
	.swiper-item{
			height: 100%;
			transform: scale(0.95);
			transition:all 0.5s ease;
			text-align: center;
			transition: all 0.5s ease-in-out;
		}
		/* &.active{
			transform: scale(1);
		} */
</style>
<style lang="scss" scoped>
	@import "../common/common.css";
	@import "./home.scss";
</style>
