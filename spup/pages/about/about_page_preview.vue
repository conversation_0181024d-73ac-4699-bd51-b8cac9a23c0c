<template>
	<view>
		<view style="padding: 15px 0 0 15px;">
			
			<swiper :display-multiple-items="1" next-margin="110rpx" @change="swiperchange" :current="current" :indicator-dots="false"  :circular="true" :interval="3000" :duration="1000">
				<swiper-item v-for="(item,index) in data.form.picInfo" :key="index">
					<view  :class="['swiper-item',index==current ? 'active' : '']">
						<image mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + item.url" style="width: 100%;height: 100%;border-radius: 3px;" alt="" srcset=""></image>
					</view>
				</swiper-item>
			</swiper>
			<!-- <swiper :display-multiple-items="1" next-margin="110rpx" @change="swiperchange" :current="current" :indicator-dots="false"  :circular="true" :interval="3000" :duration="1000">
				<swiper-item v-for="i,index in 10" :key="i">
					<view  :class="['swiper-item',index==current ? 'active' : '']">
						<image mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + '/about/about-banner.png'" style="width: 100%;height: 100%;border-radius: 2px;" alt="" srcset=""></image>
					</view>
				</swiper-item>
			</swiper> -->
		</view>
		<view class="complete">
			<view class="adorn-img">
				<image mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + '/about/about-zs.png'"></image>
			</view>
			<view class="logo-img">
				<image mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + '/about/logo.png'"></image>
			</view>
			<view class="fol-row about-txt-box">
				<view class="about-title-img">
					<image mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + '/about/about-title.png'"></image>
				</view>
				<view class="fol-col about-text">
					<text>{{data.form.introduction}}</text>
				</view>
			</view>
			<view class="about-img-row" >
				<view></view>
				<view class="about-img-box">
					<image mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + '/about/about-img.png'"></image>
				</view>
			</view>
		</view>
		<!-- 文字信息 -->
		<view class="message-box">
			<view class="message-title">参观信息  |  Visitor Information</view>
			<view>
				<view class="time-title">开放时间</view>
				<view class="hr-edge-weak hr-top"></view>
				<view class="fol-row">
					<view class="fol-col left-time size-mini">
						<text>{{data.form.openTime}}</text>
					</view>
					<view class="right-time size-mini"><text>时间 \n TIME</text></view>
				</view>
				<view class="hr-edge-weak hr-bottom"></view>
				<view class="time-title">参观地址</view>
				<view class="site-img">
					<image mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + '/about/about-map.png'"></image>
				</view>
				<view class="fol-row">
					<view class="fol-col left-site ">
						<view class="size-regular" style="padding-left: 15px;">{{data.form.address}}</view>
					</view>
					<view class="right-time size-mini"><text>地点 \n PLACE</text></view>
				</view>
				<view class="hr-edge-weak"></view>
			</view>
			<view class="fol-row">
				<view class="fol-col left-time-site ">
					<view class="time-title">地铁</view>
					<view class="size-mini" style="padding-left: 15px;">{{data.form.metro}}</view>
				</view>
				<view class="right-time-site size-mini"><text>前往 \n LEAVE FOR</text></view>
			</view>
			<view class="time-title" style="padding-top:10px;">公交</view>
			<view class="size-mini" style="padding:0 32px 10px 15px;"><text>{{data.form.traffic}}</text></view>
			<view class="time-title">停车</view>
			<view class="size-mini" style="padding:0 32px 0 15px;"><text>不提供停车服务，建议绿色出行</text></view>
		</view>
	</view>
</template>

<script setup>
	import {reactive,onMounted} from 'vue'
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {configRefStore} from '@/stores/configRef.js'
	const configStore = configRefStore();
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import {noteGet} from "@/common/api/server.js";

	const goto = (url) => {
		uni.navigateTo({
			url: url
		})
	}
	const data = reactive({data:{current:1},
							banner:[{src:"/about/banner1.png"},
								{src:"/about/banner2.png"},
								{src:"/about/banner3.png"}
							],
							form:{
								
							}
						})
	const swiperchange = (e) =>{this.current=e.detail.current}
	
	
	onMounted(()=>{
		noteGet("ABOUTUS").then(res=>{
			data.form = {...res.data}
		});
	})
</script>

<style>
	page {
		background-color:#f7f9fa;
	}
	swiper {
	    display: block;
	    height: 208px;
	}
	.swiper-item{
			height: 100%;
			transform: scale(0.95);
			transition:all 0.5s ease;
			text-align: center;
			transition: all 0.5s ease-in-out;
		}
		/* &.active{
			transform: scale(1);
		} */
</style>
<style scoped lang="scss">
	@import url("../common/common.css");
	@import "./about_page.css";
</style>
