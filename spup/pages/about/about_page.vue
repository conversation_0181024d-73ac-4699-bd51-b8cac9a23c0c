<template>
	<view>
		<view class="logo-img">
			<image mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + '/about/logo.png'"></image>
		</view>
		<view style="padding: 0 30px;">
			<image src="../../static/banner-bg.jpg" mode="widthFix" style="width: 100%;height: 100%;border-radius:0 0 3px 3px;" alt="" srcset=""></image>
		</view>
		<view class="complete">
			<view class="about-text" style="margin-top: 15px;">
				<view >浦东城市规划和公共艺术中心（Pudong UPPAC）位于浦东锦绣文化公园板块，由英国著名建筑师、普利兹克奖得主戴卫·奇普菲尔德（David <PERSON>field）及其团队设计，总建筑面积近5万平方米。</view>
				<view style="margin-top: 10px;">浦东城市规划和公共艺术中心定位打造成为集规划展示、公共艺术、科普教育、会议举办、文化创意等一体的城市公共活动空间，成为促进市民交流、城际交往、各界交融的“浦东城市会客厅”和复合型文化地标。</view>
			</view>
			<!-- <view class="about-img-row" >
				<view></view>
				<view class="about-img-box">
					<image mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + '/about/about-img.png'"></image>
				</view>
			</view> -->
		</view>
		<!-- 文字信息 -->
		<view class="message-box">
			<view style="padding: 10px 30px 0 30px;">
				<view class="time-title">开放时间</view>
				<view class="size-regular size-weight">
					<text>{{configStore.openTimeNative.slice(5)}}</text>
				</view>
				<view class="time-title">地址</view>
				<view class="size-regular size-weight">上海市浦东新区高科西路2499号</view>
				<view class="time-title">交通</view>
				<view class="fol-row size-regular">
					<view class="sub-title">地铁</view>
					<view class="size-weight left15" >近7号线锦绣路站1号口</view>
				</view>
				<view class="fol-row" style="padding: 5px 0;">
					<view class="size-regular sub-title">公交</view>
					<view class="size-regular size-weight left15">
						184路、607路、639路、792路<br>
						969路、花木1路、北蔡2路
					</view>
				</view>
				<view class="fol-row">
					<view class="size-regular sub-title">停车</view>
					<view class="size-regular size-weight left15"><text>不提供停车服务，建议绿色出行</text></view>
				</view>
			</view>
			
			<view >
				<view class="time-title" style="padding: 15px 30px 8px  30px;">地图</view>
				<!-- <image mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + '/about/about-map.png'"></image> -->
				<image mode="widthFix" src="../../static/about-map.png"></image>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {reactive,onMounted} from 'vue'
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {configRefStore} from '@/stores/configRef.js'
	const configStore = configRefStore();
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import {getNotes} from "@/common/api/server.js";

	const goto = (url) => {
		uni.navigateTo({
			url: url
		})
	}
	const data = reactive({data:{current:1},
							banner:[{src:"/about/banner1.png"},
								{src:"/about/banner2.png"},
								{src:"/about/banner3.png"}
							]})
	const swiperchange = (e) =>{this.current=e.detail.current}
	
	
	onMounted(()=>{
		getNotes();
	})
</script>

<style>
	page {
		background-color:#f7f9fa;
	}
	swiper {
	    display: block;
	    height: 208px;
	}
	.swiper-item{
			height: 100%;
			transform: scale(0.95);
			transition:all 0.5s ease;
			text-align: center;
			transition: all 0.5s ease-in-out;
		}
		/* &.active{
			transform: scale(1);
		} */
</style>
<style scoped lang="scss">
	@import url("../common/common.css");
	@import "./about_page.css";
</style>
