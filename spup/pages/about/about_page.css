page {
	font-weight: 400;
}

.complete {
	font-size: 14px;
}

.adorn-img {
	width: 7px;
	margin: auto;
	padding: 10px 0 10px 0;
}

.logo-img {
	width: 225px;
	margin: auto;
	padding: 35px 0 35px 0;
}

.about-txt-box {
	margin-top: 36px;
}

.about-title-img {
	width: 50px;
}

.about-text view{
	/* padding-left: 41px; */
	/* text-indent: 2em; */
	padding:  0 30px;
	letter-spacing: 0.2px;
	font-weight: 300;
	color: #000000;
	line-height: 23px;
	text-align: justify;
}
.about-text::after{
	content: '';
	display: inline-block;
	width: 100%;
}
.about-text2 {
	padding-left: 90px;
	text-indent: 2em;
	letter-spacing: 0.2px;
	font-weight: 300;
	color: #000000;
	line-height: 23px;
}

.about-img-row {
	display: flex;
	justify-content: space-between;
	padding-bottom: 36px;
}

.about-img-box {
	margin-top: 32px;
	width: 281px;
}

/* 文字信息 */
.message-box {
	background-color: #fff;
	padding-bottom: 36px;
}

.message-title {
	font-size: 19px;
	text-align: center;
	font-weight: 400;
	padding: 29px 0 24px 0;
}

.hr-edge-weak {
	border: 0;
	height: 1px;
	background: #000;
}
.time-title{
	font-size: 17px;
	font-weight: 600;
	padding: 20px 0 10px 0;
}
.sub-title{
	font-size: 14px;
	font-weight: 500;
}
.left15{
	padding-left: 15px;
}
.left-time {
	padding: 10px 10px 10px 15px;
}
.size-weight{
	font-weight: 300;
}
.right-time {
	width: 16%;
	line-height: 21px;
	padding: 10px 0 10px 15px;
	border-left: 1px solid #000;
}
.hr-bottom{
	margin-bottom: 15px;
}
.site-img{
/* 	width: 324px; */
	margin-top: 26px;
}
.left-site{
	padding-top: 18px;
}
.left-time-site {
	padding: 10px 10px 0 0;
}

.right-time-site {
	width: 16%;
	line-height: 21px;
	padding: 10px 0 5px 15px;
	border-left: 1px solid #000;
	border-bottom: 1px solid #000;
}
