<template>
	<view v-for="(item, index) in props.modelValue.options" :key="index" :itemData='item'>
		<MyCheckbox :disabled='props.disabled' v-model="props.modelValue.options[index]" @myChange="myChangeParent"/>
	</view>
</template>

<script setup>
	import MyCheckbox from "./my-checkbox.vue"
	
	const props = defineProps(['modelValue', "disabled"]);
	const emit = defineEmits(['update:modelValue']);
	
	//暴露给child的方法
	const myChangeParent = (childItemData)=>{
		
	}
</script>

<style>
</style>