
	 .pd-title{
		padding-left: 20px;
	 	font-size: 17px;
		font-weight: 700;
	 }
	 .hr-box{
	 	position: relative;
	 	padding: 16px 0;
	 }
	 .hr-twill{
	 	position: absolute;
	 	border: 0;
	 	width: 47px;
	 	height: 6px;
	 	margin-top: -2px;
	 	margin-left: 28px;
	 	background: linear-gradient(-45deg, #000 25%, #F7F9FA 0, #F7F9FA 50%, #000 0, #000 75%, #F7F9FA 0);
	 	background-size: 8px 8px;
	 }
	 .hr-edge-weak{
	 	border: 0;
	 	width: 100%;
	 	height: 1px;
	 	background: #000;
	 }
	.pq-title {
		font-size: 14px;
		font-weight: 700;
		margin-bottom: 10px;
	}

	.topic-box {
		padding: 15px 10px;
		margin-bottom: 15px;
		background-color: #fcfeff;
		box-shadow: 0 0 20px rgba(227, 229, 229, 0.5);
		border-radius: 6px;
	}

	.check-size {
		display: flex;
		padding: 10px 0;
	}


	/* 按钮 */
	.btn-box {
		width: 100%;
		border-top: 1px solid #000;
		display: flex;
		/* position: fixed; */
		padding: 15px 0;
		/* #ifdef H5 */
		left: var(--window-left);
		right: var(--window-right);
		/* #endif */
		/* bottom: 0; */
		background: #f7f9fa;
		font-weight: 400;
	}
	
	.btn-right-site {
		text-align: right;
	}
	
	.btn-s {
		width: 94px;
		margin-bottom: 10px;
		margin-right: 15px;
		/* padding: 3px 0; */
		font-size: 14px;
		color: #fff;
		background: var(--button-color);
		border-radius: var(--button-small-radius);
	}
	.btn-n{
		width: 94px;
		margin-bottom: 10px;
		margin-right: 15px;
		/* padding: 3px 0; */
		font-size: 14px;
		border: 1px solid var(--button-solid-color);
		background-color: var(--button-n);
		color: var(--button-n-text);
		border-radius: 6px;
		text-align: center;
	}
	.txt-req{
		color: #ff0000;
	}
	.txt-noreq{
		visibility: hidden;
	}