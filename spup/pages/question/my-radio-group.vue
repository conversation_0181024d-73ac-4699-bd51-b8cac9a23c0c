<template>
	<view v-for="(item, index) in props.modelValue.options" :key="index" :itemData='item'>
		<MyRadio :disabled='props.disabled' v-model="props.modelValue.options[index]" @myChange="myChangeParent"/>
	</view>
</template>

<script setup>
	import MyRadio from "./my-radio.vue"
	
	const props = defineProps(['modelValue', "disabled"]);
	const emit = defineEmits(['update:modelValue']);
	
	//暴露给child的方法
	const myChangeParent = (childItemData)=>{
		console.log("myChangeParent:", childItemData);
		props.modelValue.options.forEach(item=>{
			if(item.title!=childItemData.title){
				delete item.select
			}
		})
	}
</script>

<style>
</style>