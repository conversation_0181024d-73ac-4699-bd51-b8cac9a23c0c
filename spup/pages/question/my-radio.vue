<template>
	<view class="my-radio-wrap"  @click.stop="myClick">
		<radio @click.stop="myClick" :disabled='props.disabled' :checked="props.modelValue.select==1" class="checkbox-size" color="#815462" />
		<view @click.stop="myClick" class="title">{{props.modelValue.title}}</view>
	</view>
	<view v-if="props.modelValue.type=='textarea' && props.modelValue.select==1" style="background-color: #f2f4f5;width: 100%;height: 45px;border-radius: 5px;">
		<input :disabled='props.disabled' v-model="props.modelValue.input" type="text" style="padding: 10px;"/>
	</view>
</template>

<script setup>
	const props = defineProps(['modelValue', "disabled"]);
	const emit = defineEmits(['update:modelValue', "myChange"]);
	
	const myClick = ()=>{
		console.log("myClick:", props.modelValue);
		if(props.disabled){
			return
		}
		if(props.modelValue.select){
			delete props.modelValue.select
		}else{
			props.modelValue.select = 1
		}
		emit('myChange', props.modelValue)	//通过父暴露给子的方法，通知数据有变化
	}
</script>

<style scoped>
	.my-radio-wrap{
		display: flex;
		margin: 12px 0 10px 5px;
	}
	.checkbox-size {
		transform: scale(0.7);
		padding-right: 10px;
	}
</style>