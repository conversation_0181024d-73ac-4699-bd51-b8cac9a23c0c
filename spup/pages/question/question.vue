<template>
	<view style="padding: 30px 15px 50px 15px;">
		<text class="pd-title">意见反馈</text>
		<view class="hr-box">
			<view class="hr-twill"></view>
			<view class="hr-edge-weak"></view>
		</view>
		
	    <view class="topic-box" v-for="(item, index) in data.list" :key="index" :itemData='item'>
	    	<view class="pq-title"><text :class="{'txt-req':true, 'txt-noreq':!item.required}">*</text>{{index+1}}、{{item.title}}</view>
			<MyRadioGroup :disabled="data.disabled" v-model="data.list[index]" v-if="item.type==1"></MyRadioGroup>
			<MyCheckboxGroup :disabled="data.disabled" v-model="data.list[index]" v-if="item.type==2"></MyCheckboxGroup>
	    	<view v-if="item.type==3" style="background-color: #f2f4f5;width: 100%;height: 150px;border-radius: 5px;">
	    		<textarea :disabled="data.disabled" v-model="item.input"  style="width:100%;padding: 15px;box-sizing: border-box;"/>
	    	</view>
	    </view>
		
	</view>
	<!-- 按钮 -->
	<view class="btn-box fol-row">
		<view class="fol-col" ></view>
		<view class="btn-right-site">
			<button :class="data.disabled?'btn-n':'btn-s'" @click="okClick">提交</button>
		</view>
	</view>
</template>
<script setup>
	import {ref,reactive,onMounted,computed} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import {getQuestion, postQuestion} from "@/common/api/server.js";
	import dayjs from "dayjs";
	import MyRadioGroup from "./my-radio-group.vue"
	import MyCheckboxGroup from "./my-checkbox-group.vue"
	
	const data = reactive(
		{
			id:null,
			disabled:true,
			list:[
				// {id:1, required:1, type:"radio", title:"您的性别", options:["男", "女"]},
				// {id:2, required:1, type:"radio", title:"您的年龄", options:["18岁以下", "18岁至40岁", "41岁至60岁", "60岁以上"]},
				// {id:3, required:1, type:"radio", title:"您的最高学历", options:["高中/中专及以下", "大专", "本科", "硕士及以上"]},
				// {id:4, required:1, type:"radio", title:"您的专业背景或从事的行业", options:["城市规划或建筑设计、景观设计等相关专业或行业","非相关专业或行业"]},
				// {id:5, required:1, type:"radio", title:"您参观浦东规划馆的方式", options:["个人自行参观", "和家人、亲友参观","单位组织参观","社会组织参观","其他"]},
				// {id:6, required:1, type:"radio", title:"您这次来浦东规划馆的目的是", options:["随便逛逛", "了解一下浦东新区的发展", "了解具体的规划方案或实施政策", "其他"]},
				// {id:7, required:1, type:"radio", title:"您每月去参观各类型展馆的频率", options:["0次 ", "1-2次", "3-4次", "5次及以上"]},
				// {id:8, required:1, type:"checkbox", title:"您最感兴趣的馆内展项是（可多选）", options:[{type:"section" ,title:"三层展厅:", list:["地球之眼", "映像浦东","战略引领","风从东方来","规划之路","战略定位","BOX空间","更高水平改革开放的开路先锋","自主创新发展的时代标杆","宜居宜业的城市治理样板","璀璨浦东"]}, {type:"section" ,title:"二层展厅:", list:["人民城市理念", "浦东天际线","畅游公园","绿色动脉 活力水岸","浦东林荫道","魅力都市","历史街区","美丽乡村","骑行浦东","数字沙盘——《浦东十二时辰》","陆家嘴记忆","幸福公园","看不见的地下生命线","缤纷社区体验空间——《种子》艺术装置","飞阅浦东"]}]},
				// {id:9, required:1, type:"radio", title:"您认为参观后有助于您了解浦东新区的规划吗", options:["作用非常大", "作用比较大","一般","作用比较小","一点都没有作用"]},
				// {id:10, required:1, type:"radio", title:"您认为进行规划知识普及教育，对于市民的生活或工作作用大吗", options:["非常大", "比较大","一般","不怎么大","没作用"]},
				// {id:11, required:1, type:"checkbox", title:"您认为浦东规划馆应该起到哪些作用（可多选）", options:["向市民介绍规划编制成果", "向市民展示城市发展前景", "对市民进行规划知识普及教育", "为市民开展城市规划公众参与提供场所", "为市民提供可休息的场所", "为市民提供小孩活动的场所", {type:"moreInfo", title:"其他"}]},
				// {id:12, required:1, type:"radio", title:"您对馆内公共服务设施的评价是", options:["非常满意", "满意","一般","不满意","非常不满意"]},
				// {id:13, required:1, type:"radio", title:"您对馆内路线设计和标识的评价是", options:["非常满意", "满意","一般","不满意","非常不满意"]},
				// {id:14, required:1, type:"radio", title:"您对馆内环境卫生的评价是", options:["非常满意", "满意","一般","不满意","非常不满意"]},
				// {id:15, required:1, type:"radio", title:"您对展馆工作人员服务态度的评价是", options:["非常满意", "满意","一般","不满意","非常不满意"]},
				// {id:16, required:1, type:"radio", title:"参观后您是否愿意向亲人、朋友和同事等推荐浦东规划馆", options:["非常愿意", "愿意","一般","不愿意"]},
				// {id:17, required:0, type:"textarea", title:"请留下您宝贵的建议"},
			]
	   }
	)
		
	//提交 
	const okClick = ()=>{
		if(data.disabled){
			return
		}
		console.log("提交：", data.list);
		let errMsg;
		for(let i=data.list.length-1; i>=0; i--){
			let item = data.list[i]
			if(item.type==1||item.type==2){
				if(item.options.filter(option=>!option.select).length==item.options.length && item.required){
					errMsg = "请先完成第"+(i+1)+"题"
				}
				item.options.forEach(option=>{
					if(option.select && option.type=="textarea"){	//当某个选项被选中就需要输入信息时
						if(!option.input || option.input.trim().length<2 || option.input.trim().length>140){
							errMsg = "请先完善第"+(i+1)+"题的选项：“"+option.title+"”，2~140个字"
						}
					}
				})
			}else if(item.type==3){
				console.log("item.type==3")
				if(item.required){
					if(!item.input || item.input.trim().length<2 || item.input.trim().length>140){
						errMsg = "请先完成第"+(i+1)+"题，2~140个字"
					}
				}else{
					if(item.input && item.input.trim().length>0 && (item.input.trim().length<2 || item.input.trim().length>140)){
						errMsg = "请为第"+(i+1)+"题输入2~140个字的回答"
					}
				}
			}
		}
		if(errMsg){
			uni.showModal({
				content: errMsg,
				showCancel: false,
				complete:()=>{
				}
			});
			return;
		}
		postQuestion(data.id, data.list).then(res=>{
			if(res.code==0){
				data.disabled = true;
				uni.showModal({
					content: "提交成功",
					showCancel: false,
					complete:()=>{
					}
				});
			}
		})
	}
	
	onLoad(options => {
		console.log("options:", options);
		if(options.id){
			data.id = options.id
		}
		getQuestion(data.id).then(res=>{
			if(res.data.hasSubmit>0){
				data.disabled = true;
				uni.showModal({
					content: res.data.message ? res.data.message : "系统繁忙，暂时不能提交，hasSubmit="+res.data.hasSubmit,
					showCancel: false,
					complete:()=>{
					}
				});
			}else{
				data.disabled = false;
			}
			data.list = JSON.parse(res.data.options)
			data.id = res.data.id
		})
	})	
</script>
<style>
	page {
		background-color: #f7f9fa;
	}
</style>
<style scoped>
	 @import "../../pages/common/common.css";
	 @import "./question.css";
</style>
