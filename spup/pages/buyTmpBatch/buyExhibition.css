/* 信息 */
  page{
  	font-weight: 400;
  }
	.complete {
		padding: 10px 15px;
	}
	.complete2 {
		padding: 0 15px;
	}
	.pd-title{
		font-size: 17px;
		font-weight: 400;
		padding-bottom: 10px;
	}
	.right-site{
		width: 20%;
		padding-top: 20px;
		border-left: 1px solid #000000;
		text-align: right;
	}
	.left-site{
		font-size: 12px;
		
	}
	.bottom-line{
		border-bottom: 1px solid #000;
	}
	.site-hr{
		padding-bottom: 15px;
		margin-top: 10px;border-bottom: 1px solid #000;
	}
	.time-left{
		padding-top: 10px;
	}
	.time-hr{
		border-bottom: 1px solid #000;
		padding: 15px 0;
	}
	.right-time{
		width: 20%;
		padding-top: 10px;
		padding-bottom: 10px;
		border-left: 1px solid #000000;
		text-align: right;
	}
	.date-box{
		border-radius: 6px;
		margin:12px 4px;
		text-align: center;
		font-size: 12px;
	}
	
	.date-box-s {
		border: 1px solid #8d4460;
		background-color: #f1ebee;
		color: #000;
	}
	
	.date-box-n {
		background-color: #f2f2f2;
		color: #959595;
	}
	
	.date-box-z {
		border: 1px solid #000;
		color: #000000;
	}
	.span-number {
		white-space: nowrap;
		overflow-wrap: break-word;
		margin-top: -8px;
		font-size: 12px;
	}
	.time-box-z {
		padding: 3px 0;
		margin: 4px 6px;
		border-radius: 6px;
		text-align: center;
		border: 1px solid var(--button-solid-color);
		color: #000000;
	}
	
	.time-box {
		border: 1px solid var(--button-solid-color);
		padding: 5px 0;
		margin: 2px 6px 2px 6px;
		color: #000000;
		border-radius: 6px;
		text-align: center;
		width: 44%;
	}
	
	.time-box-s {
		border: 1px solid var(--button-color);
		background-color: #efebec;
		color: var(--button-color);
	}
	
	.time-box-n {
		border: 1px solid var(--button-solid-color);
		background-color: var(--button-n);
		color: var(--button-n-text);
		border-radius: 6px;
		text-align: center;
	}
.hr-edge-weak {
	border: 0;
	height: 1px;
	background: #000;
	margin-top: 6px;
}
.btn-add{
	width: 71px;
	height: 34px;
	margin-top: 5px;
	font-size: 14px;
	line-height: 34px;
	border: 1px solid #000000;
	border-radius: var(--button-small-radius);
}
.information-text {
		border: 1px solid #000000;
		border-radius:var(--button-small-radius);
		padding: 6px 10px;
		margin: 10px 0 15px 0;
	}
	/* 按钮 */
	.btn-box {
		width: 100%;
		border-top: 1px solid #000;
		display: flex;
		position: fixed;
		padding: 15px 0;
		/* #ifdef H5 */
		left: var(--window-left);
		right: var(--window-right);
		/* #endif */
		bottom: 0;
		background: #F7F9FA ;
		font-weight: 400;
	}
	
	.btn-right-site {
		text-align: right;
	}
	
	.btn-s {
		width: 94px;
		margin-bottom: 10px;
		margin-right: 15px;
		/* padding: 3px 0; */
		font-size: 14px;
		color: #fff;
		background: var(--button-color);
		border-radius: var(--button-small-radius);
	}
	
	
	.new-list {
		margin: 10px 0;
		background-color: #fff;
		font-size: 14px;
		border-radius: 2px;
		padding: 1px 0;
		color: #80616e;
		box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
	}

	uni-button:after {
		content: none;
		width: 200%;
		height: 200%;
		position: absolute;
		top: 0;
		left: 0;
		border: 1px solid rgba(0, 0, 0, 0.2);
		transform: scale(0.5);
		transform-origin: 0 0;
		box-sizing: border-box;
		border-radius: 10px;
	}

	
	
	.date-box:first-child{
		margin-left: 0px;
	}
	.date-box:last-child{
		margin-right: 0px;
	}
	