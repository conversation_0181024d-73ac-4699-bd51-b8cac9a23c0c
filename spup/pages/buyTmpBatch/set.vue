<template>
	<view style="padding: 20px; padding-bottom: 100px;">
		<view style="display: flex;margin-bottom: 15px;">
			<view style="font-weight: 600; flex:1;font-size: 15px;">请选择需要开放的场次：</view>
			<view >当前日期：{{dayjs().format("YYYY-MM-DD")}}</view>
		</view>
<!-- 		<view v-for="(subItem, index) in data.list" :key="subItem.id" :itemData='subItem'>
			<view @click.stop="timeClick(subItem)" class="my-radio-wrap">
				<checkbox @click.stop="timeClick(subItem)" :disabled='subItem.disabled' class="checkbox-size" :checked="subItem.opened==1" color="#815462" />
				<view @click.stop="timeClick(subItem)" class="title">{{subItem.name.substr(0, 2)}}:{{subItem.name.substr(2, 2)}}</view>
			</view>
		</view> -->
		<view v-for="(item, index) in data.list" :key="item.id" :itemData='item'>
			<view @click="timeClick(item)"
				:class="'time-btn '+itemStatus(item)" >
			<viwe class="middle-text"> {{item.name.substr(0,2)+':'+item.name.substr(2,4)}}</viwe>
			<viwe class="rigth-text" v-if="item.opened==1" >开放中</viwe>
			</view>
		</view>
	</view>
	<view class="btn-box fol-row">
		<view class="fol-col" ></view>
		<view class="btn-right-site">
			<button :class="selectBatchs.length==0?'btn-n':'btn-s'" @click="openClick">开放选中的场次</button>
		</view>
	</view>
</template>

<script setup>
	import {ref,reactive,onMounted,computed,watch} from 'vue'
	import {onLoad} from '@dcloudio/uni-app';
	import dayjs from "dayjs";
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {configRefStore} from '@/stores/configRef.js'
	const configStore = configRefStore();
	import {tempBatchList, tempBatchOpen} from "@/common/api/server.js";
	
	const data = reactive({
		list:[]
	})
	
	//带参数的计算属性
	const itemStatus = computed(() => (item) => {
		if(item.opened>1){			//已过期
			return "time-diabled"
		}else if(item.opened==1){	//已激活
			return "time-opened"
		}else{
			if(item.select){		//选中
				return "time-selected"
			}else{					//未选中
				return "time-normal"
			}
		}
		return "time-normal"
	})
	
	const timeClick = (subItem)=>{
		let status = itemStatus.value(subItem)
		if(status=="time-normal"){
			subItem.select = true
		}else if(status=="time-selected"){
			delete subItem.select
		}else if(status=="time-opened"){
			uni.showToast({
				icon: "none",
				title: '该场次已经是开放状态'
			})
		}else{
			uni.showToast({
				icon: "none",
				title: '该场次已过期，不能设置'
			})
		}
	}
	
	const selectBatchs = computed(()=>{
		let selectItems = [];
		data.list.forEach(subItem=>{
			if(subItem.select){
				selectItems.push(subItem)
			}
		})
		return selectItems
	})
	
	const openClick = ()=>{
		if(selectBatchs.value.length==0){
			return
		}
		uni.showModal({
			title: '开放场次',
			content: '开放之后无法关闭，确定要开放选中的场次吗？',
			confirmText: "确认开放",
			cancelText:"再想一想",
			success: function(res0) {
				if(res0.confirm){
					tempBatchOpen(selectBatchs.value).then(res=>{
						selectBatchs.value.forEach(item=>{
							delete item.select
							item.opened = 1;
						})
						uni.showModal({
							title:"设置成功",
							content: '场次已设置为开放状态',
							confirmText: "确定",
							showCancel: false,
						});
					});
				}
			}
		})
	}
	
	onMounted(()=>{
		if(commonStore.tmpBatch.menuCode.indexOf("-1")>=0){
			tempBatchList().then(res=>{
				// res.data.forEach(subItem=>{
				// 	subItem.disabled = subItem.opened
				// })
				data.list = res.data;
			})
		}else{
			uni.showModal({
				title:"权限不足",
				content: '只有管理员才能进行该操作',
				confirmText: "确定",
				showCancel: false,
			});
		}
	})
</script>
<style>
	page{
		background: #ffffff;
	}
</style>
<style scoped>
	 @import "../../pages/common/common.css";
	.my-radio-wrap{
		display: flex;
		margin: 12px 0 10px 5px;
	}
	
	.checkbox-size {
		transform: scale(0.7);
		padding-right: 10px;
	}
	
	.time-btn{
		padding: 7px 0;
		margin: 10px 11px;
		font-size: 16px;
		border-radius: 6px;
		/* text-align: center;	 */
	    display: flex;
		justify-content: space-between;
		align-items: center;
		height: 30px;
		width: 100%;
		border:1px solid #ccc;
		background-color:#fff;
		color:#000;
	}
	
	.middle-text {
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
	}
	.rigth-text {
		padding-left: 59%;
		font-size: 14px;
	}	
	.time-diabled{
		border: 1px solid #c7c9ce;
		background-color: var(--button-n);
		color: #8f939c;
	}
	.time-opened{
		border: 1px solid var(--button-color);
		background-color: #efebec;
		color: var(--button-color);
	}
	.time-selected{
		border: 1px solid var(--button-color);
		background-color:var(--button-color);
		color:#fff;
	}
	
	
	
	/* 按钮 */
	.btn-box {
		width: 100%;
		border-top: 1px solid #000;
		display: flex;
		position: fixed;
		padding: 15px 0;
		/* #ifdef H5 */
		left: var(--window-left);
		right: var(--window-right);
		/* #endif */
		bottom: 0;
		background: #f7f9fa;
		font-weight: 400;
	}
	
	.btn-right-site {
		text-align: right;
	}
	
	.btn-s {
		margin-bottom: 10px;
		margin-right: 15px;
		/* padding: 3px 0; */
		font-size: 14px;
		color: #fff;
		background: var(--button-color);
		border-radius: var(--button-small-radius);
	}
	.btn-n{
		margin-bottom: 10px;
		margin-right: 15px;
		/* padding: 3px 0; */
		font-size: 14px;
		border: 1px solid var(--button-solid-color);
		background-color: var(--button-n);
		color: var(--button-n-text);
		border-radius: 6px;
		text-align: center;
	}
</style>