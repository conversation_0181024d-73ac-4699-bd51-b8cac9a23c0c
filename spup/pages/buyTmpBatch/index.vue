<template>
	<view class="msg">
		{{data.msg}}
	</view>
</template>

<script setup>
	import {ref,reactive,onMounted,computed,watch} from 'vue'
	import {onLoad} from '@dcloudio/uni-app';
	import dayjs from "dayjs";
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {configRefStore} from '@/stores/configRef.js'
	const configStore = configRefStore();
	import {tempBatchGetRole} from "@/common/api/server.js";
	
	const data = reactive({
		msg:""
	})
	onLoad(options=>{
		if(options.category){
			tempBatchGetRole().then(res=>{
				commonStore.tmpBatch.menuCode = res.data.menuCode.split(",");
				if(commonStore.tmpBatch.menuCode.indexOf("-1")>=0){//展项场次设置管理员
					uni.reLaunch({
						url: "/pages/buyTmpBatch/set"
					})
				}else{
					uni.reLaunch({
						url: "/pages/buyTmpBatch/buyExhibition?category="+options.category
					})
				}
			}).catch(err=>{
				data.msg = err.message ? err.message : "网络异常，请稍候再试"
				uni.showModal({
					title:"请求失败",
					content: "网络异常，请稍候再试",
					confirmText: "确定",
					showCancel: false,
				});
			});
		}else{
			data.msg = "参数异常, 请扫描正确的二维码";
			uni.showModal({
				title:"参数异常",
				content: '请扫描正确的二维码',
				confirmText: "确定",
				showCancel: false,
			});
		}
	})
</script>

<style>
	.msg{
		text-align: center;
		color: #ff0000;
		margin-top: 40%;
	}
</style>