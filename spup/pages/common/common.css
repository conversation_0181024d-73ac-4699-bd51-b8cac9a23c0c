/* 小程序需要在此处定义 ，H5需要定义在APP.vue*/
:root{
	--button-color:#815462;
	--assist-color:#815462;
	--button-radius:6px;
	--button-small-radius:8px;
	--button-solid-color:#999;
	--button-n:#EDEFF0;
	--button-n-text:#8c8c8c;
	--bt-color:#da2424;
}
.fol-row {
	display: flex;
}

.fol-col {
	flex: 1;
	/* max-width: 334px; */
}

.txt-color {
	color: #26111a;
}
.box{
	padding: 0 15px;
}

.size-mini {
	font-size: 12px;
}
.size-normal{
	font-size: 13px;
}

.size-regular {
	font-size: 14px;
}

.size-medium {
	font-size: 16px;
}
.size-big {
	font-size: 18px;
}

.size-span {
	font-size: 12px;
	color: #999999;
}
.size-span2 {
	font-size: 14px;
	color: #999999;
}
.size-txt{
	overflow-wrap: break-word;
	white-space: nowrap;
}
.text-wrap{
	overflow-wrap: break-word;
}
uni-button:after {
    content:none;
}
button::after {
    border: none;
}

image{
	width: 100%;
}

.empty-box{
	position: absolute;
	top:15%; 
	left:50%;
	transform:translate(-50%,-15%);
	text-align: center;
}
.empty-img {
	margin: auto;
	width: 251px;
	height:190px;

}
.empty-txt{
	font-size: 14px;
	color: #999;
	padding-top: -50px;
}



.btn-white{
	text-align: center;
	background: none !important;
	border: 0.8px solid #815462;
	font-size: 13px;
	border-radius: 25px;
	color: #815462;
	overflow: hidden;
	bottom: 0;
	height: 28px;
	line-height: 28px;
}
.btn-white-big{
	text-align: center;
	background: none !important;
	border: 0.8px solid #815462;
	font-size: 13px;
	padding: 0px 20px;
	border-radius: 25px;
	color: #815462;
	overflow: hidden;
	bottom: 0;
	height: 28px;
	line-height: 28px;
}
.btn-color{
	font-size: 14px;
	color: #fff;
	background: var(--button-color);
	border-radius: 25px;
	height: 28px;
	line-height: 28px;
	margin-left: 12px;
}
.btn-color-big{
	font-size: 14px;
	color: #fff;
	background: var(--button-color);
	border-radius: 25px;
	padding: 0px 20px;
	height: 28px;
	line-height: 28px;
	margin-left: 12px;
}

.btn-disable{
/* 	font-size: 14px;
	height: 28px;
	line-height: 28px;
	border: 1px solid #f0f0f0;
	background-color: var(--button-n);
	color: var(--button-n-text);
	border-radius: 25px; */
	background: #ccc; 
	color: #f7f7f7;
}

.btn-disable-big{
	font-size: 14px;
	height: 28px;
	line-height: 28px;
	border: 1px solid #f0f0f0;
	background-color: var(--button-n);
	color: var(--button-n-text);
	border-radius: 25px;
	padding: 0px 20px;
}
.btn-color-huge {
	margin-bottom: 10px;
	/* padding: 3px 0; */
	font-size: 14px;
	color: #fff;
	background: var(--button-color);
	border-radius: 25px;
}
.btn-disable-huge{
	margin-bottom: 10px;
	/* padding: 3px 0; */
	font-size: 14px;
	border: 1px solid #f0f0f0;
	background-color: var(--button-n);
	color: var(--button-n-text);
	border-radius: 25px;
}