<template>
	<view class="complete">
		<view class="pd-title" style="border-bottom: 1px solid #000;">浦东城市规划和公共艺术中心</view>
		<!-- <exhibitionTypeTab v-model="exhibitionType" ></exhibitionTypeTab> -->
		<!-- 选择显示时间段 -->
		<view class="fol-row bottom-line">
			<view class="fol-col">
				<view class="size-regular time-left">
					<text>当天日期</text>
				</view>
			</view>
			<view class="right-time size-regular"><text>{{reserveDate.selectMonth}}月{{dayjs().format('D')}}日</text>
			</view>
		</view>
		<view class="fol-row time-hr">
			<view class="fol-col">
				<view class="size-regular">
					<view>选择场次 | TIME</view>
				</view>
			</view>
			<view class="size-regular">
				<text>剩余票数：{{reserveDate.selectTime.ticketRemaining?reserveDate.selectTime.ticketRemaining:"*"}}</text>
			</view>
		</view>
		<view style="display: flex; flex-wrap: wrap;margin-top: 10px;">
			<template v-for="(item, index) in reserveDate.times" :key="index" :itemData='item'>
				<view @click="timeClick(item)"
					:class="reserveDate.selectTime.id===item.id ? 'time-box time-box-s' : (item.batchStatus==1 && item.ticketRemaining>0? 'time-box' : 'time-box time-box-n')"
					style="margin-top: 10px;">
					{{item.batchStartTime.substr(0,2)+':'+item.batchStartTime.substr(2,4)}}
				</view>
			</template>
			<view class="size-mini" style="color:#999;font-weight: 500;padding-top: 10px;">* 成功预约后请提前10分钟到展项所在位置等候。
			</view>
		</view>
	</view>
	<view class="hr-edge-weak"></view>
	<view v-if="!justShow">
		<view class="complete2">
			<view class="fol-row  time-hr" style="margin-bottom: 20px;">
				<view class="fol-col" style="text-align: left;line-height: 22px;">
					<view class="size-regular">添加预约人｜TICKET BUYER</view>
					<text class="size-mini" style="color:#999;">每次最多预约2人</text>
				</view>
				<view style="text-align: right;">
					<button class="btn-add"
						@click="goto('/subpages/buy/customerList?maxCount=2&orderType='+SPUP_GLOBAL_CONFIG.orderType.exhibition)">添加</button>
				</view>
			</view>
			<template v-for="(item, index) in commonStore.customers.filter(item=>item.checked)" :key="index"
				:itemData='item'>
				<view class="information-text">
					<text class="size-regular">{{formatName(item.name)}}</text>
					<view class="size-span">
						<text>手机号: {{item.phone.substr(0,3)}}****{{item.phone.substr(7,11)}}</text>
					</view>
				</view>
			</template>
		</view>
		<view class="btn-box fol-row">
			<view class="fol-col"></view>
			<view class="btn-right-site">
				<button class="btn-s" @click="submitClick">提交预约</button>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		reactive,
		onMounted,
		computed,
		watch
	} from 'vue'
	import {
		onLoad,
		onUnload
	} from '@dcloudio/uni-app';
	import dayjs from "dayjs";
	import {
		useCommonStore
	} from '@/stores/common.js'
	import {
		formatName
	} from '@/common/myUtil.js'
	const commonStore = useCommonStore();
	import {
		getDetailByDateByExhibitionNo,
		createExhibitionOrder
	} from "@/common/api/server.js";
	import {
		SPUP_GLOBAL_CONFIG
	} from '@/common/config.js'
	// import exhibitionTypeTab from "./exhibitionTypeTab.vue"

	const justShow = ref(false)
	const exhibitionType = ref(SPUP_GLOBAL_CONFIG.exhibitionType.pudong)
	watch(exhibitionType, (newVal, oldVal) => {
		console.log("exhibitionType变化了：", newVal, oldVal);
	});
	const data = reactive({
		exhibitionNo: 'fypd'
	})
	const reserveDate = reactive({
		dates: [],
		selectDate: {},
		selectMonth: '',
		times: [],
		selectTime: {},
	})

	const goto = (url) => {
		uni.navigateTo({
			url: url
		})
	}

	//点击日期
	const dateClick = (item) => {
		reserveDate.selectDate = item
		reserveDate.selectMonth = dayjs(item.fullDate).month() + 1

		let now = dayjs().format('YYYYMMDDHHmm')
		rawReservDate[item.fullDate].forEach(tmpItem => {
			// tmpItem.batchStatus = now>tmpItem.batchNo ? 0 : tmpItem.batchStatus
			tmpItem.batchStatus = tmpItem.batchStatus
		})
		reserveDate.times = rawReservDate[item.fullDate]
		timeClick({})
		for (let i = 0; i < reserveDate.times.length; i++) {
			let tmpItem = reserveDate.times[i]
			if (tmpItem.batchStatus == 1 && tmpItem.ticketRemaining > 0) {
				timeClick(tmpItem)
				break;
			}
		}
	}
	//点击时间
	const timeClick = (item) => {
		console.log(item.ticketRemaining);
		if (item.id && (item.batchStatus == 0 || item.ticketRemaining <= 0)) {
			uni.showToast({
				icon: "none",
				title: item.ticketRemaining <= 0 ? '本时段预约已满，请选择其他场次' : '本时段不可预约，请选择其他场次'
			})
		} else {
			reserveDate.selectTime = item
		}
	}

	//点击提交预约按钮
	const submitClick = () => {
		if (!reserveDate.selectTime.id) {
			uni.showToast({
				icon: "none",
				title: '请先选择预约时间'
			})
			return;
		}
		if (commonStore.customers.filter(item => item.checked).length < 1) {
			uni.showToast({
				icon: "none",
				title: '请先添加预约人'
			})
			return;
		}

		let batchNo = reserveDate.selectTime.batchNo
		let contacts = commonStore.customers.filter(item => item.checked)
		uni.showLoading({
			title: "提交中…",
			mask: true
		});
		createExhibitionOrder(batchNo, contacts).then((res) => {
			uni.hideLoading()
			console.log("展项预约下单返回：", res);
			if (res.code === 0) {
				uni.navigateTo({
					url: "/pages/buyExhibition/buyExhibitionOK"
				})
			} else {
				uni.navigateTo({
					url: "/pages/buyExhibition/buyExhibitionOK?message=" + (res.message || "提交预约失败")
				})
			}
		}).catch(res => {
			uni.hideLoading()
		});
	}

	let rawReservDate = {}
	onLoad(options => {
		console.log("buyExhibition options:", options);
		justShow.value = parseInt(options.justShow) || 0;

		// if(options.A19aspz9llEil1vxnja!="ke12lodXB1235ZPXusu"){
		// 	justShow.value = 1;
		// 	uni.showModal({
		// 		content: '请入馆后扫码馆内二维码，否则不能预约！',
		// 		showCancel: false,
		// 		complete:()=>{
		// 		}
		// 	});
		// }

		commonStore.customers = []
		let today = dayjs().format('YYYYMMDD')
		getDetailByDateByExhibitionNo(data.exhibitionNo, SPUP_GLOBAL_CONFIG.orderType.exhibition, today).then((
			res) => {
				console.log("取得个人可预约时间段", res.data);
				let tmpObj = {};
				tmpObj[today] = res.data;
				rawReservDate = tmpObj;
				let dayLabelArr = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
				let tmpDates = []
				for (var date in rawReservDate) {
					let tmpDate = dayjs(date)

					tmpDates.push({
						date: tmpDate.date(),
						fullDate: date,
						day: today === date ? '今天' : dayLabelArr[tmpDate.day()],
						status: 1
					})
				}
				console.log("tmpDates:", tmpDates)
				dateClick(tmpDates[0])
				reserveDate.dates = tmpDates
			})
	})
</script>

<style scoped>
	@import url("../common/common.css");
	@import "./buyExhibition.css";
</style>
<style>
	page {
		background: #F7F9FA;
		padding-bottom: 86px;
	}
</style>