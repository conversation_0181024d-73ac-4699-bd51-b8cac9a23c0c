<template>
	<view class="content">
		<view class="complete">
			<!-- <exhibitionTypeTab v-model="exhibitionType" ></exhibitionTypeTab> -->
			<!-- 场馆文字介绍 -->
			<!-- 大模型的文字 -->
				<!-- <view class="pd-text">
					<text>宽32 m、高18 m（16：9）的超大展墙，设置国内唯一竖向多功能、超沉浸式的巨幅壁挂模型与国内首个最大尺度室内投影升降巨幕，通过物理模型与虚拟影像、全息激光秀相结合的手法，打造全沉浸式体验空间，展示“开放浦东 梦想之城”的浦东形象。</text>
				</view> -->
				
				<!-- 飞阅的文字 -->
				<view class="pd-text">
					<text>坐上悬空飞行器，身临其境翱翔于城市上空，“飞阅”大美浦东，开启超感官体验。</text>
				</view>
			<!-- 中部 -->
			<!-- 大模型的图 -->
			<!-- <image mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + '/exhibition/Scale-Model-Exhibition.png'" class="ex-img"></image> -->
			<!-- 飞阅的图 -->
			<image mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + '/exhibition/over-pd.png'" class="ex-img"></image>
			<!-- 大型展厅 按钮 -->
			<!-- <view class="fol-row bottom-line">
				<view class="fol-col">大模型厅 <br> Scale Model Exhibition</view>
				<view  style="text-align: right;"> 
				 <button class="ex-btn"  @click="goto('/pages/buyExhibition/buyExhibition')">大模型厅影片 >></button>
				</view>
			</view> -->
			<!-- 飞阅浦东 按钮 -->
			<view class="bottom-line">
				<view class="fol-row">
					<view class="fol-col size-medium">飞阅浦东 <br> Soaring Over Pudong</view>
					<view  style="text-align: right;"> 
					 <button class="ex-btn"  @click="pudongClick">预约体验 >></button>
					</view>
				</view>
				<view class="fol-row" style="margin: 12px 0;">
					<view class="note-box">定员：7名 </view>
					<view class="note-box">身高限制：110cm以下不得乘坐 </view>
				</view>
				<view class="note-box">位置：2F规划展示厅二 </view>
			</view>
			
			<!-- 须知文字 -->
			<view class="fol-row bottom-line" style="border-bottom: none;">
				 <view>*展项预约须知：
				<view >• 成功预约入馆参观门票并核验进场后即可根据当日可选时段进行预约体验，成功预约该展项后请提前10分钟到展项所在位置等候并听从现场工作人员指挥、对号入座。</view>
				<view>• 飞阅浦东展项实行线上预约制，同一证件号（身份证）每天最多预约2张展项体验票。因个人原因无法按时体验需至少提前10分钟取消预约，当月预约未取消爽约2次将在30天内无法再次预约。</view>
				</view>
				
			</view>
			<!-- 参观指南 -->
			<!-- <view class="fol-row bottom-line">
				<view class="left-btn size-normal">参观指南</view>
				<view style="text-align: right;">
					<view class="right-btn" @click="goto('/subpages/home/<USER>')" >点击查看 >></view>
				</view>
			</view> -->
			<!-- 个人中心 -->
			<!-- <view class="fol-row bottom-line">
				<view class="left-btn size-normal">个人中心｜PERSONAL</view>
				<view style="text-align: right;">
					<view class="right-btn" @click="goto('/pages/order/orderList')">点击查看 >></view>
				</view>
			</view> -->
			<view class="bottom-logo">
				<image mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + '/logo/logo.png'" ></image>
			</view>
		</view>
	</view>
	<uni-popup ref="popup" background-color="#fff" @change="change" :mask-click="false">
		<view class="popup-content">
			<view class="">
				<view class="flex-row justify-between">
					<view class="popup-title">飞阅浦东乘坐须知</view>
				</view>
				<view class="content">
					<view class="subTitle">符合下述状况的游客不能乘坐此设备：</view>
					<view class="noteTxt">
					1.患有高血压、心脏病、心血管疾病的人士<br>
					2.孕妇<br>
					3.患有颈椎病、脊椎病的人士<br>
					4.晕车、晕船人士<br>
					5.60岁以上人士<br>
					6.不能遵守设备的警告提示或者员工说明的人士<br>
					7.不能正常使用安全装置的人士<br>
					8.醉酒的人士<br>
					9.有过因受到光线刺激产生痉挛或丧失意识等经验的人士<br>
					</view>
					<view class="subTitle">乘坐时的注意事项：</view>
					<view class="noteTxt">
					1.禁止使用手机、照相机、摄影机等<br>
					2.由于振动机撞击而容易损坏或影响其使用功能的物品，请不要带入观影区内<br>
					3.抱小孩的乘客不宜乘坐此设备<br>
					4.观影前请吐出口中的糖果及口香糖等<br>
					5.禁止携带饮用水等液体<br>
					6.在观影中由于机器运转容易掉落或卷入设备的物品请事先放在规定区域<br>
					7.头发长的顾客请将头发系好
					</view>
				</view>
				<!-- 按钮 -->
				<view class="btns">
					<button class="btn-n" type="default" @click="rejectClick">不同意</button>
					<!-- <navigator style="margin: 0 5%;font-size: 16px;" class="btn-n" open-type="exit" target="miniProgram">不同意</navigator> -->
					<button :class="countdown>0?'btn-n':'btn-s'" type="default"
						@click="agreeClick">同意{{countdownLabel}}</button>
					<!-- <button class="btn-s" :type="default" @click="agreeClick">同意{{countdownLabel}}</button> -->
				</view>
			</view>
		</view>
	</uni-popup>
</template>

<script setup>
	import {ref,reactive,onMounted,computed,watch} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import dayjs from "dayjs";
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {getReserveTime, createExhibitionOrder} from "@/common/api/server.js";
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import exhibitionTypeTab from "./exhibitionTypeTab.vue"
	
	const exhibitionType = ref(SPUP_GLOBAL_CONFIG.exhibitionType.pudong)
	watch(exhibitionType, (newVal, oldVal) => {
		console.log("exhibitionType变化了：", newVal, oldVal);
	});
	
	//倒计时
	const countdown = ref(3)
	const countdownLabel = computed(() => {
		return countdown.value <= 0 ? '' : '(' + countdown.value + 's)'
	});
	const popup = ref();
	let timer;
	const rejectClick = () => {
		popup.value.close()
		clearTimer()
	}
	const agreeClick = () => {
		if(countdown.value<=0){
			popup.value.close()
			goto('/pages/buyExhibition/buyExhibition?justShow='+justShow+'&A19aspz9llEil1vxnja='+A19aspz9llEil1vxnja)
		}
	}
	const clearTimer = ()=>{
		if(timer){
			clearInterval(timer);
		}
	}
	const pudongClick = ()=>{
		// if(A19aspz9llEil1vxnja!="ke12lodXB1235ZPXusu"){
		// 	uni.showModal({
		// 		content: '请入馆后扫码馆内二维码，否则不能预约！',
		// 		showCancel: false,
		// 		complete:()=>{
		// 		}
		// 	});
		// 	return;
		// }
		countdown.value = 3
		popup.value.open("bottom")
		clearTimer()
		timer = setInterval(() => {
			if (countdown.value <= 0) {
				clearInterval(timer)
			} else {
				countdown.value--
			}
		}, 1000)
	}
	
	const goto = (url) => {
		uni.navigateTo({
			url: url
		})
	}
	let justShow = 0;
	let A19aspz9llEil1vxnja = "0";
	onLoad(options=>{
		justShow = options.justShow || 0;
		A19aspz9llEil1vxnja = options.A19aspz9llEil1vxnja || "0";
		// if(A19aspz9llEil1vxnja!="ke12lodXB1235ZPXusu"){
		// 	justShow = 1;
		// 	uni.showModal({
		// 		content: '请入馆后扫码馆内二维码，否则不能预约！',
		// 		showCancel: false,
		// 		complete:()=>{
		// 		}
		// 	});
		// }
	})
</script>

<style>
	page{
		background: #F7F9FA;
	}
	
</style>
<style lang="scss" scoped>
	@import '../common/common.css';
	@import 'exhibition-index.scss';
</style>