<template>
	<!-- 顶部选项卡 -->
	<view class="tabs">
		<view class="uni-tab-item" v-for="(item, index) in data" :key="index">
			<text class="uni-tab-item-title" :class="{tabActive: item.type==selectType}" @click="itemClick(item)">
				{{item.name}}
			</text>
			<view v-if="item.type==selectType" class="tab-item-title-line" ></view>
		</view>
	</view>
</template>
	<script setup>
		import { ref,reactive,onMounted,computed} from 'vue'
		import {useCommonStore} from '../../stores/common.js'
		import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
		const commonStore = useCommonStore();
		const props = defineProps(['modelValue']);
		const emit = defineEmits(['update:modelValue', 'itemClick']);
		const selectType = ref(0);
		const data = reactive( [
				{name: "大模型厅影片",type: SPUP_GLOBAL_CONFIG.exhibitionType.model},
				{name: "飞阅浦东",type: SPUP_GLOBAL_CONFIG.exhibitionType.pudong}
			]);
			
		const itemClick = (item) =>{
			if(item.type==SPUP_GLOBAL_CONFIG.exhibitionType.model){
				uni.showToast({
					icon:'none',
					title:'暂不开放'
				})
			}else{
				selectType.value = item.type;
				emit("update:modelValue", item.type);
			}
			
			// emit("itemClick", item.type);
		};
		
		onMounted(()=>{
			selectType.value  = props.modelValue;
		})

	</script>

<style scoped>
	@import '../common/common.css';
</style>
<style>
	.tabs{
		display: flex;
		flex: 1;
		flex-direction: row;
		overflow-x: scroll;
		height: 100%;
		/* margin: 0 15px; */
		border-bottom: 1px solid #000;
	}
	.uni-tab-item{
		width: 100%;
		white-space: nowrap;
		line-height: 100rpx;
		height: 100rpx;
		text-align: center;
		overflow: hidden;
	}
	.uni-tab-item-title{
		color:#000;
		font-size: 13px;
		width: 160rpx;
		display: inline-block;
		text-align:center;
	}
	.tabActive{
		font-size: 16px;
		font-weight: 500;
		color: #000;
	}
	.tab-item-title-line{
		display: block;
		border-bottom: 10rpx solid var(--button-color);
		border-top: 10rpx solid  var(--button-color);
		width: 90rpx;
		height:10rpx;
		line-height: 10rpx;
		margin: 0 auto;
		margin-top:-6px;
		background-color: var(--button-color);
		box-sizing: border-box;
	}
</style>