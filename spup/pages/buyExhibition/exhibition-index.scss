/* 上 */
.pd-text{
	line-height: 24px;
	text-indent: 26px;
	padding: 20px 0;
}
/* 中 */
.ex-img{
	border-radius: 8px;
}
/* 按钮 */
	.btn-box {
		display: flex;
		padding: 16px 0;
		border-bottom: 1px solid #000;
	}

	.btn-line {
		font-size: 14px;
		width: 100%;
		height: 36px;
		margin: 10px 6px;
		border-radius:0 25px 25px 0;
		background: none;
		border: 1px solid var(--button-solid-color);
	}
	.ex-btn{
		text-align: center;
		font-size: 13px;
		margin-top: 5px;
		background: none !important;
		border: 1px solid #000000;
		border-radius: var(--button-small-radius);
	}
	.bottom-line{
		border-bottom: 1px solid #000;
	}

	/*  */
	.complete {
		padding: 0 15px 10px 15px;
		position: relative;
		font-size: 14px;
		border-radius: 12px 12px 0 0;
	}

	.uni-noticebar[data-v-a1596656] {
		margin-top: 8px;
		padding: 3px 12px;
		border-radius: 5px;
		margin-bottom: 10px;
		background-color: #f2f2f2;
		color: #000;
	}

	.content-text {
		margin-top: 20px;
		border-radius: 8px;
		line-height: 32px;
		padding: 20px 8px;
		border: 1px solid #80616e;
		background-color: #f6f4f5;
	}

	.uni-line {
		border-bottom: 1px solid #cccccc;
	}
	
	
	
	/* // 底部 */
	
	.address-border{
		border-bottom: 1px solid #000;
	}
	.left-site{
		padding:15px 20px 16px 0;
	}
	.left-btn{
		flex: 1;
		padding-top: 4px;
	}
	.right-btn{
		text-align: center;
		font-size: 13px;
		width: 102px;
		height: 32px;
		line-height: 32px;
		border: 1px solid #000000;
		border-radius: var(--button-small-radius);
	}
	.bottom-logo{
		width: 100px;
		margin: auto;
		margin-top: 30px;
	  text-align: center;
	position: fixed;
	    bottom: 20px; /* 距离底部20px */
	    left: 0;
	    right: 0;
	}
	/* 当页面内容不足一屏时，按钮固定在底部并距离底部20px */
	@media (max-height: 800px) {
	  .bottom-logo {
	   margin-top: 30px;
	    left: 0;
	    right: 0;
	    margin: 0 auto;
		background: #F7F9FA;
		padding: 10rpx 300rpx;
		bottom: 0;
	  }
	}
	.bottom-line{
		padding: 16px 0;
		border-bottom: 1px solid #000;
	}
	.note-box{
		background: #e8ebeb;
		font-size: 13px;
		border: 1px solid #dee0e0;
		width: fit-content;
		padding: 2px 10px;
		border-radius: 5px;
		margin-right: 10px;
	}
	
	.popup-content {
		font-size: 14px;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100%;
		background-color: #fff;
		.content{
			padding: 0 15px 30px;
			overflow-y: auto;
		}
		.popup-title {
			width: 100%;
			text-align: center;
			margin: auto;
			font-size: 18px;
			font-weight: 500;
			color: rgba(0, 0, 0, 0.85);
			line-height: 24px;
			padding: 15px 0;
		}
		.subTitle{
			font-weight: 600;
			font-size:15px;
		}
		.btns{
			width: 100%;
			text-align: center;
			display: flex;
			padding: 10px 0;
		}
		.btn-n{	
			font-size: 14px;
			width: 40%;
			height: 36px;
			// margin: 10px 6px;
			border-radius: 25px;
			border: 1px solid #ccc;
			color: var(--button-color);
			background: #none;
		}
		.btn-s{
			font-size: 14px;
			width: 40%;
			height: 36px;
			// margin: 10px 6px;
			border-radius: 25px;
			border: 1px solid var(--button-color);
			color: var(--button-color);
			background: #F4F2F3;
		}
		
	}