<template>
	<view class="tips-box">
		<view class="tips-icon">
			<image mode="widthFix" :src="data.errorCode?'../../static/tips/tips-fail.png':'../../static/tips/tips-success.png'"></image>
		</view>
		<text class="size-medium">{{data.message}}</text>
		<view class="tips-span">
			<text>您可在“个人中心-我的预约”中查看</text>
		</view>


		<!-- 按钮 -->
		<view class="btn-box fol-row">
			<view class="fol-col">
				<button class="btn-n btn-box-width" @click="buyAgainClick">再次预约</button>
			</view>
			<view class="fol-col">
				<button class="btn-s btn-box-width" @click="seeOrderListClick">前往查看</button>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {ref,reactive,onMounted,computed,watch} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	
	const data = reactive({
		errorCode:0,
		message:"提交预约成功",
		category:0
	})
	
	const buyAgainClick = ()=>{
		uni.redirectTo({
			url: data.category==SPUP_GLOBAL_CONFIG.orderType.fypdTmpBatch ? "/pages/buyTmpBatch/buyExhibition?category="+data.category : "/pages/buyExhibition/buyExhibition"
		})
	}
	const seeOrderListClick = ()=>{
		uni.navigateTo({
			url: "/pages/order/orderList?bigType="+SPUP_GLOBAL_CONFIG.orderType.exhibition
		})
	}
	onLoad(options => {
		if(options.message){
			data.errorCode = 1
			data.message = options.message
		}
		if(options.category){
			data.category = options.category
		}
	})
</script>
<style scoped>
	@import "../../pages/common/common.css";
</style>
<style>
	page{
		background-color: #f7f9fa;
		font-weight: 400;
	}
	uni-button:after {
		    content:none;
		}
	
		/* 信息 */
		.tips-box {
			text-align: center;
			margin-top: 60px;
		}
	
		.tips-icon {
			width:132px ;
			margin: auto;
			padding-bottom: 10px;
		}
	
		.tips-span {
		font-size: 14px;
		margin-top: 10px;
		color: #999999;
		}
	
		/* 按钮 */
		.btn-box {
			margin: auto;
			width: 100%;
			margin-top: 30px;
		}
	    .btn-box-width{
			width: 130px;
			font-size: 13px;
			border-radius: var(--button-small-radius);
		}
		.btn-s {
			
			margin-left: 15px;
			color: var(--button-color);
			background: #fff;
			border: 1px solid var(--button-color);
		}
		.btn-n{
			color: #000;
			margin-right: 15px;
			background-color: #eef1f2;
			border: 1px solid #cccccc;
		}
	
		uni-button:after {
			content: none;
			width: 200%;
			height: 200%;
			position: absolute;
			top: 0;
			left: 0;
			border: 1px solid rgba(0, 0, 0, 0.2);
			transform: scale(0.5);
			transform-origin: 0 0;
			box-sizing: border-box;
			border-radius: 10px;
		}
</style>
