<template>
	<view>
		<image :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + '/banner/tic-bg_list.png'" style="width: 100%;height: 130px;"></image>
		<view class="complete">
			<!-- 信息 -->
			<view class="fol-row">
				<view class="fol-col">
					<view class="txt-color size-medium">
						<text>9:30 &nbsp;</text>
						<text>——</text>
						<text>&nbsp;16:30</text>
					</view>
					<view class="txt-color size-mini">
						<text style="padding-right: 60px;">开馆</text>
						<text>闭馆</text>
					</view>
				</view>
				<view style="text-align: center;padding:0 30px;;border-left: 1px dashed #26111a;">
					<image s:src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + '/exhibition/icon-notice.png" style="width: 18px;height: 18px;"></image>
					<view @click="goto('../notice/notice')">
						<text class="txt-color size-mini">参观须知</text>
					</view>

				</view>
			</view>
			<text class="txt-color size-mini" style="margin-top: 6px;">开放时间：每周一至周五（16:00停止检票）</text>
		</view>
		<view class="complete2">
			<view class="fol-row">
				<view class="fol-col" style="text-align: left;">选择时间</view>
			</view>
			<!-- 选择时间 -->
			<view class="fol-row">
				<view class="fol-col time-box-z">
					<text>10:00</text>
					<view class="span-number">剩余7人</view>
				</view>
				<view class="fol-col time-box-z">
					<text>11:00</text>
					<view class="span-number">已约满</view>
				</view>
				<view class="fol-col time-box-n">
					<text>14:00</text>
					<view class="span-number">剩余6人</view>
				</view>
				<view class="fol-col time-box-s">
					<text>15:00</text>
					<view class="span-number">剩余7人</view>
				</view>
			</view>
		</view>
		<view class="complete3">
			<text>
				<text style="color: #80616e;">*预约须知：</text>互动项目（飞阅浦东），在<text
					style="color: #80616e;">成功预约展馆参观</text>后，核验进场后扫描场馆内动态二维码，根据当日可选时段进行预约，选择项目体验场次。
			</text>
		</view>
		<!--从公众号打开不显示这部分内容 -->
		<!--从公众号打开不显示这部分内容 -->
		<!--从公众号打开不显示这部分内容 -->
		<!--从公众号打开不显示这部分内容 -->
		<!--从公众号打开不显示这部分内容 -->
		<view v-if="!data.justShow" class="complete2" style="margin-top: 10px;">
			<view class="fol-row">
				<view class="fol-col" style="text-align: left;">预约信息</view>
			</view>
			<!-- 预约人显示 -->
			<view class="information-text">
				<text class="size-regular">钱亦亦</text>
				<view class="size-span">
					<text>手机号: 189****7901</text>
				</view>
			</view>
			<!-- 列表 -->
			<button class="new-list" @click="goto('../ticket/ticket_list')">添加/删除预约人</button>
		</view>
		<!-- 按钮 -->
		<view class="btn-box">
			<button class="btn-s" @click="goto('../exhibition_appointment/exhibition_QR')">提交预约</button>
		</view>
		<!--从公众号打开不显示这部分内容 -->
		<!--从公众号打开不显示这部分内容 -->
		<!--从公众号打开不显示这部分内容 -->
		<!--从公众号打开不显示这部分内容 -->
		<!--从公众号打开不显示这部分内容 -->
	</view>
</template>

<script setup>
	import {ref,reactive,onMounted,computed,watch} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import dayjs from "dayjs";
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {getReserveTime} from "@/common/api/server.js";
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	
	const data = reactive({
		justShow:false
	})
	
	
	const goto = (url) =>{
		uni.navigateTo({
			url: url
		})
	}
	
	onLoad(options => {
		if(options.justShow){
			data.justShow = options.justShow
		}
		getReserveTime(SPUP_GLOBAL_CONFIG.orderType.exhibition).then((res) => {
			
		})
	})
	
</script>

<style>
	@import url("../common/common.css");
	@import "./buyExhibition0.css";
	
</style>
