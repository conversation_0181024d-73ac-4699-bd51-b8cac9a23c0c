/* 信息 */
	.complete {
		padding: 10px 15px;
		position: relative;
		margin: -30px 15px 0 15px;
		font-size: 14px;
		background: linear-gradient(to top, #f5eef1, #80616e);
	}

	/* 时间段 */
	.complete2 {
		padding: 10px 15px;
		margin: 0 15px;
		font-size: 14px;
		background: #ffffff;
	}

	.complete3 {
		padding: 10px 0 0;
		margin: 0 20px;
		font-size: 12px;
		line-height: 20px;
		color: #4c4c4c;
	}

	.time-box-s {
		border: 1px solid #80616e;
		padding: 2px 0;
		margin: 4px 6px;
		font-size: 14px;
		background-color: #efebec;
		color: #80616e;
		border-radius: 6px;
		text-align: center;
	}

	.time-box-n {
		padding: 2px 0;
		margin: 4px 6px;
		font-size: 14px;
		background-color: #efefef;
		color: #959595;
		border-radius: 6px;
		text-align: center;
	}

	.time-box-z {
		padding: 2px 0;
		margin: 4px 6px;
		font-size: 14px;
		background-color: #ffffff;
		border: 1px solid #b7b6ba;
		color: #000000;
		border-radius: 6px;
		text-align: center;
	}

	.span-number {
		white-space: nowrap;
		overflow-wrap: break-word;
		margin-top: -6px;
		font-size: 12px;
	}

	.time-box {
		border: 1px solid #b7b6ba;
		padding: 3px 0;
		margin: 4px 6px;
		background-color: #ffffff;
		color: #000000;
		border-radius: 6px;
		text-align: center;
	}

	.new-list {
		margin: 10px 0;
		background-color: #fff;
		font-size: 14px;
		border-radius: 2px;
		padding: 1px 0;
		color: #80616e;
		box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
	}

	.btn-box {
		width: 100%;
		display: flex;
		position: fixed;
		/* #ifdef H5 */
		left: var(--window-left);
		right: var(--window-right);
		/* #endif */
		bottom: 0;
	}

	.btn-s {
		width: 100%;
		font-size: 16px;
		padding: 5px 0;
		border-radius: 0;
		background: #80616e;
		color: #FFFFFF;
	}


	uni-button:after {
		content: none;
		width: 200%;
		height: 200%;
		position: absolute;
		top: 0;
		left: 0;
		border: 1px solid rgba(0, 0, 0, 0.2);
		transform: scale(0.5);
		transform-origin: 0 0;
		box-sizing: border-box;
		border-radius: 10px;
	}

	.information-text {
		background-color: #f6f4f5;
		border-radius: 6px;
		padding: 6px 10px;
		margin: 10px 0 15px 0;
	}