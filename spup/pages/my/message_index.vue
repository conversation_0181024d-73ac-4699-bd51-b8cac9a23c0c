<template>
	<view class="complete">
		<view class="head-title">我要留言</view>
		<view class="hr-box">
			<view class="hr-twill"></view>
			<view class="hr-edge-weak"></view>
		</view>
		<view>
			<!-- 参观目的 -->
			<view class="uni-column fol-row fol-row_box">
				<text class="uni-form-item__title">参观目的</text>
				<view class="uni-input-wrapper">
					<picker @change="purposePickerChange" :value="purpose.index" :range="purpose.array" range-key="name">
						<view class="uni-input fol-row">
							{{purpose.array[purpose.index].name}}
						</view>
					</picker>
				</view>
				<view style="text-align: right;">
					<uni-icons type="right" size="14"></uni-icons>
				</view>
			</view>
			<!-- 留言内容 -->
			<view class="easyinput-box">
				<textarea v-model="data.msg" placeholder-style="color:#ccc; font-size:13px" style="width: calc( 100vw - 50px);height: 260px; padding: 10px;font-size: 14px;" placeholder="请输入您的反馈或者建议，帮助我们更好的为您提供服务。"/>
				<!-- <uni-easyinput type="textarea"  v-model="value" placeholder="请输入您的反馈或者建议，帮助我们更好的为您提供服务。">
				</uni-easyinput> -->
			</view>
			<!-- 入馆时间 -->
			<view class="uni-column fol-row fol-row_box">
				<text class="uni-form-item__title">参观日期</text>
				<view class="uni-input-wrapper">
					<uni-datetime-picker type="date" v-model="data.visitDate">
						<view style="">{{data.visitDate}}</view> 
					</uni-datetime-picker>
				</view>
				<view style="text-align: right;">
					<uni-icons type="right" size="14"></uni-icons>
				</view>
			</view>
			<!-- 入馆时间 -->
			<view class="uni-column fol-row fol-row_box">
				<text class="uni-form-item__title">参观时间</text>
				<view class="uni-input-wrapper">
					<picker @change="visitTimePickerChange" :value="visitTime.index" :range="visitTime.array" range-key="name">
						<view class="uni-input fol-row">
							{{visitTime.array[visitTime.index].name}}
						</view>
					</picker>
				</view>
				<view style="text-align: right;">
					<uni-icons type="right" size="14"></uni-icons>
				</view>
			</view>
		</view>
	</view>
	<!-- 按钮 -->
	<view class="btn-box fol-row">
		<view class="fol-col"></view>
		<view class="btn-right-site complete">
			<button class="btn-s" @click="submitClcik">提交</button>
		</view>
	</view>
</template>
<script setup>
	import {ref,reactive,onMounted,computed} from 'vue'
	import {onShow,onHide} from '@dcloudio/uni-app';
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import {addContacts} from "@/common/api/server.js";
	import {comment} from "@/common/api/server.js";
	import dayjs from "dayjs";
	
	const data =  reactive({
			msg:"",
			visitDate:dayjs().format("YYYY-MM-DD")
		})
	const purpose = reactive({
  			array: [{
				name: '随便逛逛',
				id:1
  			},
			{
				name: '了解一下浦东新区的发展',
				id:2
			},
			{
				name: '了解具体的规划方案或实施政策 ',
				id:3
			},
			{
				name: '其它',
				id:3
			}
			],
  			index: 0,
  			multiIndex: [0, 1, 2],
  		}) 
		
	const purposePickerChange = (e) => {
		purpose.index = e.detail.value;
	}
	const visitTime = reactive({
				array: [{
					name: '09:30~13:00',
					id:1
				},
				{
					name: '13:00~16:00',
					id:2
				}
				],
				index: 0,
				multiIndex: [0, 1],
			}) 
	const visitTimePickerChange = (e) => {
		visitTime.index = e.detail.value;
	}
	
	const submitClcik = ()=>{
		if(!data.msg){
			uni.showToast({icon: "none",title:  '请输入您的留言'})
			return;
		}
		
		comment(purpose.array[purpose.index].name, data.msg, data.visitDate, visitTime.array[visitTime.index].name).then((res) => {
			if(res.code==0){
				uni.showModal({
					title: "提交成功",
					content: '感谢您的留言',
					showCancel: false,
					complete:()=>{
						uni.navigateBack({
							delta: 1
						});
					}
				});
			}
		})
	}
	
	const costomer =  reactive({
		name:"",
		phone: "",
		idcardCategory: 1,
		idcardNo : ""
	})
</script>

<style scoped>
	@import 'meassage_index.css';
	@import "../../pages/common/common.css";
	
</style>
<style>
	page{
		background-color: #f7f9fa;
	}
	/* 输入框默认样式修改 */
	.is-input-border {
		padding: 18px 8px 0 8px;
		line-height: 26px;
	    display: flex;
	    box-sizing: border-box;
	    flex-direction: row;
	    align-items: center;
	    border: none !important;
	    border-radius: 8px !important;
	}
</style>
