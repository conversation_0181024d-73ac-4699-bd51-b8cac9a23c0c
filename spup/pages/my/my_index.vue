<template>
	<view class="page-box">
		<!-- 背景 -->
		<image mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + '/my/self-bg.png'" style="width: 100%;"></image>
		<view class="page-content">
			<view class="fol-row page-head  head-name-top" style="margin: 20px 15px 0 15px;">
				<!-- 头像 -->
				<!-- <image :src="commonStore.userInfo.userAvatarSrc ? commonStore.userInfo.userAvatarSrc:SPUP_GLOBAL_CONFIG.mediaBaseUrl+'/my/uni.png'" class="head-img"></image> -->
				<view >
					<image mode="widthFix" :src="commonStore.userInfo.userAvatarSrc ? commonStore.userInfo.userAvatarSrc:SPUP_GLOBAL_CONFIG.mediaBaseUrl+'/my/uni.png'" style="width: 65px;border-radius: 50%;margin-top: 24px;"></image>
				</view>
				<!-- 名字 -->
				<view class="fol-col head-box">
					<!-- 名字 -->
					<text class="head-name">{{commonStore.userInfo.userName}}</text>
					<!-- 标签 -->
					<view class="fol-row" @click="goto('../my/my_contacts-edit')">
						<image :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + (commonStore.userInfo.phone ? '/my/my-badge-s.png' : '/my/my-badge-n.png')" class="badge-img"></image>
						<text class="head-txt">编辑资料</text>
						<uni-icons type="compose" size="16"></uni-icons>
					</view>
				</view>
				
			</view>
			<!-- 列表 -->
			<view class="bg-box">
				<view class="list-row" @click="goto('/pages/order/orderList')">
					<view>我的预约&nbsp;|&nbsp;MY RESERVATION</view><uni-icons type="right" size="16" color="#000"></uni-icons>
				</view>
				<view class="list-row" @click="goto('/pages/active/my_active_list')">
					<view>我报名的活动&nbsp;|&nbsp;MY ACTIVITIES</view><uni-icons type="right" size="16" color="#000"></uni-icons>
				</view>
				<view class="list-row" @click="goto('../my/volunteer_index')">
					<view>我的志愿服务&nbsp;|&nbsp;MY VOLUNTEER SERVICE</view><uni-icons type="right" size="16" color="#000"></uni-icons>
				</view>
				<view class="list-row" @click="goto('/pages/shop/goods_list?my=1')">
					<view>我的预定&nbsp;|&nbsp;MY BOOKING</view><uni-icons type="right" size="16" color="#000"></uni-icons>
				</view>
				<view class="list-row" @click="goto('../my/message_index')">
					<view>我要留言&nbsp;|&nbsp;LEAVE A MESSAGE</view><uni-icons type="right" size="16" color="#000"></uni-icons>
				</view>
			</view>

			<!-- <view class="bg-box" style="margin-top: 10px;">
				<uni-list-item :border="false" title="我的预约" showArrow to="/pages/order/orderList"
					:thumb="SPUP_GLOBAL_CONFIG.mediaBaseUrl+'/my/my-company.png'" thumb-size="sm" />
				<uni-list-item title="我的志愿服务" showArrow to="../my/volunteer_index"
					:thumb="SPUP_GLOBAL_CONFIG.mediaBaseUrl+'/my/my-volunteer.png'" thumb-size="sm" />
				<uni-list-item title="我要留言" showArrow to="../my/message_index"
					:thumb="SPUP_GLOBAL_CONFIG.mediaBaseUrl+'/my/my-message.png'" thumb-size="sm" />
				<uni-list-item title="我的单位" showArrow to="../my/company_index" :thumb="SPUP_GLOBAL_CONFIG.mediaBaseUrl+'/my/my-company.png'" thumb-size="sm" /> 
			</view> -->
		</view>
		<!-- 头部 -->
	</view>
</template>

<script setup>
	import {
		ref,
		onMounted,
		computed
	} from 'vue'
	import {
		SPUP_GLOBAL_CONFIG
	} from '@/common/config.js'
	import {
		useCommonStore
	} from '@/stores/common.js'
	const commonStore = useCommonStore();


	const goto = (url) => {
		uni.navigateTo({
			url: url
		})
	}
</script>
<style>
	page{
		background-color: #FAF9FA;
	}
</style>
<style lang="scss" scoped>
	@import url("../common/common.css");
	@import 'my_index.css';
</style>
