.complete {
	padding: 10px 15px;
}

.head-title{
	font-size: 18px;
	font-weight: 400;
	color: #000000;
}
.hr-box {
	position: relative;
	padding: 16px 0 0 0;
}

.hr-twill {
	position: absolute;
	border: 0;
	width: 45px;
	height: 6px;
	margin-top: -2px;
	margin-left: 13px;
	background: linear-gradient(-45deg, #000 25%, #F7F9FA 0, #F7F9FA 50%, #000 0, #000 75%, #F7F9FA 0);

	background-size: 8px 8px;
}

.hr-edge-weak {
	border: 0;
	width: 100%;
	height: 1px;
	background: #000;
}

/* 表单 */
.fol-row_box {
	font-size: 13px;
	border-bottom: 0.8px solid #000;
	margin-top: 15px;
	height: 47px;
	line-height: 47px;
	text-align: left;
	align-items: center;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.uni-input-wrapper {
	/* #ifndef APP-NVUE */
	display: flex;
	flex: 1;
	/* #endif */
	flex-direction: row;
	flex-wrap: nowrap;

}

.uni-column {
	text-align: left;
}

.uni-form-item__title {
	color: #000;
	width: 110px;
}

.uni-input {
	flex: 1;
}
/* 输入框 */
.easyinput-box{
	width: 100%;
	height: 283px;
	background: #F0F1F2;
	border-radius:var(--button-small-radius);
	margin-top: 30px;
}

/* 按钮 */
.btn-box {
	width: 100%;
	/* border-top: 1px solid #000;
	background: #F7F9FA; */
	padding-top: 10px;
	margin-top: 50px;
	display: flex;
	position: fixed;
	/* #ifdef H5 */
	left: var(--window-left);
	right: var(--window-right);
	/* #endif */
	bottom: 0;
}

.btn-right-site {
	text-align: right;
}

.btn-s {
	width: 94px;
	margin-bottom: 10px;
	/* padding: 2px 0; */
	font-size: 14px;
	color: #fff;
	background: var(--button-color);
	border-radius: var(--button-small-radius);
}
