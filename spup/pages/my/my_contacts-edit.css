.txt-color {
		color: #26111a;
	}
	.box{
		padding: 0 15px;
	}
	.size-span {
		font-size: 12px;
		color: #999999;
	}
	.bt-color{
		color: var(--bt-color);
	}
	.size-span2 {
		font-size: 14px;
		color: #999999;
	}
	.size-txt{
		overflow-wrap: break-word;
		white-space: nowrap;
	}
	uni-button:after {
		content:none;
	}
	.contacts-box {
		padding: 15px 15px 0 15px;
	}
	.add-title-hr{
		background: var(--button-color);
		height: 50px;
		text-align: center;
		line-height: 50px;
		width: 122px;
		font-size: 17px;
		font-weight: 500;
		color: #FFFFFF;
	}
.input-box{
	font-size: 14px;
	text-align: left;
}
	/* 表单 */
	.fol-row_box {
		font-size: 13px;
		border: 0.8px solid #000;
		margin-top: 15px;
		border-radius: var(--button-small-radius);
		height: 47px;
		line-height: 47px;
		text-align: left;
		align-items: center;
	}

	.uni-input-wrapper {
		/* #ifndef APP-NVUE */
		display: flex;
		flex: 1;
		/* #endif */
		flex-direction: column;
		flex-wrap: nowrap;
		overflow: hidden;
		text-overflow:ellipsis;
		white-space: nowrap;
		text-align: left;
	}
.uni-column{
	text-align: left;
}
	.uni-form-item__title {
		color: #000;
		width: 80px;
		padding-left: 10px;
	}

	.uni-input {
		flex: 1;
		display: block;
		font-size: 13px;
		line-height: 1.4em;
		height: auto ;
		min-height: 1.4em;
		overflow: hidden;
	}
.complete {
		padding: 10px 15px;
	}
	/* 按钮 */
	.btn-box {
		width: 100%;
		border-top: 1px solid #000;
		padding-top: 10px;
		display: flex;
		position: fixed;
		/* #ifdef H5 */
		left: var(--window-left);
		right: var(--window-right);
		/* #endif */
		bottom: 0;
		background-color: #f7f9fa;
	}
	.btn-right-site{
		text-align: right;
	}
	.btn-s {
		width: 94px;
		margin-bottom: 10px;
		/* padding: 3px 0; */
		font-size: 14px;
		color: #fff;
		background: var(--button-color);
		border-radius: var(--button-small-radius);
	}