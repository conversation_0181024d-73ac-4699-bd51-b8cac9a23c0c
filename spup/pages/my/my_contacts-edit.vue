<template>
	<view style="border-bottom: 0.8px solid #000;">
		<view class="add-title-hr"><text>完善资料</text></view>
	</view>
	
	<view class="contacts-box">
		<view class="uni-form-item uni-column fol-row fol-row_box">
			<text class="uni-form-item__title"><text class="bt-color">*</text>姓名</text>
			<view class="uni-input-wrapper">
				<input class="uni-input" type="text" v-model="costomer.name" placeholder="请输入姓名" />
			</view>
		</view>
		<view class="uni-form-item uni-column fol-row fol-row_box">
			<text class="uni-form-item__title"><text class="bt-color">*</text>手机号码</text>
			<view class="uni-input-wrapper">
				<input class="uni-input" type="number" v-model="costomer.phone" maxlength="11" placeholder="请输入手机号码" />
			</view>
		</view>
		<view class="uni-form-item uni-column fol-row fol-row_box">
			<text class="uni-form-item__title">证件类型</text>
			<view class="uni-input-wrapper">
				<picker mode="selector" @change="bindPickerChange" :value="idTypeIndex" :range="SPUP_GLOBAL_CONFIG.idType0" range-key="name">
					<view class="uni-input fol-row input-box">
						{{SPUP_GLOBAL_CONFIG.idType0[idTypeIndex].name}}
					</view>
				</picker>
			</view>
			<view style="text-align: right;padding-right: 10px;">
				<uni-icons type="right" size="14"></uni-icons>
			</view>
		</view>
		<view class="uni-form-item uni-column fol-row fol-row_box">
			<text class="uni-form-item__title"><text class="bt-color">*</text>证件号码</text>
			<view class="uni-input-wrapper">
				<input class="uni-input" type="idcard" v-model="costomer.idcardNo" placeholder="请输入证件号码" />
			</view>
		</view>
		<view class="uni-form-item uni-column fol-row fol-row_box">
			<text class="uni-form-item__title"><text class="bt-color">*</text>性别</text>
			<view class="uni-input-wrapper">
				<view class="">
					<radio-group @change="genderChaged">
						<label class="radio" style="margin-right: 30rpx;">
							<radio value="1" :checked="costomer.gender==1" color="var(--button-color)" style="transform:scale(0.7)"/>男
						</label>
						<label class="radio">
							<radio value="2" :checked="costomer.gender==2" color="var(--button-color)" style="transform:scale(0.7)"/>女
						</label>
					</radio-group>
				</view>
			</view>
		</view>
		<view class="uni-form-item uni-column fol-row fol-row_box">
			<text class="uni-form-item__title">职业</text>
			<view class="uni-input-wrapper">
			  <picker @change="jobChange" :value="selectJob.index" :range="data2.array" range-key="name">
				<view class="uni-input fol-row input-box">
					{{selectJob.name}}
				</view>
			  </picker>
			</view>
			<view style="text-align: right;padding-right: 10px;">
				<uni-icons type="right" size="14"></uni-icons>
			</view>
		</view>
	</view>
	<!-- 按钮 -->
	<view class="btn-box fol-row">
		<view class="fol-col" ></view>
		<view class="btn-right-site complete">
			<button class="btn-s" @click="okClick">确定</button>
		</view>
	</view>
	<!-- 按钮 -->
	<!-- <view class="btn-box">
		<button class="btn-s" @click="okClick">确认添加</button>
	</view> -->
</template>

<script setup>
	import {ref,reactive,onMounted,computed} from 'vue'
	import {onShow,onHide} from '@dcloudio/uni-app';
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import {saveCustomer} from "@/common/api/server.js";
	
	const selectJob =computed(() => {
		return data2.array.find(item=>item.id==costomer.job) || data2.array[0]
	});
	const idTypeIndex = ref(0)
	const bindPickerChange = (e)=>{
		idTypeIndex.value = e.detail.value;
		costomer.idcardCategory = SPUP_GLOBAL_CONFIG.idType0[idTypeIndex.value].id
	}
    const data2 = reactive({
  			array: [{
				name: '国家机关、党群组织、企业、事业单位负责人',
				id:1,
				index:0
  			},
			{
				name: '专业技术人员',
				id:2,
				index:1
			},
			{
				name: '办事人员和有关人员 ',
				id:3,
				index:2
			},
			{
				name: '商业、服务业人员',
				id:4,
				index:3
			},
			{
				name: '农、林、牧、渔生产人员',
				id:5,
				index:4
			},
			{
				name: '生产、运输设备操作人员及有关人员',
				id:6,
				index:5
			},
			{
				name: '军人',
				id:7,
				index:6
			},
			{
				name: '其他从业人员',
				id:8,
				index:7
			}
			],
  			multiIndex: [0, 0, 0],
  		}) 
	const costomer =  reactive({
		name:"",
		phone: "",
		idcardCategory: 1,
		idcardNo : "",
		gender: 0,
		job:""
	})
	const genderChaged = (e)=>{
		console.log("genderChaged:", e);
		costomer.gender = parseInt(e.detail.value)
	}
	const jobChange = (e)=>{
		console.log("jobChange:", e);
		costomer.job = data2.array[e.detail.value].id
	}
	
	const okClick = ()=>{
		if(!costomer.name){
			uni.showToast({icon: "none",title:  '请输入姓名'})
			return;
		}
		if(!/^[1][0-9]{10}$/.test(costomer.phone)){
			uni.showToast({icon: "none",title:  '请输入正确的手机号码'})
			return;
		}
		if(!costomer.idcardNo){
			uni.showToast({icon: "none",title:  '请输入证件号码'})
			return;
		}
		if(!costomer.gender){
			uni.showToast({icon: "none",title:  '请选择性别'})
			return;
		}
		saveCustomer(costomer.name, costomer.phone, costomer.idcardCategory, costomer.idcardNo, costomer.gender, costomer.job).then((res)=>{
			if(res.code==0){
				commonStore.userInfo.realName = costomer.name 
				commonStore.userInfo.phone = costomer.phone 
				commonStore.userInfo.cardCategory = costomer.idcardCategory 
				commonStore.userInfo.cardNo = costomer.idcardNo 
				commonStore.userInfo.userGender = costomer.gender
				commonStore.userInfo.job = costomer.job 
				uni.showToast({icon: "success",title:  '提交成功'})
				if(back==1){
					uni.navigateBack({
						delta: 1
					});
				}
			}
		})
	}
	
	let back = 0	//提交之后是否返回上一页
	onLoad(options => {
		if(options.back){
			back = options.back;
		}
		costomer.name = commonStore.userInfo.realName
		costomer.phone = commonStore.userInfo.phone
		costomer.idcardCategory = commonStore.userInfo.cardCategory
		for(let i in SPUP_GLOBAL_CONFIG.idType0){
			if(SPUP_GLOBAL_CONFIG.idType0[i].id==costomer.idcardCategory){
				idTypeIndex.value = i;
			}
		}
		costomer.idcardNo = commonStore.userInfo.cardNo
		costomer.gender = commonStore.userInfo.userGender
		costomer.job = commonStore.userInfo.job
		
		let jobObject = data2.array.find(item=>item.id==costomer.job)
		if(jobObject){
			jobObject
		}
	});
</script>

<style scoped>
	@import 'my_contacts-edit.css';
	@import "../../pages/common/common.css";
	
</style>
<style>
	page{
		background-color: #f7f9fa;
	}
</style>
