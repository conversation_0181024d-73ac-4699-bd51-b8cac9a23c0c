<template>
	<view class="complete">
		<view class="pd-title">浦东城市规划和公共艺术中心</view>
		<view class="fol-row site-hr">
			<view class="fol-col ">
				<view class="size-mini left-site ">
					<view>场馆位置：上海市浦东新区高科西路2499号</view>
					<view style="line-height: 17px;"><text>{{configStore.openTimeNative}}</text></view>
				</view>
			</view>
			<view class="right-site size-mini"><text style="text-decoration:underline; text-underline-offset:8px;" @click="goto('/subpages/home/<USER>')">参观指南</text></view>
		</view>
		<!-- 选择显示日期 -->
		<view class="fol-row time-hr">
			<view class="fol-col">
				<view class="size-regular time-left">
					<view>选择日期</view>
				</view>
			</view>
			<view class="right-time size-regular"><text>{{reserveDate.selectMonth}}月</text></view>
		</view>
		<view class="fol-row dates_wrap" style="border-bottom: 1px solid #000;padding-bottom: 5px;margin-bottom: 6px;">
			<template v-for="(item, index) in reserveDate.dates" :key="index" :itemData='item'>
				<view @click="dateClick(item)" 
				:class="dayjs(reserveDate.selectDate).format('YYYYMMDD')===item.fullDate? 'fol-col date-box date-box-s': 'fol-col date-box date-box-z'">
					<text class="size-regular">{{item.date}}</text>
					<view class="span-number">{{item.day}}</view>
				</view>
			</template>
		</view>
		
		<!-- 选择显示时间段 -->
		<view class="fol-row time-hr" style="padding: 10px 0;">
			<view class="fol-col">
				<view class="size-regular">
					<view>选择时间</view>
				</view>
			</view>
			<view class="size-regular"><text>{{year2Flag?'免预约入馆':ticketRemainingTxt}}</text></view>
		</view>
		<view class="date-content" >
			<template v-for="(item, index) in reserveDate.times" :key="index" :itemData='item'>
				<view @click="timeClick(item)" 
					:class="reserveDate.selectTime.id===item.id ? 'time-box time-box-s' : (item.batchStatus==1 && item.ticketRemaining>0? 'time-box' : 'time-box time-box-n')" style="margin-top: 10px;">
				{{item.batchStartTime.substr(0,2)}}:{{item.batchStartTime.substr(2,4)}}~{{item.batchEndTime.substr(0,2)}}:{{item.batchEndTime.substr(2,4)}}
				</view>
			</template>
			<view class="size-mini" style="color:#999;font-weight: 500;padding-top: 10px;">* 请在预约时段内入馆。</view>
		</view >
	</view>
	<view class="hr-edge-weak"></view>
	<view class="complete">
		<view class="fol-row time-hr" style="padding: 10px 0 10px 0;margin-bottom: 20px;">
			<view class="fol-col" style="text-align: left;line-height: 22px;">
				<view class="size-regular">添加预约人｜TICKET BUYER</view>
				<text class="size-mini" style="color:#999;">每次最多预约5人</text>
			</view>
			<view style="text-align: right;">
			  <button class="btn-add" @click="addCustomersClick">添加</button>
			</view>
		</view>
			<template v-for="(item, index) in commonStore.customers.filter(item=>item.checked)" :key="index" :itemData='item'>
				<view class="information-text">
					<text class="size-regular">{{formatName(item.name)}}</text>
					<view class="size-span">
						<text>手机号: {{item.phone.substr(0,3)}}****{{item.phone.substr(7,11)}}</text>
					</view>
				</view>
			</template>
	</view>	
	<view v-if="year2Flag">
		<view style="text-align:center">
			免预约入馆
		</view>
	</view>
	<view class="btn-box fol-row">
		<view class="fol-col" ></view>
		<view class="btn-right-site">
			<button class="btn-s" @click="submitClick">提交预约</button>
		</view>
			
	</view>

</template>

<script setup>
	import {ref,reactive,onMounted,computed,watch} from 'vue'
	import {onShow,onHide} from '@dcloudio/uni-app';
	import dayjs from "dayjs";
	import {useCommonStore} from '@/stores/common.js'
	import {formatName} from '@/common/myUtil.js'
	const commonStore = useCommonStore();
	import {configRefStore} from '@/stores/configRef.js'
	const configStore = configRefStore();
	import {getReserveTime, getAvailableTime, getDetailByDate} from "@/common/api/server.js";
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	
	//显示几天
	let showDateCount = 4;
	//可以预约的时间段
	const dateRang = reactive({start:"", end:""});
	const reserveDate = reactive({
		dates: [],
		selectDate: '',
		selectMonth: '',
		times: [],
		selectTime: {},
	})
	//两周年庆
	const year2Flag = computed(()=>reserveDate.selectDate=="2025-05-24"||reserveDate.selectDate=="2025-05-25")
	const goto = (url) =>{
		uni.navigateTo({
			url: url
		})
	}
	
	watch(()=>reserveDate.selectDate, (newValue, oldValue) => {
		console.log("reserveDate.selectDate变化 :", newValue);
		reserveDate.selectMonth = dayjs(newValue).month()+1
		getDetailByDate(SPUP_GLOBAL_CONFIG.orderType.person, dayjs(newValue).format("YYYYMMDD")).then(res=>{
			reserveDate.times = res.data
			timeClick({})
			for(let i=0;i<reserveDate.times.length; i++){
				let tmpItem = reserveDate.times[i]
				if(tmpItem.batchStatus==1 && tmpItem.ticketRemaining>0){
					timeClick(tmpItem)
					break;
				}
			}
		})
		
		if(reserveDate.dates.filter(item=>dayjs(item.fullDate).format("YYYY-MM-DD")==newValue).length==0){
			console.log("需要刷新【选择日期】中的日期");
			let today = dayjs().format('YYYYMMDD')
			let dayLabelArr = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
			let tmpDates = []
			let startIndex=0, endIndex=showDateCount-1; 
			for(let i=0; i<rawReservDateList.length; i++){
				if(newValue==dayjs(rawReservDateList[i].day).format('YYYY-MM-DD')){
					startIndex = i;
					break;
				}
			}
			endIndex = startIndex+showDateCount-1;
			if(endIndex>=rawReservDateList.length){
				endIndex=rawReservDateList.length-1;
				startIndex = endIndex-showDateCount+1;
			}
			for(let index=startIndex; index<=endIndex; index++){
				let tmpDate = dayjs(rawReservDateList[index].day)
				tmpDates.push({date:tmpDate.date()
							, fullDate : tmpDate.format('YYYYMMDD')
							, day: today === tmpDate.format('YYYYMMDD') ? '今天' : dayLabelArr[tmpDate.day()]
							, isWorkday : rawReservDateList[index].isWorkday
							, dayRemark : rawReservDateList[index].dayRemark
							})
			}
			reserveDate.dates = tmpDates
		}
	})
	//点击日期
	const dateClick = (item)=>{
		reserveDate.selectDate = dayjs(item.fullDate).format("YYYY-MM-DD")
	}
	//点击时间
	const timeClick = (item)=>{
		if(item.id && (item.batchStatus==0 || item.ticketRemaining<=0) ){
			uni.showToast({
				icon: "none",
				title: item.ticketRemaining<=0 ? '本时段预约已满，请选择其他场次' : '本时段不可预约，请选择其他场次'
			})
		}else{
			reserveDate.selectTime = item
		}
	}
	//余票数量
	const ticketRemainingTxt = computed(()=>{
		// if(reserveDate.dates.filter(item=>reserveDate.selectDate==dayjs(item.fullDate).format("YYYY-MM-DD")&&!item.isWorkday).length>0){
		// 	return "馆休"
		// }
		let sItem = reserveDate.dates.find(item=>reserveDate.selectDate==dayjs(item.fullDate).format("YYYY-MM-DD"));
		console.log("sItem:", sItem);
		if(sItem&&sItem.dayRemark&&!sItem.isWorkday){
			return sItem.dayRemark;
		}
		return "剩余票数："+ (reserveDate.selectTime.ticketRemaining>0?reserveDate.selectTime.ticketRemaining:"*")
	})
	//点击添加按钮
	const addCustomersClick = ()=>{
		if(year2Flag.value){
			uni.showToast({
				icon: "none",
				title: '免预约入馆'
			})
			return;
		}
		goto('/subpages/buy/customerList?maxCount=5&orderType='+SPUP_GLOBAL_CONFIG.orderType.person)
	}
	//点击提交预约按钮
	const submitClick = ()=>{
		if(year2Flag.value){
			uni.showToast({
				icon: "none",
				title: '免预约入馆'
			})
			return;
		}
		if(!reserveDate.selectTime.id){
			uni.showToast({
				icon: "none",
				title: '请先选择预约时间'
			})
			return;
		}
		if(commonStore.customers.filter(item=>item.checked).length<1){
			uni.showToast({
				icon: "none",
				title: '请先添加预约人'
			})
			return;
		}
		commonStore.buyData.time = reserveDate.selectTime
		uni.navigateTo({
			url: '/subpages/buy/buyConfirm'
		})
	}
	
	let rawReservDateList = {}
	onMounted(()=>{
		commonStore.customers = []
		getAvailableTime(SPUP_GLOBAL_CONFIG.orderType.person).then(res=>{
			showDateCount = res.data.pageShowDays
			rawReservDateList = res.data.dayList;
			dateRang.start =  dayjs(rawReservDateList[0].day).format('YYYY-MM-DD');
			dateRang.end =  dayjs(rawReservDateList[rawReservDateList.length-1].day).format('YYYY-MM-DD');
			reserveDate.selectDate = dateRang.start;
		})
	})

</script>

<style scoped>
	@import url("../common/common.css");
	@import "./buyPerson.css";
</style>
<style>
	page{
		background:#f7f9fa;
		padding-bottom: 76px;
	}
</style>
