<template>
	<!-- 预约信息 -->
	<view class="information-box fol-row">
		<view class=" info-text">
			<view class="top-title">{{data.detail.exhibitionTitle}}</view>
			<view class="size-mini"><text>地点：</text><text class="bold">{{data.detail.exhibitionAddress}}</text></view>
			<view class="size-mini"><text>展期：</text><text class="bold">{{data.detail.exhibitionStartDate}} 至 {{data.detail.exhibitionEndDate}}</text></view>
		</view>
	</view>
	<view class="complete">
	<view class="fol-row size-regular time-border">
		<view>
			<text>入馆时间</text>
		</view>
		<view class="fol-col" style="text-align: right;">{{dayjs(commonStore.buyData.time.batchDate).format('YYYY-MM-DD') }} &nbsp;{{commonStore.buyData.time.batchStartTime.substr(0,2)}}:{{commonStore.buyData.time.batchStartTime.substr(2,4)}}~{{commonStore.buyData.time.batchEndTime.substr(0,2)}}:{{commonStore.buyData.time.batchEndTime.substr(2,4)}}</view>
	</view>
	<view class="size-regular" style="line-height: 32px;">
		<template v-for="(item, index) in commonStore.customers.filter(item=>item.checked)" :key="index" :itemData='item'>
			<view class="customer_wrap">
				<view class="fol-row">
					<view class="fol-col">姓名</view>
					<view class="fol-col" style="text-align: right;">{{item.name}}</view>
				</view>
				<view class="fol-row">
					<view class="fol-col">手机号</view>
					<view class="fol-col" style="text-align: right;">{{item.phone}}</view>
				</view>
				<view class="fol-row">
					<view class="fol-col">证件号</view>
					<view class="fol-col" style="text-align: right;">{{item.idcardNo}}</view>
				</view>
			</view>
		</template>
	</view>
	</view>
	<!-- 按钮 -->
	<view class="btn-box fol-row">
		<view class="fol-col" >
			<view class="size-regular">
				<text>合计：</text><text>免费</text>
			</view>
		</view>
		<view class="btn-right-site complete">
			<button class="btn-s" @click="confirmClick">确认预约</button>
		</view>
	</view>
<!-- 按钮 -->
	<!-- <view class="btn-box">
		<button class="btn-s" @click="confirmClick">确认预约</button>
	</view> -->
</template>

<script setup>
	import {ref,reactive,onMounted,computed} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import dayjs from "dayjs";
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {getTmpExhibitionDetail, createTmpExhibitionOrder} from "@/common/api/server.js";
	
	const data = reactive({
		detail:{},
		exhibitionNo:0
	})
	
	const confirmClick = ()=>{
		let batchNo = commonStore.buyData.time.batchNo
		let contacts = commonStore.customers.filter(item=>item.checked)
		uni.showLoading({
		    title: "提交中…",
		    mask: true
		});
		createTmpExhibitionOrder(batchNo, contacts, data.exhibitionNo, SPUP_GLOBAL_CONFIG.orderType.tmpExhibition).then((res) => {
			uni.hideLoading()
			console.log("下单返回：", res);
			if(res.code===0){
				uni.navigateTo({
					url: "/subpages/buy/buyOk"
				})
			}else{
				uni.navigateTo({
					url: "/subpages/buy/buyOk?message=" + (res.message||"预约失败")
				})
			}
		}).catch(res=>{
			uni.hideLoading()
		});
	}
	onLoad(options => {
		//临展基本信息
		data.exhibitionNo = options.exhibitionNo
		getTmpExhibitionDetail(options.exhibitionNo).then(res=>{
			data.detail = res.data;
		})
		
	})
	
	
</script>
<style>
	page{
		background: #f7f9fa;
	}
</style>
<style scoped >
	@import "./tmpExhibition_buy_confirm.css";
	@import "../../pages/common/common.css";
	
</style>
