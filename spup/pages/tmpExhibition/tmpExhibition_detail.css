.content-box{
		padding: 0 15px 86px 15px;
	}
	.txt-title{
		font-size: 18px;
		font-weight: 500;
		padding: 10px 0 10px 0;
	}
	.bold{
		font-weight: 500;
		
	}
	.left20{
		padding-left:20px;
	}
	.top-con{
		border-bottom: 1px dashed #000;padding-bottom: 20px;
	}
	.padding20{
		padding: 10px 0 10px 0;
	}
	/* 按钮 */
	.btn-box {
		display: flex;
		padding: 16px 0 16px 0;
		border-bottom: 1px dashed #000;
	}

	.btn-line {
		font-size: 14px;
		width: 100%;
		height: 36px;
		margin: 10px 6px;
		border-radius:25px;
		background: none;
		border: 1px solid var(--button-solid-color);
	}
	.btn-sincere{
		font-size: 14px;
		width: 100%;
		height: 36px;
		margin: 10px 6px;
		border-radius:25px;
		border: 1px solid var(--button-color);
		color:var(--button-color);
		background: #F4F2F3;
	}