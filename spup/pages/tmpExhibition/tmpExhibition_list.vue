<template>
	<!-- <view class="msg-wrap"><text class="msg">{{commonStore.lifeMsg}}</text></view> -->
	<!-- 头部标题 -->
	<view class="content top20">
		<text class="pd-title">展览预约</text>
		<view class="hr-box">
			<view class="hr-twill"></view>
			<view class="hr-edge-weak"></view>
		</view>
	</view>
	<!-- 筛选tab -->
	<!-- <TmpExhitionTypeTab @typeChange="typeChange"></TmpExhitionTypeTab> -->
	
	<!-- 内容list -->
	<view class="content">
		<view class="goods-list-wrap" >
			<!-- 板块名称-常设展 -->
			 <view class="title-container">
			    <view class="title">常设展</view>
			    <image mode="widthFix" class="decoration" src="../../static/title-set.png"></image> 
			  </view>
			<!-- 内容 -->
			<view class="goods-item-wrap" style="display: flex;padding-bottom: 36px;" @click="goDetail()">
				<image  mode="widthFix" class="cover-img" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + SPUP_GLOBAL_CONFIG.permanentExhibition.detailBannerPic"></image>
				<view class="goods-title size-medium item-right-wrap">
					<text class="common-title-skin">{{SPUP_GLOBAL_CONFIG.permanentExhibition.exhibitionTitle}}</text>
				</view>
			</view>
			<!-- 分割线 -->
			<view style="border-top: 1px dashed #000;">&nbsp;</view>
			<!-- 板块名称-临时展览 -->
			 <view class="title-container">
				<view class="title">特展</view>
				<image mode="widthFix" class="decoration" src="../../static/title-set.png"></image> 
			  </view>
			<!-- 内容 -->
			<template v-if="listData.length">
				<template v-for="(itemData, index) in listData" :key="index" :itemData='itemData'>
					<view class="goods-item-wrap" style="display: flex;padding-bottom: 36px;" @click="goDetail(itemData.exhibitionNo)">
						<image mode="widthFix" class="cover-img" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + itemData.detailBannerPic"></image>
						<view class="goods-title size-medium item-right-wrap">
							<text class="common-title-skin">{{itemData.exhibitionTitle}}</text>
						</view>
					</view>
				</template>
			</template>
			<view class=" goods-list-empty" v-else>
				{{data.init?"数据加载中~":"暂无"}}
			</view>
		</view>
		
	</view>
	<view class="myOrderBtn-wrap" @click="showMyOrder" v-if="data.plant=='H5-SSB'">
		我的预约
	</view>
</template>

<script setup>
	import {ref,reactive,onMounted,computed,watch} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import dayjs from "dayjs";
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {getTmpExhibitionList} from "@/common/api/server.js";
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import TmpExhitionTypeTab from "./tmpExhition_type_tab.vue"
	
	const data = reactive({
		init:true,
		list:[//{id:99999, exhibitionTitle:"浦东城市规划展 (常设展)", detailBannerPic:"/tmpExhibition/99999.png", type:SPUP_GLOBAL_CONFIG.orderType.tmpExhibition},
			// {id:1, exhibitionTitle:"中国 (上海自由贸易试验区）10年改革建设成就展", detailBannerPic:"/tmpExhibition/1.png"},
			// {id:2, exhibitionTitle:"test", detailBannerPic:"/tmpExhibition/2.png"},
		],
		plant: SPUP_GLOBAL_CONFIG.plant
	})
	
	const goDetail = (exhibitionNo)=>{
		if(!exhibitionNo){
			uni.navigateTo({
				url: "/pages/home/<USER>"
			})
		}else{
			uni.navigateTo({
				url: "/pages/tmpExhibition/tmpExhibition_detail?exhibitionNo="+exhibitionNo
			})
		}
	}
	
	const showMyOrder = ()=>{
		uni.navigateTo({
			url: "/pages/order/orderList"
		})
	}
	
	const listData = computed(() => {
		return data.list
	});
	
	
	//暴露给child的方法
	const typeChange = (childItemData)=>{
		console.log(childItemData);
	}
	
	
	onLoad(options=>{
		getTmpExhibitionList().then(res=>{
			data.init = false
			data.list = [...res.data]
		})
	})
</script>

<style>
	.title-container {
	  text-align: center;
	  margin-bottom: 28px;
	}
	
	.title {
	  font-size: 15px;
	}
	
	.decoration {
	  max-width: 100px; /* 装饰图片的最大宽度，根据需要调整 */
	  display: block;
	  margin: 0 auto; /* 图片水平居中 */
	  margin-top: -15px;
	}
	
	page{
		background: #F7F9FA;
	}
	.content{
		padding: 0 15px;
	}
	.goods-list-wrap{
		/* display: flex; 
		flex-wrap: wrap; */
		padding: 8px 0;
	}
	.goods-list-empty{
		color: #9e9e9e;
		text-align: center;
	}
	.goods-item-wrap{
		/* flex: 1; */
		/* padding-left: 8px; */
	}
	.goods-img {
		width: 100%;
		/* height: 200px; */
		border-radius: 3px;
	}
	.goods-title{
		text-align: left;
		font-size: 14px;
		font-weight: 500;
		padding: 4px 0 20px 0;
	}
	/* // 上部 */
	.top20{
		margin-top: 20px;
	}
	.pd-title{
		font-size: 17px;
	}
	.hr-box{
		position: relative;
		padding: 16px 0;
	}
	.hr-twill{
		position: absolute;
		border: 0;
		width: 30px;
		height: 6px;
		margin-top: -2px;
		margin-left: 20px;
		background: linear-gradient(-45deg, #000 25%, #F7F9FA 0, #F7F9FA 50%, #000 0, #000 75%, #F7F9FA 0);
		
		background-size: 8px 8px;
	}
	.hr-edge-weak{
		border: 0;
		width: 100%;
		height: 1px;
		background: #000;
	}
	.pd-text{
		text-indent: 2em;
		font-size: 13px;
		line-height: 24px;
		letter-spacing:0.2px;
		padding-bottom: 17px;
		border-bottom: 1px solid #000;
	}
	.item-right-wrap{
		padding-left: 15px;
		flex:1
	}
	.cover-img{
		flex:1;
		border-radius: 2px;
	}
	
	.msg-wrap{
		overflow: auto;
	}
	.msg{
		width:80%;
		display:inline-block;
		white-space: pre-wrap; 
		word-wrap: break-word;
		height: auto;
	}
	.myOrderBtn-wrap{
		bottom: 42px;
		right: 20px;
		position: fixed;
		text-align: center;
		color: #fff;
		font-weight: 500;
		font-size: 14px;
		width: 40px;
		height: 40px;
		background: #8d5b6e;
		padding: 10px;
		border-radius: 44px;
		line-height: 20px;
	}
</style>