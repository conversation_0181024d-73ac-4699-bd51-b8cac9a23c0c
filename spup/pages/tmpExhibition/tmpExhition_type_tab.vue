<template>
	<view class="tabs-wrap">
	<template v-for="(itemData, index) in data.types" :key="index" :itemData='itemData'>
		<view :class="index==data.curIndex?'tab active-tab':'tab'" @click="itemClick(index)">{{itemData.name}}</view>
	</template>
	</view>
</template>

<script setup>
	import {ref,reactive,onMounted,computed,watch} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import dayjs from "dayjs";
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {getShopGoodsList} from "@/common/api/server.js";
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	const emit = defineEmits(['typeChange']);
	
	const data = reactive({
		curIndex: 0,
		types: [{name:'全部', id:1},
				// {name:'今日', id:2},
				// {name:'本周', id:3},
				// {name:'本月', id:4},
				],
	})
	
	
	const itemClick = index=>{
		data.curIndex = index
		emit("typeChange", data.types[index]);
	}
</script>

<style>
	.tabs-wrap{
		margin: 10px 10px 15px 5px;
		text-align: center;
		display: flex;
	}
	.tab{
		padding: 1.5px 15px;
		margin: 0 6px;
		color: #000;
		border: 0.7px solid #000;
		border-radius: 7px;
	}
	.active-tab{
		color: #fff ;
		background-color: #815462;
		border: none;
		border-radius: 7px;
	}
</style>