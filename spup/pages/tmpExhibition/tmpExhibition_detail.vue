<template>
	<view class="content-box">
		<view style="margin-top:15px">
			<image mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl+data.detail.detailBannerPic"></image>
		</view>
		<view class="top-con">
			<view class="txt-title">{{data.detail.exhibitionTitle}}</view>
			<view class="size-normal"><text>地点</text> <text class="left20">{{data.detail.exhibitionAddress}}</text></view>
			<view class="size-normal"><text>展期</text> <text class="left20">{{data.detail.exhibitionStartDate}} {{!data.detail.exhibitionEndDate?'至今':' 至 '+data.detail.exhibitionEndDate}}</text></view>
		</view>
		<!-- 按钮 -->
		<!-- <view class="btn-box" style="display:block"> -->
		<view class="btn-box" v-if="data.exhibitionNo=='L20250326151436123'">
			<button class="btn-line" @click="buyClick(1)">团体预约</button>
			<button class="btn-sincere" @click="buyClick(0)">个人预约</button>
		</view>
		<div v-if="data.exhibitionNo!='L20250326151436123'">
			<view style="font-size:17px;margin-bottom: 10px; margin-top: 10px;">预约方式</view>
			<view>入馆后无需预约，自行参观</view>
		</div>
		<view class="size-medium bold padding20"></view>
		<view class="size-normal">
			<view style="font-size:17px;margin-bottom: 10px;">展览简介</view>
			<rich-text :nodes="data.detail.exhibitionContent"></rich-text>
		</view>
	</view>
<!-- 	<view class="btn-box fol-row">
		<view class="btn-right-site">
			<button class="btn-s" @click="buyClick(1)">团体预约</button>
		</view>
		<view class="btn-right-site">
			<button class="btn-s" @click="buyClick(0)">个人预约</button>
		</view>
	</view> -->
</template>

<script setup>
	import {ref,reactive,onMounted,computed,watch} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import dayjs from "dayjs";
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {getTmpExhibitionDetail} from "@/common/api/server.js";
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	// import TmpExhitionTypeTab from "./tmpExhition_type_tab.vue"
	
	const data = reactive({
		exhibitionNo:0,
		detail:{
			detailBannerPic: "",
			exhibitionAddress: "",
			exhibitionContent: "",
			exhibitionEndDate: "",
			exhibitionNo: "",
			exhibitionStartDate: "",
			exhibitionTitle: "",
			id: 0,
			thumbnailPic: ""
		}
	})
	
	
	const buyClick = (isTeam) => {
		let url = "/pages/tmpExhibition/tmpExhibition_buy?exhibitionNo="+data.exhibitionNo
		if(isTeam==1){
			url = "/pages/tmpExhibition/tmpExhibition_buyTeam?exhibitionNo="+data.exhibitionNo
		}else{
			uni.showToast({
				icon: "none",
				title: data.exhibitionNo=='L20250326151436123' ?'入馆后无需预约':'静待开放'
			})
			return 
		}
		uni.navigateTo({
			url: url
		})
	}
	
	onLoad(options => {
		console.log("options:", options);
		if(options.exhibitionNo){
			data.exhibitionNo = options.exhibitionNo
		}
		
		if(options.scene){	//如果用户扫带参数的二维码
			function getQueryString(name) {
			    var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
			    var r = decodeURIComponent(options.scene).match(reg);
			    if (r != null) {
			        return unescape(r[2]);
			    }
			    return null;
			}
			//服务员ID
			let exhibitionNo = getQueryString("exhibitionNo")
			if(exhibitionNo){
				data.exhibitionNo = exhibitionNo
			}
		}
		getTmpExhibitionDetail(data.exhibitionNo).then(res=>{
			data.detail = res.data;
		})
	})
	
</script>

<style>
	page {
		background-color:#f7f9fa;
	}
	
</style>
<style scoped lang="scss">
	@import url("../common/common.css");
	@import 'tmpExhibition_detail.css';
</style>