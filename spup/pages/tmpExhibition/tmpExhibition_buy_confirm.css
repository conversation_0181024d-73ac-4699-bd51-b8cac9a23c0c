uni-button:after {
	content: none;
}
.information-box {
	padding: 20px 15px 20px 15px;
	border-bottom: 1px solid #000;
}
.info-text{

}
.complete {
	padding: 0 15px;
}

.top-title {
	font-size: 17px;
	font-weight: 500;
	padding-bottom: 5px;
}

.time-border {
	border-bottom: 1px solid #000;
	padding: 17px 0;
}

.card-box {
	background-color: #f7f7f7;
	padding: 15px 20px 15px 20px;
	border-radius: 5px;
	margin-top: 10px;
}

/* 按钮 */
.btn-box {
	width: 100%;
	border-top: 1px solid #000;
	display: flex;
	position: fixed;
	padding: 20px 15px 10px 15px;
	/* #ifdef H5 */
	left: var(--window-left);
	right: var(--window-right);
	/* #endif */
	bottom: 0;
	background: #fff;
	font-weight: 400;
}

.btn-right-site {
	text-align: right;
}

.btn-s {
	width: 94px;
	margin-bottom: 10px;
	margin-right: 15px;
	/* padding: 3px 0; */
	font-size: 14px;
	color: #fff;
	background: var(--button-color);
	border-radius: var(--button-small-radius);
}

.customer_wrap {
	padding: 10px 0;
	border-bottom: 1px solid #000;
}

.customer_wrap:last-child {
	border-bottom: none;
}
.bold{
	font-weight: 500;
}