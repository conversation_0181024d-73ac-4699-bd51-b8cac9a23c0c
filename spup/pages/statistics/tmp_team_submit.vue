<template>
	<view>
		<uni-popup ref="popup" :mask-click="false">
			<view class="popup-content">
				<view class="title-name"><text style="color:#be1b1b;">*</text>接待人</view>
				<input class="prompt-input" type="text" @input="_input" :value="data.receptioner"/>
				<view class="btn-wrap">
					<view style="flex: 1;"></view>
					<view style="display: flex;">
						<button class="btn btn-no" type="primary" @click="okClick(0)">取消</button>
						<button class="btn btn-ok" type="primary" @click="okClick(1)">确认</button>
					</view>
					
				</view>
			</view>
			
		</uni-popup>
	</view>
</template>

<script setup>
	import {ref,reactive,watch,computed,onMounted} from 'vue'
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import {useCommonStore} from '@/stores/common.js';
	const commonStore = useCommonStore()
	import {tmpExhibitionTeamConfirmReception} from "@/common/api/server.js";
	const props = defineProps(['visible', 'orderData']);
	const emit = defineEmits(['update:visible']);
	
	const data = reactive({
		receptioner:""
	})
	
	//弹窗
	const popup = ref();
	//通过computed实现父子组件的属性双向绑定
	const visible = computed({
		// 子组件v-model绑定 计算属性, 一旦发生变化, 就会给父组件传递值
		get: () => props.visible,
		set: (nv) => {
			emit('update:visible', nv)
		}
	})
	const change = (e) => {
		visible.value = e.show;
	};
	watch(visible, (value) => {
		if (value) {
			popup.value.open("center");
		} else {
			popup.value.close();
		}
	});
	
	const _input = (e)=>{
		//将参数传出去，这样在getInput函数中可以通过e去获得必要的参数
		//this.triggerEvent("getInput",e.detail);
		data.receptioner = e.detail.value;
	}
	
	
	const okClick = (yes)=>{
		if(yes){
			if(!data.receptioner){
				uni.showToast({
					icon: "none",
					title: '请输入接待人'
				})
				return
			}
			uni.setStorageSync('tmpExhibitionTeamConfirmReceptioner', data.receptioner);
			tmpExhibitionTeamConfirmReception(props.orderData.id, data.receptioner, SPUP_GLOBAL_CONFIG.orderStatus.completed).then(res=>{
				if(res.code==0){
					props.orderData.orderStatus = SPUP_GLOBAL_CONFIG.orderStatus.completed
					visible.value = false;
				}
			})
		}else{
			visible.value = false;
		}
	}
	onMounted(()=>{
		data.receptioner = uni.getStorageSync('tmpExhibitionTeamConfirmReceptioner');
	})
</script>

<style lang="scss" scoped>
	.popup-content {
		font-size: 14px;
		// width: 100%;
		// height: 100%;
		padding: 20px 20px;
		background: #fff;
		border-radius: 10px;
		margin: 0 35px;
		.title-name{
			font-size: 15px;
			font-weight: 500;
		}
		input{
			border: 1px solid #305096;
			width: 240px;
			height: 38px;
			border-radius: 5px;
			margin-top: 10px;
			padding: 0 15px;
		}
		.shop-item-wrap{
			padding: 20px 15px;
			border-radius: 5px;
			border: 1px solid #e5e7ee;
			margin-top: 20px;
			.shop-name{
				flex: 1;
				font-size: 15px;
				font-weight: 500;
			}
			.shop-addr{
				font-size: 14px;
				margin-top: 15px;
				color:#999;
			}
		}
		.shop-item-active{
			padding: 20px 15px;
			border-radius: 5px;
			border: 1px solid var(--text-solely);
		}
		
		.btn-wrap{
			margin-top: 20px;
			display: flex;
			button {
				border: none !important;;
			}
			.btn{
				font-size: 14px;
				color: #000;
				padding: 0 20px;
				border-radius:4px;
			}
			.btn-no{
				color:#666666;
				background:#e5e5e5;
			}
			.btn-ok{
				margin-left: 20px;
				color: #fff;
				background:#305096;
			}
		}
	}
	radio{
		background: transparent !important;
	}
</style>