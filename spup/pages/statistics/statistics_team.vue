<template>
<view class="content">
	 <view>
		 <view class="col-fow">
			 <view class=" info-title">{{data.orderType=="tmp"?'特展团队预约':'团队预约'}}</view>
		 </view>
	    <view class="info-box" v-if="data.list.length">
<!-- 	        <view class="col-fow">
	            <view class="card-title">今日预约团队：</view>
	            <view class="col-fl">{{data.list.length}}</view>
	        </view> -->
			
			<template v-for="(item, index) in data.list" :key="item.id" :itemData='item'>
				<view class="list-box">
					<view class="col-fow line">
						<view class="status-title">入馆状态：</view>
						<view class="btn-n size-normal status-txt">
							{{SPUP_GLOBAL_CONFIG.orderStatusLabel[item.orderStatus]}}
						</view>
					</view>
				    <view class="col-fow ">
				        <view class="card-title">来访单位：</view>
				        <view class="col-fl">{{item.owerUnit}}</view>
				    </view>
				    <view class="col-fow top-10">
				        <view class="card-title">来访时间：</view>
				        <view class="col-fl">{{dayjs(item.batchDate+item.batchStartTime).format('YYYY-MM-DD HH:mm')}}~{{item.batchEndTime.substr(0,2)}}:{{item.batchEndTime.substr(2,2)}}</view>
				    </view>
					<view class="col-fow top-10">
					    <view class="card-title">来访人数：</view>
					    <view class="col-fl">{{item.visitorsNum}}人</view>
					</view>
				    <view class="col-fow top-10">
				        <view class="card-title">联系人：</view>
				        <view class="col-fl">{{item.ownerName}} （ {{item.ownerPhone}} ）</view>
				    </view>
				    <view class="col-fow top-10">
				        <view class="card-title">提交时间：</view>
				        <view class="col-fl">{{dayjs(item.createTime).format('YYYY-MM-DD HH:mm')}}</view>
				    </view>
				</view>
			</template>
	    </view>
		<view v-else>
			<view class="list-box">
				<view style="color: #888;">暂无</view>
			</view>
		</view>
	</view>
</view>
</template>

<script setup>
	import {ref,reactive,onMounted,computed,watch} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import dayjs from "dayjs";
	import {getNewTeamOrder} from "@/common/api/server.js";
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	
	const data = reactive({
			orderType:'',
			list:[],
		})
			
	onLoad(options => {
		console.log("options:", options);
		if(options.orderType){
			data.orderType = options.orderType
		}
		getNewTeamOrder().then(retData=>{
			if(data.orderType=="tmp"){
				data.list = retData.data.filter(item=>item.orderCategory==SPUP_GLOBAL_CONFIG.orderType.tmpExhibitionTeam).sort((a,b)=>{
					return a["batchNo"]-b["batchNo"]
				});
			}else{
				data.list = retData.data.filter(item=>item.orderCategory!=SPUP_GLOBAL_CONFIG.orderType.tmpExhibitionTeam);
			}
		})
	})
</script>
<style>
	body{
		background-color: #f7f9fa;
	}
</style>
<style scoped>
	@import "statistics.css";
</style>