<template>
	<view class="content">
	    <!-- 标题时间 -->
	    <view class="col-fow top">
	        <view class="title-txt">预约数据统计</view>
	        <view class="col-fl time-txt">统计时间：{{dayjs(data.statistics.analysis_date).format('YYYY年MM月DD日') }}</view>
	    </view>
	    <view class="hr-box">
	        <view class="hr-twill"></view>
	        <view class="hr-edge-weak"></view>
	    </view>
	    <!-- 个人预约 -->
	    <view>
	        <p class="info-title">个人预约</p>
	        <view class="info-box">
	            <view class="col-fow">
	                <view class="card-title">今日已预约：</view>
	                <view><span>{{data.statistics.ticket_reserve_total}}</span>人（上午 <span>{{data.statistics.ticket_reserve_am}}</span> 人，下午 <span>{{data.statistics.ticket_reserve_pm}}</span> 人）</view>
	            </view>
	            <view class="col-fow top">
	                <view class="card-title">今日已核销：</view>
	                <view><span>{{data.statistics.ticket_checkin_total}}</span>人（上午 <span>{{data.statistics.ticket_checkin_am}}</span> 人，下午 <span>{{data.statistics.ticket_checkin_pm}}</span> 人）</view>
	            </view>
	        </view>
	    </view>
	    <!-- 展项预约 -->
	    <view>
	        <p class="info-title">展项预约</p>
	        <view class="info-box">
	            <view class="col-fow">
	                <view class="card-title">今日已预约：</view>
	                <view><span>{{data.statistics.item_reserve_total}}</span>人（上午 <span>{{data.statistics.item_reserve_am}}</span> 人，下午 <span>{{data.statistics.item_reserve_pm}}</span> 人）</view>
	            </view>
	            <view class="col-fow top">
	                <view class="card-title">今日已核销：</view>
	                <view><span>{{data.statistics.item_checkin_total}}</span>人（上午 <span>{{data.statistics.item_checkin_am}}</span> 人，下午 <span>{{data.statistics.item_checkin_pm}}</span> 人）</view>
	            </view>
	        </view>
	    </view>
	     <!-- 团队预约 -->
	     <view>
			<view class="col-fow">
				 <view class=" info-title">团队预约</view>
				 <view class="col-fl btn-more" @click="showTeamStatistics()">点击查看团队预约</view>
			</view>
			<view class="col-fow">
				 <view class=" info-title">特展预约</view>
				 <view class="col-fl btn-more" @click="showTmpTeamStatistics()">点击查看特展预约</view>
			</view>
	        <!-- <view class="info-box">
	            <view class="col-fow">
	                <view class="card-title">今日预约团队：</view>
	                <view class="col-fl">{{data.statistics.teamOrders.length}}</view>
	            </view>
				
				<template v-for="(item, index) in data.statistics.teamOrders" :key="item.id" :itemData='item'>
					<view class="list-box">
					    <view class="col-fow ">
					        <view class="card-title">预约单位名称：</view>
					        <view class="col-fl">{{item.owerUnit}}</view>
					    </view>
					    <view class="col-fow top-10">
					        <view class="card-title">预约入馆时间：</view>
					        <view class="col-fl">{{dayjs(item.batchDate).format('YYYY-MM-DD')}}</view>
					    </view>
					    <view class="col-fow top-10">
					        <view class="card-title">联系人：</view>
					        <view class="col-fl">{{item.ownerName}}</view>
					    </view>
					    <view class="col-fow top-10">
					        <view class="card-title">联系电话：</view>
					        <view class="col-fl">{{item.ownerPhone}}</view>
					    </view>
					</view>
				</template>
	        </view> -->
	    </view>
	</view>
</template>

<script setup>
	import {ref,reactive,onMounted,computed,watch} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import dayjs from "dayjs";
	import {getStatistics} from "@/common/api/server.js";
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	
	const data = reactive({
		orderType:'',
		statistics:{
			teamOrders:[
				// {
				// 	"owerUnit" : "上海煌豆网络科技有限公司",
				// 	"ownerName" : "易明",
				// 	"ownerPhone" : "15388980303",
				// 	"batchDate" : "20230531"
				// },
				// {
				// 	"owerUnit" : "上海绿能网络科技有限公司",
				// 	"ownerName" : "李明",
				// 	"ownerPhone" : "15388888888",
				// 	"batchDate" : "20230531"
				// }
			]
		}
	})
	const showTeamStatistics = () => {
		let url = '/pages/statistics/statistics_team'+(data.orderType?'?orderType='+data.orderType:'')
		uni.navigateTo({
			url: url
		})
	}
	const showTmpTeamStatistics = () => {
		let url = '/pages/statistics/statistics_team?orderType=tmp'
		uni.navigateTo({
			url: url
		})
	}
	onLoad(options => {
		if(options.orderType){
			data.orderType = options.orderType
		}
		getStatistics().then(retData=>{
			// console.log("统计数据：", data);
			data.statistics = {...retData.data}
			console.log("统计数据：", data.statistics);
		})
	})
</script>
<style>
	body{
		background-color: #f7f9fa;
	}
</style>
<style scoped>
	@import "statistics.css";
</style>
