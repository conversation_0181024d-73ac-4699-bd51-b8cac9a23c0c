view{
    padding: 0;
    margin: 0;
    font-size: 14px;
    font-weight: 400;
}
.content{
    padding:10px 20px;
    background: #f7f9fa;
    /* background: -webkit-linear-gradient(350deg,#efedee,#ededed); */
}
.info-title{
    font-size: 16px;
    font-weight: 600;
   padding: 15px 0 10px 0;
}
.info-box{
    padding: 15px 15px;
    border-radius: 10px;
    background: #fafcfb;
	margin-bottom: 15px;
    box-shadow: 0px 0px 15px rgba(167,172,175,0.1);
}
.col-fow{
    display: flex;
}
.col-fl{
    flex: 1;
}
.card-title{
    padding-right: 10px;
}
.top{
    padding-top: 13px;
}
.top-3{
    padding-top: 3px;
}
.top-10{
	padding-top: 10px;
}
.left10{
	padding-left: 10px;
}
.list-box{
    margin-top: 10px;
    border-radius: 8px;
    padding: 12px 10px;
    background-color: #f2f6f7;
    border: 0.7px solid #edf1f2;
}
.title-txt{
    font-size: 17px;
    font-weight: 600;
    line-height: 30px;
}
.time-txt{
    font-size: 13px;
    line-height: 30px;
    text-align: right;
}

.hr-box{
    position: relative;
    padding: 16px 0;
}
.hr-twill{
    position: absolute;
    border: 0;
    width: 47px;
    height: 6px;
    margin-top: -2px;
    margin-left: 28px;
    background: linear-gradient(-45deg, #000 25%, #F7F9FA 0, #F7F9FA 50%, #000 0, #000 75%, #F7F9FA 0);
    background-size: 8px 8px;
}
.hr-edge-weak{
    border: 0;
    width: 100%;
    height: 1px;
    background: #000;
}
.btn-more{
	padding: 15px 0 10px 0;
	text-align: right;
	color: #305096;
}

.line{
	padding: 1px 0 5px 0;
	border-bottom: 1px solid #f0f0f0;
	margin-bottom: 6px;
}
.status-title{
	width: 100px;
}
.status-txt {
	text-align: right;
	color: #e65e1b;
	flex: 1;
}
.gray-color{
	color: #7f8284;
}
.btn-s{
	font-size: 12px;
	background: #305096;
	color: #fff;
}
.margin10{
	margin: 5px 0 15px 0;
}
.screen{
	    font-size: 13px;
		width: fit-content;
		width: 56px;
		text-align: center;
		border-radius: 5px;
		/* padding: 2px 10px; */
		margin-right: 15px;
}
.screen-date{
		margin-top: 10px;
		border: 1px solid #000;
		border-radius: 5px;
		width: 80px;
		height: 35px;
		text-align: center;
		line-height: 35px;
	}
	.screen-s{
		border: 0.7px solid #305096;
		background: #e2eafc;
		color: #305096;
	}
	.screen-n{
		border: 0.7px solid #000;
		border-radius: 5px;
		color: #000;
	}
	
	.wrap{
		display:inline-block;
		border:6px solid transparent;
		border-top-color:#000;
		border-radius: 3px;
	
	}
	