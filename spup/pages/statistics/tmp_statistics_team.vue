<template>
<view class="content">
	 <view>
		 <!-- 日期筛选 -->
		<view class="col-fow margin10">
			<view class="info-title col-fl">特展团队预约</view>
			<!-- <view class="screen-date">近一周<uni-icons type="bottom" size="14" color="#000"></uni-icons></view> -->
			<view class="screen-date">
			    <uni-data-select :clear="false"
				  v-model="data.selectValue"
				  :localdata="data.selectRange"
				  @change="change"
				></uni-data-select>
			</view>
		 </view>
		 <!-- 状态筛选 -->
		 <view class="col-fow margin10">
			 <view @click="data.orderStatus=0" :class="'screen '+ (data.orderStatus==0?'screen-s':'screen-n')">全部</view>
			 <view @click="data.orderStatus=2" :class="'screen '+ (data.orderStatus==2?'screen-s':'screen-n')">已预约</view>
			 <view @click="data.orderStatus=4" :class="'screen '+ (data.orderStatus==4?'screen-s':'screen-n')">已接待</view>
			 <view @click="data.orderStatus=8" :class="'screen '+ (data.orderStatus==8?'screen-s':'screen-n')">已取消</view>
		 </view>
	    <view  v-if="listData.length">
<!-- 	        <view class="col-fow">
	            <view class="card-title">今日预约团队：</view>
	            <view class="col-fl">{{data.list.length}}</view>
	        </view> -->
			<template  v-for="(item, index) in listData" :key="item.id" :itemData='item'>
				<view class="info-box">
					<!-- 预约提交时间和状态 -->
					<view class="col-fow">
						<view class="gray-color">预约提交时间：{{dayjs(item.createTime).format('YYYY-MM-DD HH:mm')}}</view>
						<view :class="'btn-n size-normal status-txt '+(item.orderStatus==SPUP_GLOBAL_CONFIG.orderStatus.canceled?'status-cancled':'')">
							{{orderStatusLabel(item.orderStatus)}}
						</view>
					</view>
					<!-- 背景框里的内容 -->
					<view class="list-box">
						<!-- 图文 -->
						<view class="col-fow" style="border-bottom: 1px solid #e3e5e5;padding-bottom: 10px;">
							<image style="width: 40%;" mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl+'/tmpExhibition/1.jpg?v=3'"></image>
							<view class="left10">中国（上海）自由贸易试验区十周年改革建设成就展</view>
						</view>
						<!-- 字段 -->
						<view class="col-fow top-10">
						    <view class="card-title gray-color">组织机构：</view>
						    <view class="col-fl">{{item.owerUnitCode}}</view>
						</view>
						<view class="col-fow top-3">
						    <view class="card-title gray-color">参观单位：</view>
						    <view class="col-fl">{{item.owerUnit}}</view>
						</view>
						<view class="col-fow top-3">
						    <view class="card-title gray-color">参观时间：</view>
						    <view class="col-fl">{{dayjs(item.batchDate+item.batchStartTime).format('YYYY-MM-DD HH:mm')}}~{{item.batchEndTime.substr(0,2)}}:{{item.batchEndTime.substr(2,2)}}</view>
						</view>
						<view class="col-fow top-3">
						    <view class="card-title gray-color">参观人数：</view>
						    <view class="col-fl">{{item.visitorsNum}}人</view>
						</view>
						<view class="col-fow top-3">
						    <view class="card-title gray-color" ><text style="letter-spacing:0.3rem;">联系人</text>：</view>
						    <view class="col-fl">{{item.ownerName}}</view>
						</view>
						<view class="col-fow top-3">
						    <view class="card-title gray-color">联系电话：</view>
						    <view class="col-fl"><text class="paid-num" @click="callClick(item.ownerPhone)" style="font-size: 14px;color: #0048d4;border-bottom: 1px solid #5973ED;">{{item.ownerPhone}}</text> 
							</view>
						</view>
					</view>
					<!-- 接待确认按钮 -->
					<view class="col-fow top-10" v-if="item.orderStatus==SPUP_GLOBAL_CONFIG.orderStatus.reserved">
						<view class="col-fl"></view>
						<view><button class="btn-s" @click="submitClick(item)">接待确认</button></view>
					</view>
				</view>
			</template>
	    </view>
		<view v-else>
			<view class="list-box">
				<view style="color: #888;">暂无</view>
			</view>
		</view>
	</view>
</view>
<TmpTeamSubmit v-model:visible="tmpTeamSubmitVisible" v-model:orderData="data.selectOrderData"></TmpTeamSubmit>
</template>

<script setup>
	import {ref,reactive,onMounted,computed,watch} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import dayjs from "dayjs";
	import {getTmpExhibitionTeamStatistics} from "@/common/api/server.js";
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import TmpTeamSubmit from './tmp_team_submit.vue'
	const tmpTeamSubmitVisible = ref(false);
	const data = reactive({
			exhibitionNo:"",
			orderStatus:0,
			list:[],
			selectOrderData:{},
			selectValue: 1,
			selectRange: [
				{ value: 1, text: "今天" },
				{ value: 2, text: "近一周" },
				{ value: 3, text: "近15天" },
				{ value: 4, text: "近30天" },
				{ value: 5, text: "近60天" },
				{ value: 6, text: "近180天" },
			  ],
		})
	//拨打电话
	import tel from "@/common/tel.js"
	const { telClick } = tel();
	const callClick = (telnum)=>{
		telClick(telnum);
	}
	
	//数据
	const listData = computed(() => {
		let _listData = data.list.filter(item=>item.orderCategory==SPUP_GLOBAL_CONFIG.orderType.tmpExhibitionTeam);
		if(data.orderStatus!=0){
			_listData = _listData.filter(item=>item.orderStatus==data.orderStatus)
		}
		return _listData
	})
	
	//带参数的计算属性
	const orderStatusLabel = computed(() => (orderStatus) => {
		let txt = SPUP_GLOBAL_CONFIG.orderStatusLabel[orderStatus]
		if(orderStatus==SPUP_GLOBAL_CONFIG.orderStatus.completed){
			txt = "已接待"
		}
		return txt
	})
	
	//提交
	const submitClick = (orderData)=>{
		console.log("submitClick:", tmpTeamSubmitVisible.value);
		data.selectOrderData = orderData
		tmpTeamSubmitVisible.value = true
	}
	
	watch(()=>data.selectValue, (newValue, oldValue) => {
		_doGetTeamOrderList()
	})
	
	const _doGetTeamOrderList = ()=>{
		let startDate = dayjs().format('YYYYMMDD');
		let endDate = dayjs().format('YYYYMMDD');
		switch(data.selectValue){
			case 2:
				endDate = dayjs().add(6, 'days').format('YYYYMMDD');
				break;
			case 3:
				endDate = dayjs().add(14, 'days').format('YYYYMMDD');
				break;
			case 4:
				endDate = dayjs().add(29, 'days').format('YYYYMMDD');
				break;
			case 5:
				endDate = dayjs().add(59, 'days').format('YYYYMMDD');
				break;
			case 6:
				endDate = dayjs().add(179, 'days').format('YYYYMMDD');
				break;
		}
		getTmpExhibitionTeamStatistics(data.exhibitionNo, startDate, endDate).then(retData=>{
			data.list = retData.data.filter(item=>item.orderCategory==SPUP_GLOBAL_CONFIG.orderType.tmpExhibitionTeam);
		})
	}
	onLoad(options => {
		console.log("options:", options);
		if(!options.exhibitionNo){
			uni.showModal({
				content: '参数缺失，请和管理员联系',
				showCancel: false
			});
			return;
		}
		data.exhibitionNo = options.exhibitionNo
		_doGetTeamOrderList()
	})
</script>
<style>
	body{
		background-color: #f7f9fa;
	}
	
</style>
<style scoped>
	@import "statistics.css";
	.status-cancled{
		color: #be1b1b;
	}
	
</style>