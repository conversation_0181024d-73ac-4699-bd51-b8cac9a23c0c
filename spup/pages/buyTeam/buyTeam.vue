<template>
	<view class="complete">
		<view class="pd-title">浦东城市规划和公共艺术中心</view>
		<view class="fol-row site-hr">
			<view class="fol-col left-site ">
				<view class="size-mini">
					<view>场馆位置：上海市浦东新区高科西路2499号</view>
					<view style="line-height: 17px;padding-bottom: 15px;"><text>{{configStore.openTimeNative}} </text></view>
				</view>
			</view>
			<view class="right-site size-mini"><text style="text-decoration:underline;text-underline-offset:8px;" @click="goto('/subpages/home/<USER>')">参观指南</text></view>
		</view>
		<!-- 选择显示日期 -->
		<view class="fol-row bottom-line">
			<view class="fol-col">
				<view class="size-regular time-left">
					<text>选择日期</text>
				</view>
			</view>
			<view class="right-time size-regular"><text>{{reserveDate.selectMonth}}月</text></view>
		</view>
		<view class="fol-row dates_wrap" style="border-bottom: 1px solid #000;padding-bottom: 5px;margin-bottom: 6px;">
			<template v-for="(item, index) in reserveDate.dates" :key="index" :itemData='item'>
				<view @click="dateClick(item)" 
				:class="dayjs(reserveDate.selectDate).format('YYYYMMDD')===item.fullDate? 'fol-col date-box date-box-s': 'fol-col date-box date-box-z'">
					<text class="size-regular">{{item.date}}</text>
					<view class="span-number">{{item.day}}</view>
				</view>
			</template>
			<view style="color:#8F6A79; " class="fol-col date-box date-box-z">
				<uni-datetime-picker type="date" v-model="reserveDate.selectDate"
					:start="dateRang.start"
					:end="dateRang.end">
					<uni-icons type="calendar" color="#8F6A79 "  size="14"></uni-icons>
					<view style="font-size: 12px;margin: -9px;">更多</view> 
				</uni-datetime-picker>
			</view>
		</view>
		<!-- 选择显示时间段 -->
		<view class="fol-row time-hr" style="padding-top: 5px;">
			<view class="fol-col">
				<view class="size-regular">
					<view>选择时间</view>
				</view>
			</view>
			<view class="size-regular"><text>{{ticketRemainingTxt}}</text></view>
		</view>
		<view style="display: flex; flex-wrap: wrap;margin-top: 10px;" >
			<template v-for="(item, index) in reserveDate.times" :key="index" :itemData='item'>
				<view @click="timeClick(item)" 
					:class="reserveDate.selectTime.id===item.id ? 'time-box time-box-s' : (item.batchStatus==1 && item.ticketRemaining>0? 'time-box' : 'time-box time-box-n')" style="margin-top: 10px;">
				{{item.batchStartTime.substr(0,2)}}:{{item.batchStartTime.substr(2,4)}}~{{item.batchEndTime.substr(0,2)}}:{{item.batchEndTime.substr(2,4)}}
				</view>
			</template>
		</view >
	</view>
	<view class="hr-edge-weak"></view>
	<view class="complete">
		<view class="fol-row  time-hr" style="margin-top: 8px;">
			<view class="fol-col" style="text-align: left;line-height: 22px;">
				<view class="size-regular">预约信息</view>
			</view>
		</view>
		<view>
			<view class="fol-row fol-row_hr fol-top">
				<text class="uni-form-item__title"><text class="span-must">*</text>单位名称</text>
				<view class="fol-col">
					<input class="uni-input" v-model="teamInfo.owerUnit" type="text" placeholder="请输入单位名称" />
				</view>
			</view>
			<view class="fol-row fol-row_hr fol-top">
				<text class="uni-form-item__title"><text class="span-must">*</text>组织机构代码</text>
				<view class="fol-col">
					<input class="uni-input" v-model="teamInfo.owerUnitCode" type="text" placeholder="请输入组织机构代码" />
				</view>
			</view>
			<view class=" fol-row_hr  fol-top">
			  <view class="fol-row">
				 <text class="uni-form-item__title"><text class="span-must">*</text>参观人数</text>
				 <view class="fol-col">
					<input class="uni-input" v-model="teamInfo.visitorsNum" type="number" placeholder="请输入参观人数,最多100人" />
				 </view>
			   </view>
			  <!-- <view class="size-mini"><text style="color: #999;">预约信息发送至规划馆官方邮箱</text><text><EMAIL></text> </view> -->
			 </view>
			<view class="fol-row fol-row_hr fol-top">
				<text class="uni-form-item__title"><text class="span-must">*</text>联系人</text>
				<view class="fol-col">
					<input class="uni-input" type="text" v-model="teamInfo.ownerName" placeholder="请输入联系人姓名" />
				</view>
			</view>
			<view class="fol-row fol-row_hr fol-top">
				<text class="uni-form-item__title"><text class="span-must">*</text>联系方式</text>
				<view class="fol-col">
					<input class="uni-input" type="number" maxlength="11" v-model="teamInfo.ownerPhone" placeholder="请输入联系人手机号" />
				</view>
			</view>
		</view>
		<view style="margin-top: 25px;padding-bottom: 20px;">
			<view class="size-regular" style="color: #000;padding-bottom: 5px;">注：</view>
			<ul class="size-normal"  style="padding-left: 15px;">
				<li class="list-style ">提交预约信息后，工作人员会在3-5个工作日联系您确认行程；</li>
				<li class="list-style ">确认行程后，请将团队预约信息及介绍信（加盖公章）发送至馆方邮箱<text style="color: #000;"><EMAIL></text>；</li>
				<li class="list-style ">本馆暂不接待旅行社组织的旅游团队及类似团队；</li>
				<li class="list-style ">如遇特殊情况，请遵从馆方协调安排，馆方拥有最终解释权。</li>
			</ul>
		</view>
	</view>
	<view class="btn-box fol-row">
		<view class="fol-col" ></view>
		<view class="btn-right-site">
			<button class="btn-s" @click="submitClick">提交预约</button>
		</view>
			
	</view>
</template>

<script setup>
	import {ref,reactive,onMounted,computed,watch} from 'vue'
	import {onShow,onHide} from '@dcloudio/uni-app';
	import dayjs from "dayjs";
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {configRefStore} from '@/stores/configRef.js'
	const configStore = configRefStore();
	import {getReserveTime, getAvailableTime, getDetailByDate, createTeamOrder} from "@/common/api/server.js";
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	
	//显示几天
	let showDateCount = 4;
	//可以预约的时间段
	const dateRang = reactive({start:"", end:""});
	const reserveDate = reactive({
		dates: [],
		selectDate: '',
		selectMonth: '',
		times: [],
		selectTime: {},
	})
	const teamInfo = reactive({
		owerUnitCode:"",
		owerUnit:"",
		visitorsNum:"",
		ownerName:"",
		ownerPhone:""
	})
	
	const goto = (url) =>{
		uni.navigateTo({
			url: url
		})
	}
	
	watch(()=>reserveDate.selectDate, (newValue, oldValue) => {
		console.log("reserveDate.selectDate变化 :", newValue);
		reserveDate.selectMonth = dayjs(newValue).month()+1
		getDetailByDate(SPUP_GLOBAL_CONFIG.orderType.team, dayjs(newValue).format("YYYYMMDD")).then(res=>{
			reserveDate.times = res.data
			timeClick({})
			for(let i=0;i<reserveDate.times.length; i++){
				let tmpItem = reserveDate.times[i]
				if(tmpItem.batchStatus==1 && tmpItem.ticketRemaining>0){
					timeClick(tmpItem)
					break;
				}
			}
		})
		
		if(reserveDate.dates.filter(item=>dayjs(item.fullDate).format("YYYY-MM-DD")==newValue).length==0){
			console.log("需要刷新【选择日期】中的日期");
			let today = dayjs().format('YYYYMMDD')
			let dayLabelArr = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
			let tmpDates = []
			let startIndex=0, endIndex=showDateCount-1; 
			for(let i=0; i<rawReservDateList.length; i++){
				if(newValue==dayjs(rawReservDateList[i].day).format('YYYY-MM-DD')){
					startIndex = i;
					break;
				}
			}
			endIndex = startIndex+showDateCount-1;
			if(endIndex>=rawReservDateList.length){
				endIndex=rawReservDateList.length-1;
				startIndex = endIndex-showDateCount+1;
			}
			for(let index=startIndex; index<=endIndex; index++){
				let tmpDate = dayjs(rawReservDateList[index].day)
				tmpDates.push({date:tmpDate.date()
							, fullDate : tmpDate.format('YYYYMMDD')
							, day: today === tmpDate.format('YYYYMMDD') ? '今天' : dayLabelArr[tmpDate.day()]
							, isWorkday : rawReservDateList[index].isWorkday
							, dayRemark : rawReservDateList[index].dayRemark
							})
			}
			reserveDate.dates = tmpDates
		}
	})
	//点击日期
	const dateClick = (item)=>{
		reserveDate.selectDate = dayjs(item.fullDate).format("YYYY-MM-DD")
	}
	//点击时间
	const timeClick = (item)=>{
		if(item.id && (item.batchStatus==0 || item.ticketRemaining<=0) ){
			uni.showToast({
				icon: "none",
				title: item.ticketRemaining<=0 ? '本时段预约已满，请选择其他场次' : '本时段不可预约，请选择其他场次'
			})
		}else{
			reserveDate.selectTime = item
		}
	}
	
	//剩余团队数量
	const ticketRemainingTxt = computed(()=>{
		// if(reserveDate.dates.filter(item=>reserveDate.selectDate==dayjs(item.fullDate).format("YYYY-MM-DD")&&!item.isWorkday).length>0){
		// 	return "馆休"
		// }
		let sItem = reserveDate.dates.find(item=>reserveDate.selectDate==dayjs(item.fullDate).format("YYYY-MM-DD"));
		console.log("sItem:", sItem);
		if(sItem&&sItem.dayRemark&&!sItem.isWorkday){
			return sItem.dayRemark;
		}
		return "剩余团队："+ (reserveDate.selectTime.ticketRemaining>0?reserveDate.selectTime.ticketRemaining:"*")
	})
	
	//点击提交预约按钮
	const submitClick = ()=>{
		let tips = "";
		if(!reserveDate.selectTime.id){
			tips = "请先选择预约时间"
		}else if(!teamInfo.owerUnitCode){
			tips = "请填写组织结构代码"
		}else if(!teamInfo.owerUnit){
			tips = "请填写单位名称"
		}else if(!teamInfo.visitorsNum || teamInfo.visitorsNum>100){
			tips = "请填写参观人数，最多100人"
		}else if(!/^\d*$/.test(teamInfo.visitorsNum)){
			tips = "请填写正确的参观人数"
		}else if(!teamInfo.ownerName){
			tips = "请填写联系人姓名"
		}else if(!/^1\d{10}$/.test(teamInfo.ownerPhone)){
			tips = "请填写正确的联系人手机号"
		}
		if(tips){
			uni.showToast({
				icon: "none",
				title: tips
			})
			return;
		}

		let params = {
			"batchNo": reserveDate.selectTime.batchNo,
			"owerUnit": teamInfo.owerUnit,
			"owerUnitCode": teamInfo.owerUnitCode,
			"ownerName": teamInfo.ownerName,
			"ownerPhone": teamInfo.ownerPhone,
			"visitorsNum": teamInfo.visitorsNum
		}
		uni.showLoading({
		    title: "提交中…",
		    mask: true
		});
		createTeamOrder(params).then((res) => {
			uni.hideLoading()
			console.log("团体票下单返回：", res);
			if(res.code===0){
				uni.navigateTo({
					url: "/pages/buyTeam/buyTeamOk"
				})
			}else{
				uni.navigateTo({
					url: "/pages/buyTeam/buyTeamOk?message=" + (res.message||"提交预约失败")
				})
			}
		}).catch(res=>{
			uni.hideLoading()
		});
	}
	
	let rawReservDateList = []
	onMounted(()=>{
		getAvailableTime(SPUP_GLOBAL_CONFIG.orderType.team).then(res=>{
			showDateCount = parseInt(res.data.pageShowDays)
			rawReservDateList = res.data.dayList;
			dateRang.start =  dayjs(rawReservDateList[0].day).format('YYYY-MM-DD');
			dateRang.end =  dayjs(rawReservDateList[rawReservDateList.length-1].day).format('YYYY-MM-DD');
			reserveDate.selectDate = dateRang.start;
		})
	})
	
	

</script>

<style scoped>
	@import url("../common/common.css");
	@import "./buyTeam.css";
</style>
<style>
	page{
		background: #f7f9fa;
		padding-bottom: 76px;
	}
	
	.uni-input{
		padding-top: 3px;
		z-index: 0;
	}
</style>
