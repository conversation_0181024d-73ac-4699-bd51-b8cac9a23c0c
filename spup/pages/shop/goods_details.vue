<template>
	<view class="box">
		<image mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl+data.detail.goodsConverPicture"></image>
		<view class="size-normal goods-color">¥<text class="size-big">{{data.detail.goodsPrice}}</text></view>
	</view>
	<view class="size-medium line-bottom">{{data.detail.goodsName}}</view>
	<view class="box" style="padding-bottom: 36px;">
		<view class="size-title">商品详情</view>
		<view class="size-normal">商品编号：<text>{{data.detail.goodsNo}}</text></view>
		<view class="size-normal padding10">{{data.detail.goodsIntroduce}}</view>
		<image mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl+data.detail.goodsConverPicture"></image>
	</view>
	
	<view class="btn-box fol-row">
		<view class="fol-col" ></view>
		<view class="btn-right-site">
			<button v-if="data.isBooked" class="btn-n">已预定</button>
			<button v-else class="btn-s" @click="submitClick">预定</button>
		</view>
	</view>
</template>

<script setup>
	import {ref,reactive,onMounted,computed,watch} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import dayjs from "dayjs";
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {getShopGoodsDetail} from "@/common/api/server.js";
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	
	const data = reactive({
		detail:{},
		id:0,
		isBooked:0
	})
	
	//预定
	const submitClick = ()=>{
		let myShopGoods = uni.getStorageSync("myShopGoodsXXXX");
		if(!myShopGoods){
			myShopGoods = []
		}
		myShopGoods.push(data.detail);
		uni.setStorageSync("myShopGoodsXXXX", myShopGoods);
		uni.showToast({
			icon:'success',
			title:'预定成功'
		})
		data.isBooked = 1
	}
	
	onLoad(options => {
		getShopGoodsDetail(options.id).then(res=>{
			data.detail = res.data;
		})
		
		let myShopGoods = uni.getStorageSync("myShopGoodsXXXX");
		if(myShopGoods){
			if(myShopGoods.filter(item=>item.id==options.id).length){
				data.isBooked = 1
			}
		}
	})
</script>

<style>
	@import url("../common/common.css");
	page{
		background: #fff;
	}
	.goods-color{
		color: red;
		font-weight: 500;
	}
	.line-bottom{
		padding: 5px 15px 10px 15px;
		border-bottom: 8px solid #f0f0f0;
	}
	.size-title{
		font-size: 15px;
		font-weight: 500;
		padding: 15px 0;
		text-align: center;
	}
	.padding10{
		padding: 10px 0;
	}
	/* 按钮 */
	.btn-box {
		width: 100%;
		display: flex;
		position: fixed;
		padding: 15px 0;
		/* #ifdef H5 */
		left: var(--window-left);
		right: var(--window-right);
		/* #endif */
		bottom: 0;
		/* border-top: 1px solid #000; */
	    background: #F7F9FA;
		font-weight: 400;
		z-index: 1;
	}
	.btn-right-site {
		text-align: right;
	}
	.btn-s {
		width: 94px;
		margin-bottom: 10px;
		margin-right: 15px;
		/* padding: 3px 0; */
		font-size: 14px;
		color: #fff;
		background: #815462;
		border-radius: 6px;
	}
	.btn-n {
		width: 94px;
		margin-bottom: 10px;
		margin-right: 15px;
		/* padding: 3px 0; */
		font-size: 14px;
		color: #fff;
		background: #ccc;
		border-radius: 6px;
	}
</style>