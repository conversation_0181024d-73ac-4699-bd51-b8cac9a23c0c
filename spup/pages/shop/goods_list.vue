<template>
	<view>
		<uni-card is-full v-if="!data.my">
			<text class="size-mini">此处商品仅作线上展示，如需购买请到线下购买实体商品。</text>
		</uni-card>
		
		<view class="fol-row">
			<GoodsTypeTab v-if="!data.my" v-model="data.curTypeIndex"></GoodsTypeTab>
			<view class="fol-col content">
				<view class="goods-list-wrap" v-if="listData.length">
					<template v-for="(itemData, index) in listData" :key="index" :itemData='itemData'>
						<view class="fol-col img-box" style="position: relative;"
							@click="goDetail(itemData.id)">
							<image mode="aspectFill" class="goods-img"
								:src="SPUP_GLOBAL_CONFIG.mediaBaseUrl +  itemData.goodsConverPicture"></image>
							<view class="goods-title">
								<text class="common-title-skin">{{itemData.goodsName}}</text>
							</view>
						</view>
					</template>
				</view>
				<view class="empty-box" v-else>
					<image class="empty-img" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl+'/empty/empty.png'"></image>
					<text class="empty-txt" style="">暂无～</text>
				</view>
			</view>
		</view>

	</view>
</template>
<script setup>
	import {ref,reactive,onMounted,computed,watch} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import dayjs from "dayjs";
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {getShopGoodsList} from "@/common/api/server.js";
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import GoodsTypeTab from "./goods_type_tab.vue"
	
	const data = reactive({
		list:[],
		curTypeIndex: 0,
		my:false
	})

	const listData = computed(() => {
		let curType = Object.keys(SPUP_GLOBAL_CONFIG.goodsCategoryLabel)[data.curTypeIndex];
		console.log("curType:", curType);
		return data.list.filter(item=>item.goodsCategory==curType)
	});
	
	
	const styleChange =(e)=> {
		if (data.styleType !== e.detail.value) {
			data.styleType = e.detail.value
		}
	}
	const colorChange =(e)=> {
		if (data.styleType !== e.detail.value) {
			console.log(e.detail.value);
			data.activeColor = e.detail.value
		}
	}
	const goDetail = (id)=>{
		uni.navigateTo({
			url: "/pages/shop/goods_details?id="+id
		})
	}
	onLoad(options => {
		if(options.my){
			data.my = true
			uni.setNavigationBarTitle({
			  title: '我的预定'
			})
			let myShopGoods = uni.getStorageSync("myShopGoodsXXXX");
			console.log("myShopGoods:", myShopGoods);
			if(!myShopGoods){
				data.list = []
			}else{
				data.list = myShopGoods
			}
		}else{
			uni.setNavigationBarTitle({
			  title: '文创小店'
			})
			getShopGoodsList().then(res=>{
				data.list = res.data.content
			})
		}
	})
</script>

<style>
	@import url("../common/common.css");
	@import "./goods_list.css";
</style>

