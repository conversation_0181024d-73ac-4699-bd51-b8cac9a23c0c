<template>
	<view class="tabs-wrap">
	<template v-for="(itemData, index) in data.types" :key="index" :itemData='itemData'>
		<view :class="index==curIndex?'tab active-tab':'tab'" @tap="itemClick(index)">{{itemData}}</view>
	</template>
	</view>
</template>

<script setup>
	import {ref,reactive,onMounted,computed,watch} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import dayjs from "dayjs";
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {getShopGoodsList} from "@/common/api/server.js";
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	const props = defineProps(['modelValue']);
	const emit = defineEmits(['update:modelValue']);
	
	const data = reactive({
		types: Object.values(SPUP_GLOBAL_CONFIG.goodsCategoryLabel),
	})
	
	const curIndex = computed(() => {
		return props["modelValue"]
	});
	
	const itemClick = index=>{
		console.log("itemClick：",index);
		emit("update:modelValue", index);
	}
</script>

<style>
	.tabs-wrap{
		margin: 20rpx 10rpx 20rpx 20rpx;
		text-align: center;
		z-index: 5;
	}
	.tab{
		padding: 16rpx 26rpx;
		color: #815462;
	}
	.active-tab{
		color: #fff ;
		background-color: #815462;
	}
</style>