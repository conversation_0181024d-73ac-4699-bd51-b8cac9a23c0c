<template>
	<view>
		<Tabbar v-model="data.timeType" ></Tabbar>
		<view class="complete">
			<template v-for="(item, index) in curList" :key="item.id" :itemData='item'>
				<view class="fol-row active-list">
					<view class="active-img">
						 <view class="float-list">
						   <image style="height: 140px;" mode="aspectFill" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + item.converPicture"></image> 
						   <i class="tip">{{activeStatusLabel(item)}}</i>
						 </view>
					</view>
					<view class="fol-col text-box" style="position: relative;">
						<view class="title-head">{{item.title}}</view>
						<view class="label">{{SPUP_GLOBAL_CONFIG.activeTypeLabel[item.type]}}</view>
						<view class="size-span">报名截止 ：{{dayjs(item.startTime).format("YYYY/MM/DD")}} - {{dayjs(item.endTime).format("YYYY/MM/DD")}}</view>
						<button class="btn" @click="showDetail(item)">查看详情</button>
					</view>
				</view>
			</template>
			<view v-if="!curList.length" class="empty-box" style="top: 20%;">
				<image class="empty-img" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl+'/empty/empty.png'"></image>
				<text class="empty-txt" style="">暂无活动～</text>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {ref,reactive,onMounted,computed,watch} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import dayjs from "dayjs";
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {getActivityList, getMyActivityList} from "@/common/api/server.js";
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import Tabbar from "./tabbar.vue"
	
	const data = reactive({
		list:[],
		timeType:SPUP_GLOBAL_CONFIG.activeTimeType.recent
	})
	
	//带参数的计算属性，
	const curList = computed(() => {
	  let now = dayjs().valueOf()
	  return data.list.filter(item=>{
		return (data.timeType==SPUP_GLOBAL_CONFIG.activeTimeType.recent && now<=dayjs(item.endTime).valueOf())
			  || (data.timeType==SPUP_GLOBAL_CONFIG.activeTimeType.last && now>dayjs(item.endTime).valueOf())
	  })
	})
	
	//带参数的计算属性
	const activeStatusLabel = computed(() => (item) => {
		let now = dayjs().valueOf()
		if(now<dayjs(item.startTime).valueOf()){
			return "未开始"
		}else if(now>dayjs(item.endTime).valueOf()){
			return "已结束"
		}else{
			return "进行中"
		}
	})
	const showDetail = (item) => {
		uni.navigateTo({
			url: "/pages/active/active_details?id="+item.id
		})
	}

	
	onLoad(options => {
		if(options.my){
			uni.setNavigationBarTitle({
			  title: '我报名的活动'
			})
			getMyActivityList().then(res=>{
				data.list = res.data
			})
		}else{
			uni.setNavigationBarTitle({
			  title: '活动报名'
			})
			getActivityList().then(res=>{
				data.list = res.data
			})
		}
	})
</script>

<style>
	page{
		background: #F7F9FA;
	}
</style>
<style scoped>
	@import '../common/common.css';
	@import "./active.css";
</style>