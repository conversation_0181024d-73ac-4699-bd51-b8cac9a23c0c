<template>
	<view class="complete">
		<view class="fol-row top15 bottom-line">
			<view class="active-img">
				<image style="height: 140px;" mode="aspectFill" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + data.detail.converPicture"></image> 
			</view>
			<view class="fol-col left15">
				<view class="head-title">
					{{data.detail.title}}
				</view>
				<view class="size-span">剩余名额：{{data.entry.entryRemind}}/{{data.entry.entryLimit}}人</view>
				<view class="size-span">报名截止：{{dayjs(data.entry.entryStartTime).format("YYYY/MM/DD")}} - {{dayjs(data.entry.entryEndTime).format("YYYY/MM/DD")}}</view>
			</view>
		</view>
		<view class="bottom-line top15">
			<view class="fol-row line-height">
				<view>活动时间：</view>
				<view class="fol-col txt-right">{{dayjs(data.detail.startTime).format("YYYY/MM/DD")}} - {{dayjs(data.detail.endTime).format("YYYY/MM/DD")}}</view>
			</view>
			<view class="fol-row">
				<view>活动地点：</view>
				<view class="fol-col txt-right">{{data.detail.address}}</view>
			</view>
		</view>
		
		<view class="top15">
			<view class="fol-row line-height">
				<view class="btn-add-label">报名人员信息：</view>
				<view class="fol-col txt-right ">
					<button class="btn-add" @click="editCustomerClick">{{!customer.phone?"完善资料":"修改"}}</button>
				</view>
			</view>
			<view v-if="!customer.phone" style="color:#ff0000">您尚未编辑个人信息，请先完善资料再报名</view>
			<template v-if="customer.phone" >
				<view class="fol-row line-height">
					<view>姓名：</view>
					<view class="fol-col txt-right">{{customer.realName}}</view>
				</view>
				<view class="fol-row">
					<view>手机号：</view>
					<view class="fol-col txt-right">{{customer.phone.substr(0,3)}}****{{customer.phone.substr(7,11)}}</view>
				</view>
			</template>
		</view>
		
	</view>
	<!-- 按钮 -->
	<view class="btn-box fol-row">
		<view class="fol-col" ></view>
		<view class="btn-right-site complete">
			<button class="btn-s" @click="entry">报名</button>
		</view>
	</view>
</template>

<script setup>
	import {ref,reactive,onMounted,computed} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import {getActivityDetail, getActivityEntry, activityEntry} from "@/common/api/server.js";
	import dayjs from "dayjs";
	
	const data = reactive({
		id:0,
		detail:{
			
		},
		entry:{
			
		}
	})
	
	const customer = computed(() => {
		let obj = commonStore.userInfo
		return obj?obj:{}
	});
	const editCustomerClick = () =>{
		uni.navigateTo({
			url: "/pages/my/my_contacts-edit?back=1"
		})
	}
	const entry = () => {
		let errMsg = ""
		if(!customer.value.phone){
			uni.showModal({
				title: '完善资料',
				content: '您尚未编辑个人信息，请先完善资料再报名',
				confirmText: "完善资料",
				cancelText:"取消",
				success: function(res) {
					if(res.confirm){
						editCustomerClick();
					}
				}
			})
			return;
		}
		if(data.entry.entryRemind<=0){
			errMsg = '名额已满'
		}
		let now = dayjs().valueOf()
		if(now<dayjs(data.entry.entryStartTime).valueOf()){
			errMsg = '报名尚未开始'
		}else if(now>dayjs(data.entry.entryEndTime).valueOf()){
			errMsg = "报名已结束"
		}

		if(errMsg){
			uni.showToast({
				icon: "error",
				title: errMsg
			})
			return;
		}
		uni.showModal({
			title: '活动报名',
			content: '确定要报名？',
			confirmText: "确定",
			cancelText:"取消",
			success: function(res) {
				if(res.confirm){
					doEntry()
				}
			}
		})
	}
	
	const doEntry = ()=>{
		let params = {
			"activityId":parseInt(data.id),
			"userGender": customer.value.userGender,
			"userIdcard": customer.value.cardNo,
			"userIdcardType": customer.value.cardCategory,
			"userName": customer.value.realName,
			"userPhone": customer.value.phone,
		}
		activityEntry(params).then(res=>{
			if(res.code==0){
				uni.navigateTo({
					url: "/pages/active/active_entry_ok?id="+data.id
				})
			}
		})
	}
	
	onLoad(options => {
		console.log("options:", options);
		commonStore.customers = []
		if(options.id){
			data.id = options.id
			getActivityDetail(data.id).then(res=>{
				data.detail = res.data;
			})
			getActivityEntry(data.id).then(res=>{
				data.entry = res.data;
			})
		}else{
			uni.showModal({
				content: '参数错误',
				showCancel: false
			});
		}
	})
</script>

<style scoped>
	@import '../common/common.css';
	@import "./active.css";
</style>
<style>
</style>