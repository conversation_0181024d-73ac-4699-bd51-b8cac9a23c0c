<template>
	<view class="complete">
		<view class="details-box">
			<view class="title-head center">
				{{data.detail.title}}
			</view>
			<view class="size-regular top15 details-txt">
				<!-- <view style="white-space: pre-wrap;">
					{{data.detail.content}}
				</view>		 -->
			<!-- 	<rich-text style="word-break: break-all;" :nodes="data.detail.content">
				</rich-text> -->
				<mp-html :content="data.detail.content" />
			</view>
<!-- 			<view class="details-img">
				<image style="height: 140px;" mode="aspectFill" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + data.detail.converPicture"></image> 
			</view> -->
			<view>活动地点：{{data.detail.address}}</view>
			<view>活动时间：{{dayjs(data.detail.startTime).format("YYYY/MM/DD")}} - {{dayjs(data.detail.endTime).format("YYYY/MM/DD")}}</view>
			<view>报名截止：{{dayjs(data.entry.entryStartTime).format("YYYY/MM/DD")}} - {{dayjs(data.entry.entryEndTime).format("YYYY/MM/DD")}}</view>
			<view>剩余名额：{{data.entry.entryRemind}}/{{data.entry.entryLimit}}人</view>
		</view>
		
		<view class="top-line top15" v-if="customer.userPhone">
			<view class="fol-row line-height">
				<view class="btn-add-label">我的报名信息：</view>
			</view>
			<view class="fol-row line-height">
				<view>姓名：</view>
				<view class="fol-col txt-right">{{customer.userName}}</view>
			</view>
			<view class="fol-row">
				<view>手机号：</view>
				<view class="fol-col txt-right">{{customer.userPhone.substr(0,3)}}****{{customer.userPhone.substr(7,11)}}</view>
			</view>
		</view>
	</view>
	<!-- 按钮 -->
	<view class="btn-box fol-row" v-if="data.id!=0">
		<view class="fol-col" ></view>
		<view class="btn-right-site complete">
			<button v-if="customer.userPhone" class="btn-n">已报名</button>
			<button v-else class="btn-s" @click="entry()">参与报名</button>
		</view>
	</view>
</template>

<script setup>
	import {ref,reactive,onMounted,computed} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import {getActivityDetail, getActivityEntry} from "@/common/api/server.js";
	import dayjs from "dayjs";

	const data = reactive({
		id:0,
		detail:{
			
		},
		entry:{
			
		}
	})
	
	const customer = computed(() => {
		return data.entry&&data.entry.myEntry?data.entry.myEntry:{}
	});
	const entry = () => {
		let errMsg = ""
		if(data.entry.entryRemind<=0){
			errMsg = '名额已满'
		}
		let now = dayjs().valueOf()
		if(now<dayjs(data.entry.entryStartTime).valueOf()){
			errMsg = '报名尚未开始'
		}else if(now>dayjs(data.entry.entryEndTime).valueOf()){
			errMsg = "报名已结束"
		}
		if(customer.value.userPhone){
			errMsg = "请勿重复报名"
		}
		if(errMsg){
			uni.showToast({
				icon: "error",
				title: errMsg
			})
			return;
		}
		
		uni.navigateTo({
			url: "/pages/active/active_entry?id="+data.id
		})
	}
	
	onLoad(options => {
		console.log("options:", options);
		if(options.id){
			data.id = options.id
			getActivityDetail(data.id).then(res=>{
				data.detail = res.data;
			})
			getActivityEntry(data.id).then(res=>{
				data.entry = res.data;
			})
		}else{
			uni.showModal({
				content: '参数错误',
				showCancel: false
			});
		}
	})
</script>

<style scoped>
	@import '../common/common.css';
	@import "./active.css";
</style>
<style>

</style>