.complete {
	padding: 0 15px;
}

/* 按钮 */
.btn-box {
	width: 100%;
	border-top: 1px solid #000;
	padding-top: 10px;
	display: flex;
	position: fixed;
	/* #ifdef H5 */
	left: var(--window-left);
	right: var(--window-right);
	/* #endif */
	bottom: 0;
}

.btn-right-site {
	text-align: right;
}

.btn-s {
	width: 94px;
	margin-bottom: 10px;
	/* padding: 3px 0; */
	font-size: 14px;
	color: #fff;
	background: var(--button-color);
	border-radius: var(--button-small-radius);
}
.btn-n{
	width: 94px;
	margin-bottom: 10px;
	/* padding: 3px 0; */
	font-size: 14px;
	border: 1px solid var(--button-solid-color);
	background-color: var(--button-n);
	color: var(--button-n-text);
	border-radius: 6px;
	text-align: center;
}
/*  */
.center {
	text-align: center;
}

.active-list {
	padding-bottom: 10px;
	padding-top: 20px;
}

.active-img {
	position: relative;
	height: 100%;
	position: relative;
	box-sizing: border-box;
	background-color: #999;
	height: 140px;
	width: 115px;
}
.label{
	font-size: 12px;
	color: var(--button-color);
	width: fit-content;
	margin: 5px 0;
}
.float-list image {
	width: 100%;
	height: 100%;
}

.tip {
	position: absolute;
	display: inline-block;
	font-size: 12px;
	color: #ffffff;
	top: 0;
	left: 0;
	padding: 0 3px;
	background-color: var(--button-color);
	z-index: 2;
}

.text-box {
	margin-left: 10px;
}

.title-head {
	font-size: 16px;
	font-weight: 500;
}

.btn {
	font-size: 14px;
	padding: 0;
	text-align: left;
	color: var(--button-color);
	overflow: hidden;
	position: absolute;
	bottom: 0;
}

/*  */
.details-box {
	padding: 20px 0 73px 0;
}

.details-txt {
	text-indent: 2em;
}

.top15 {
	margin-top: 15px;
}

.details-img {
	background-color: #999;
	margin: 15px auto 10px auto;
	height: 140px;
	width: 115px;
}

/*  */
.top-line {
	border-top: 1px solid #e3e5e5;
	padding-top: 2px;
}

.bottom-line {
	border-bottom: 1px solid #e3e5e5;
	padding-bottom: 25px;
}

.left15 {
	margin-left: 15px;
}

.txt-right {
	text-align: right;
}

.line-height {
	line-height: 2.5em;
}

/* active-tip*/
.tips-box {
	text-align: center;
	margin-top: 60px;
}

.tips-icon {
	width: 132px;
	margin: auto;
	padding-bottom: 10px;
}

.tips-span {
	font-size: 14px;
	margin-top: 10px;
	color: #999999;
}
.tips-massges{
	background-color: #fff;
	border: 1px solid #e3e5e5;
	border-radius: 0.5em;
	padding:10px 15px;
	margin: 0 30px;
	text-align: left;
	font-size: 14px;
	margin-top: 25px;
}
.size-padding{
	font-size: 16px;
	padding-bottom:10px;
}
.btn-add-label{
	font-size: 12px;
	color: #999999;
}
.btn-add{
	height: 30px;
	float: right;
	font-size: 14px;
	line-height: 30px;
	border: 1px solid #000000;
}
.information-text {
	border: 1px solid #000000;
	border-radius: var(--button-small-radius);
	padding: 6px 10px;
	margin: 10px 0 10px 0;
}