<template>
	<view class="tips-box">
		<view class="tips-icon">
			<image mode="widthFix" src="../../static/tips/tips-success.png"></image>
		</view>
		<text class="size-medium">报名成功！</text>
		<view class="tips-span">
			<text>您可在“个人中心-我的活动”中查看</text>
		</view>
		<view class="tips-massges">
			<view class="size-padding">{{data.detail.title}}</view>
			<view><text class="size-span2">活动时间：</text><text>{{dayjs(data.detail.startTime).format("YYYY/MM/DD")}} - {{dayjs(data.detail.endTime).format("YYYY/MM/DD")}}</text></view>
			<view><text class="size-span2">活动地点：</text><text>{{data.detail.address}}</text></view>
		</view>
	</view>
</template>

<script setup>
	import {reactive} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import {getActivityDetail, getActivityEntry, activityEntry} from "@/common/api/server.js";
	import dayjs from "dayjs";
	
	const data = reactive({
		id:0,
		detail:{
			
		}
	})
	
	onLoad(options => {
		console.log("options:", options);
		commonStore.customers = []
		if(options.id){
			data.id = options.id
			getActivityDetail(data.id).then(res=>{
				data.detail = res.data;
			})
		}else{
			uni.showModal({
				content: '参数错误',
				showCancel: false
			});
		}
	})
</script>

<style scoped>
	@import '../common/common.css';
	@import "./active.css";
</style>
<style>
</style>