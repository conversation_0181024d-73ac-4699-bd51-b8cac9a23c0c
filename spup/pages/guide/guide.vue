<template>
	<view class="complete">
		<view class="swiper-box" v-if="curPoint.banners.length==1">
			<image class="swiper-img" mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl+curPoint.banners[0].img"></image>
		</view>
		<view  v-if="curPoint.banners.length>1">
			<swiper class="swiper swiper-box" circular :indicator-dots="true" autoplay="true" interval="3500"
				duration="500">
				<swiper-item v-for="(item, index) in curPoint.banners" :key="data.selectPoint+index" class="example-body-dots">
					<view class="my-swiper-item">
						<image class="swiper-img" mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl+item.img">
						</image>
					</view>
				</swiper-item>
			</swiper>
		</view>
	<!-- 	 <view class="container">
		    <image @load="gifLoaded" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl+'/guide/gif/gx.gif'" />
		  </view> -->
		<view>
			<view>
				<view class="flow" style="display: flex;padding-right: 15px;">
					<view class="fol-col">
						<text class="pd-title">{{data.selectPoint}}</text>
					</view>
					<view v-if="curPoint.tips" style="width: 30px;">
						<image  mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl+curPoint.tips.imgBtn" @click="showTips" style="width: 100%;" />
					</view>
				</view>
				<view class="hr-box">
					<view class="hr-twill"></view>
					<view class="hr-edge-weak"></view>
				</view>
				<!-- 音频 -->
				<view style="background: #f2f4f5; border-radius: 4px;margin: 5px 15px;">
					<hCustomAudio 
						:audio="SPUP_GLOBAL_CONFIG.mediaBaseUrl+curPoint.audio"
						playIcon="/static/icons/audio-play.png"
						pauseIcon="/static/icons/audio-pause.png"
						sliderColor="#815462" activeColor="#815462" backgroundColor="#ccc" timeTxtColor="#8c8c8c"
					/>
				</view>
				<!-- 场馆文字介绍 -->
				<view class="pd-text">
					{{curPoint.desc}}
				</view>
			</view>
		</view>
	</view>

	<!-- 楼层切换 -->
	<view class="footer-box">
		<view class="footer">
			<view class="point-wrap">
				<template v-for="(item, index) in curPoints" :key="index" :itemData='item'>
					<view :class="'point-item '+ (item.name==data.selectPoint?'point-item-selected':'')" @click="switchPoint(item.name)" >{{item.name}}</view>
				</template>
			</view>
			<view class="floor-wrap">
				<template v-for="(item, index) in data.list" :key="index" :itemData='item'>
					<view :class="'floor-item '+(item.floor==data.selectFloor?'floor-item-selected':'')" @click="switchFloor(item.floor)">{{item.floor}}</view>
				</template>
				<!-- <view class="floor-item floor-item-selected" index="0" @click="toPage(0)">1F</view>
				<view class="floor-item" index="1" @click="toPage(1)" style="text-align: center;">2F</view> -->
			</view>
		</view>
	</view>
	
	<uni-popup ref="myPopup"  mask-background-color="rgba(0,0,0,0)">
		<view class="popup-content" v-if="curPoint.tips">
			<view class="popup-title">提示</view>
			<view class="popup-txt">{{curPoint.tips.txt}}</view>
			<view style="margin: auto;width:120px;">
				<image class="popup-img" mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl+curPoint.tips.imgTips"> </image>
			</view>
			
		</view>
	</uni-popup>
</template>

<script setup>
	import {ref, reactive,onMounted,computed,watch} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import hCustomAudio from '@/components/h-custom-audio/index.vue'
	import {getGuideSpotList,viewGuide} from "@/common/api/server.js";
	const myPopup = ref();
	

	const switchFloor = floor=>{
		data.selectFloor = floor;
		data.selectPoint = curPoints.value[0].name
	}
	const curPoints = computed(() => {
		console.log("data.selectFloor :", data.selectFloor );
		let tempArr = data.list.filter(item=>item.floor==data.selectFloor)
		if(tempArr.length>0){
			return data.list.filter(item=>item.floor==data.selectFloor)[0].points;
		}
		return []
	}); 
	const curPoint = computed(() => {
		if(curPoints.value.length==0){
			return {banners:[]}
		}
		let tmpPoint = curPoints.value.filter(item=>item.name==data.selectPoint)[0];
		if(tmpPoint.tips){
			// console.log("要显示tips");
			// myPopup.value.open("center");
		}
		return tmpPoint
	}); 
	const switchPoint = point=>{
		data.selectPoint = point
	}

	const gifLoaded = event =>{
		console.log("gifLoaded", event)
	}
	const data = reactive({
		selectFloor:"",
		selectPoint:"",
		list: [],
	})
	
	watch(()=>data.selectPoint, (newValue, oldValue) => {
		console.log("selectPoint变化 :", newValue);
		let tmpPoint = curPoints.value.filter(item=>item.name==newValue)[0];
		if(tmpPoint.tips){
			showTips()
		}
		//通知后台有人浏览该展项
		viewGuide(tmpPoint.id)
	})
	
	const showTips = ()=>{
		if(myPopup.value){
			myPopup.value.open("center");
		}else{
			setTimeout(()=>{
				myPopup.value.open("center");
			}, 500)
		}
	}
	
	let rawList = [
			{
				floor: "3F",
				points: [{
					name: "映像浦东",
					desc: '居于以“映像浦东”为主题的时空隧道，立于屏幕前的互动感应点之上，在流线型屏幕中窥见浦东历史发展的演变过程，了解浦东的前世今生，见证浦东在三十多年长河中的岁月荣光。',
					banners: [{
						"img": "/guide/3F/3-1 映像浦东-1.jpg"
					}],
					tips:{
						"imgBtn": "/guide/tips/挥手-btn.png",
						"imgTips": "/guide/tips/挥手-tips.png",
						"txt": "在屏幕前方隔空挥手，体验互动效果。"
					},
					audio:"/guide/audio/映像浦东.mp3"
				},
				{
					name: "战略引领",
					desc: '从党中央、国务院正式宣布开发开放浦东重大战略决策开始，从未减少对浦东开发建设的关注，先后作出重要指示，习近平总书记更是指出“浦东发展的意义在于窗口作用、示范意义，在于敢闯敢试、先行先试，在于排头兵的作用”。',
					banners: [{
						"img": "/guide/3F/01-战略引领.jpg"
					}],
					audio:"/guide/audio/战略引领.mp3"
				},
				{
					name: "风从东方来",
					desc: '一座城市，两个世纪，22张印记。一股从传统纸媒传来的风，打开浦东三十年记忆的闸门。光斑点亮的地方，普画了浦东三十年的恢弘长卷，“外高桥保税区投入运营”“浦东国际机场通航”，来一场重访过去，展望未来之旅。',
					banners: [{
						"img": "/guide/3F/02-风从东方来.jpg"
					}],
					audio:"/guide/audio/风从东方来.mp3"
				},
				{
					name: "规划之路",
					desc: '以浦东30多年来开发开放历程为线索，打造的全包围式感知城市规划发展的四维空间。三条主轴一个理念，地面的城市规划脉络、墙面的城市发展大事记及顶部的七个发展关键点，三线并齐，更加凸显了“规划引领城市发展”的理念，展现了浦东历经三十载探索与建设的奋斗足迹。 ',
					banners: [{
						"img": "/guide/3F/03-规划之路-1.jpg"
					}, {
						"img": "/guide/3F/03-规划之路-2.jpg"
					}],
					audio:"/guide/audio/规划之路.mp3"
				},
				{
					name: "战略定位",
					desc: '《引领区意见》对浦东的战略定位做出了全新的阐释，将引领浦东向更高水平的改革开放前进。',
					banners: [{
						"img": "/guide/3F/3-5 战略定位-2.jpg"
					}],
					audio:"/guide/audio/战略定位.mp3"
				},
				{
					name: "BOX空间",
					desc: '想来一场解锁浦东之旅么？穿过寓意“开放”“创新”“高品质”的无限镜，进入用“科技玻璃”营造的浦东BOX沉浸式秀场，解锁在不断炸裂视效冲击下浦东的规划之美，将视听感官燃上新高度。',
					banners: [{
						"img": "/guide/3F/04-BOX空间-1.jpg"
					}, {
						"img": "/guide/3F/04-BOX空间-2.jpg"
					}],
					tips:{
						"imgBtn": "/guide/tips/粒子-btn.png",
						"imgTips": "/guide/tips/粒子-tips.png",
						"txt": "在展厅内走一走，体验粒子随行。"
					},
					audio:"/guide/audio/BOX空间.mp3"
				},
				{
					name: "更高水平改革开放的开路先锋",
					desc: '展区右侧墙面的壁挂模型上，看上海自贸区如何一步步发展壮大“开花结果”的过程。中间核心位置则是临港新片区主城区滴水湖核心片区模型，讲述滴水湖载着科技之船，配合光影激光秀展现临港滴水成湖，向海而生的美好未来。',
					banners: [{
						"img": "/guide/3F/05-开放主题展厅.jpg"
					}],
					audio:"/guide/audio/更高水平改革开放的开路先锋.mp3"
				},
				{
					name: "自主创新发展的时代标杆",
					desc: '展区设置了张江科学城、南北科创走廊、金色中环发展带三个大型壁挂模型，通过智能机械臂精准对位浦东八大重点科创项目模型并进行数字化三维影片演示，共同展示浦东孕育创新、争当先锋。',
					banners: [{
						"img": "/guide/3F/06-创新主题展厅.jpg"
					}],
					audio:"/guide/audio/自主创新发展的时代标杆.mp3"
				},
				{
					name: "宜居宜业的城市治理样板",
					desc: '以AI人工智能机器人为媒介，串联城市公共中心体系及各类公共服务设施、生态空间结构知识库，投射于包围式数字屏幕之上，全方位了解浦东如何深入践行人民城市的理念，“建设好属于人民、服务人民、成就人民的城市”。',
					banners: [{
							"img": "/guide/3F/3-9 高品质主题展厅-1.jpg"
					}],
					tips:{
						"imgBtn": "/guide/tips/机器人语音互动-btn.png",
						"imgTips": "/guide/tips/机器人语音互动-tips.png",
						"txt": "有一个待您提问的机器人“小浦”在此守候。"
					},
					audio:"/guide/audio/宜居宜业的城市治理样板.mp3"
				},
				{
					name: "璀璨浦东",
					desc: '通过数字魔镜墙展示陆家嘴金融城、张江科学城、外高桥贸易城、金桥智造城、度假区旅游城、祝桥航空城、临港未来城七大主题城市的未来印象。',
					banners: [{
						"img": "/guide/3F/3-10 璀璨浦东.jpg"
					},{
						"img": "/guide/3F/3-10 璀璨浦东-2.jpg"
					}],
					tips:{
						"imgBtn": "/guide/tips/挥手-btn.png",
						"imgTips": "/guide/tips/挥手-tips.png",
						"txt": "在屏幕前方隔空挥手，体验互动效果。"
					},
					audio:"/guide/audio/璀璨浦东.mp3"
				},
				// {
				// 	name: "大模型厅",
				// 	desc: '这里运用了国内唯一竖向多功能、超沉浸式的巨幅壁挂模型与国内首个最大尺度室内投影升降巨幕，通过物理模型与虚拟影像、全息激光秀相结合的手法，打造全沉浸式体验空间，展示“开放浦东 梦想之城”的浦东形象。',
				// 	banners: [{
				// 		"img": "/guide/3F/大模型厅-1.jpg"
				// 	},{
				// 		"img": "/guide/3F/大模型厅-2.jpg"
				// 	}]
				// },
				]
			},
		{
			floor: "2F",
			points: [{
				name: "人民城市理念",
				desc: '浦东，作为“人民城市”理念的践行者，一直以人民的核心需求出发。对于习近平总书记提出“人民城市人民建、人民城市为人民。城市是人集中生活的地方，城市建设必须把让人民宜居安居放在首位，把最好的资源留给人民”这一指示，更是坚定执行，现在这也成为了引领浦东城市发展建设的重要遵循。',
				banners: [{
					"img": "/guide/2F/1-2 人民城市理念.jpg"
				}],
				audio:"/guide/audio/人民城市理念.mp3"
			},
			{
				name: "浦东天际线",
				desc: '黄浦江之于上海就犹如长江之于中国一般，浦东占据比例是沿线几个区距离最长、工程量最大的。这里集多种展示手段联动于一体，上移动格栅板对应展现影片中具体点位；中三段多媒体屏幕展现浦东天际线，下固定模型展现杨浦大桥、民生码头8万吨筒仓等典型建筑，势必在有限的空间内，最大化体现不同的江岸尺度，诠释浦东如何真正做到了“还江于民、还岸于民”。',
				banners: [{
					"img": "/guide/2F/2-2 浦东天际线.jpg",
				}, {
					"img": "/guide/2F/2-3 浦东天际线.jpg"
				}],
				tips:{
					"imgBtn": "/guide/tips/点击屏幕-btn.png",
					"imgTips": "/guide/tips/点击屏幕-tips.png",
					"txt": "点击屏幕，了解更多。"
				},
				audio:"/guide/audio/浦东天际线.mp3"
			},
			{
				name: "畅游公园",
				desc: '“世纪公园、郊野公园……浦东建有足量优质的公园绿地”这里的某一处是否有你熟悉的场地？又或者承载了你儿时的部分记忆？想了解浦东？寻找自由呼吸的畅快感？那就从一座座公园开始吧。展区以公园模型壁挂图配合异形屏，乘坐浦东轨交线路，线上了解浦东内部的公园路线、点位等信息。',
				banners: [{
					"img": "/guide/2F/2-3 畅游公园-2.jpg"
				}],
				tips:{
					"imgBtn": "/guide/tips/点击屏幕-btn.png",
					"imgTips": "/guide/tips/点击屏幕-tips.png",
					"txt": "点击屏幕，了解更多。"
				},
				audio:"/guide/audio/畅游公园.mp3"
			},
			{
				name: "绿色动脉 活力水岸",
				desc: '经纬之间构建出的无限延展空间，以此展示浦东推动黄浦江两岸贯通及滨江岸线转型，还江于民、还岸于民、还景于民。漫步巨幅长墙边，依次了解绿色动脉、活力水岸等内容，通过浦东外环绿带及楔形绿地、国家级生态保护区、郊野公园、滨水空间，尽显现“草绿水美”的城市风光。',
				banners: [{
					"img": "/guide/2F/4-1 绿色动脉.jpg",
				}, {
					"img": "/guide/2F/5-1 活力水岸.jpg"
				}],
				tips:{
					"imgBtn": "/guide/tips/集音罩-btn.png",
					"imgTips": "/guide/tips/集音罩-tips.png",
					"txt": "在顶部集音罩下停留，听听自然的声音。"
				},
				audio:"/guide/audio/绿色动脉 活力水岸.mp3"
			},
			{
				name: "浦东林荫道",
				desc: '城市构建了日常中的“自然”。漫步在浦东林荫道，满目皆是醉人的斑斓景色。这里以22组标本为追随自然的线索，以上方透明半球罩为感受自然的媒介，在植物、风景、声音中寻找人与自身，与同伴，与其他物之间的联系，在浦东探索现代生活的自然性。',
				banners: [{
					"img": "/guide/2F/6-1 浦东林荫道.jpg"
				}],
				audio:"/guide/audio/浦东林荫道.mp3"
			},
			{
				name: "魅力都市",
				desc: '一方热土、魅力浦东。浦东空间跨度大，景观资源丰富，风貌类型多样。结合墙面写真，打造富有美感、充满韵律的城市天际线，彰显浦东国际化大都市的现代时尚。',
				banners: [{
					"img": "/guide/2F/8-1 魅力都市.jpg"
				}],
				audio:"/guide/audio/魅力都市.mp3"
			},
			{
				name: "历史街区",
				desc: '岁月在城市变迁中塑造了街道肌理，保存了城市记忆。他们是浦东历史的沉淀，更是浦东文化的见证和浦东情愫的寄托。通过对传统风貌特色历史文化名镇的模型复原、多媒体影像及互动查询多维度的展示手段，了解浦东在历史文化保护方面的相关信息及背后故事。',
				banners: [{
					"img": "/guide/2F/9-1 历史街区.jpg"
				}],
				audio:"/guide/audio/历史街区.mp3"
			},
			{
				name: "美丽乡村",
				desc: '“一河一路一风景，一宅一田一风情”。一弯清渠碧波荡漾、一条水泥路宽阔笔直，一道护栏排序整洁，浦东的乡村，不论何时去，都有一种全新的感觉。这里通过平面立体化展示手段，展现了浦东航头镇、泥城镇、书院镇等乡村的美丽画卷和浦东“美丽经济”的发展愿景。',
				banners: [{
					"img": "/guide/2F/10-1 美丽乡村.jpg",
				}],
				tips:{
					"imgBtn": "/guide/tips/点击屏幕-btn.png",
					"imgTips": "/guide/tips/点击屏幕-tips.png",
					"txt": "点击屏幕，了解更多。"
				},
				audio:"/guide/audio/美丽乡村.mp3"
			},
			{
				name: "骑行浦东",
				desc: '两条线路，一种心情，骑上浦东专属小座驾，随心挑选其中一种风景，屏幕中将选择该条路线，模拟真实步道场景，心随脚动，来一场痛快的浦东之行。',
				banners: [{
					"img": "/guide/2F/2-10 骑行浦东-1.jpg",
				},{
					"img": "/guide/2F/2-10 骑行浦东-2.jpg",
				}],
				tips:{
					"imgBtn": "/guide/tips/骑行-btn.png",
					"imgTips": "/guide/tips/骑行-tips.png",
					"txt": "骑上座驾，来一场痛快的浦东之行。"
				},
				audio:"/guide/audio/骑行浦东.mp3"
			},
			{
				name: "数字沙盘——《浦东十二时辰》",
				desc: '不论是早上畅快的在公园绿地上奔跑，中午肆意的在度假区旅游城玩耍，晚上便捷的在机场、站台出游……你想要的任何生活这里都有。浦东——他像一个时间记录者伴随着你的一天二十四小时，渗透进你生活的点点滴滴。体验无接触式空气屏，感受生活在浦东的美好。',
				banners: [{
					"img": "/guide/2F/11-1 数字沙盘——《浦东十二时辰》.jpg",
				}, {
					"img": "/guide/2F/11-2 数字沙盘——《浦东十二时辰》.jpg"
				}],
				tips:{
					"imgBtn": "/guide/tips/空气屏-btn.png",
					"imgTips": "/guide/tips/空气屏-tips.png",
					"txt": "隔空互动，体验无接触式空气屏。"
				},
				audio:"/guide/audio/数字沙盘-《浦东十二时辰》.mp3"
			},
			{
				name: "陆家嘴记忆",
				desc: '通过陆家嘴金融中心相关城市演变影片，展现浦东乃至中国改革开放的缩影。陆家嘴从当初的“烂泥渡路”华丽转身，变成了举世夺目的繁华之地。这里以“望远镜”作为回忆过去的媒介，设置4个回忆点，展示陆家嘴的发展历程。',
				banners: [{
					"img": "/guide/2F/16-2 陆家嘴记忆.jpg"
				}],
				audio:"/guide/audio/陆家嘴记忆.mp3"
			},
			{
				name: "幸福公园",
				desc: '建筑，最基础的组成单位，是城市文化表情的记录者。这里以浦东路网为地面基准生长延伸出图书馆等基础设施建筑、学校教育设施建筑、医院养老设施建筑三大点位。展现新时代人民群众幼有所育、学有所教、病有所医、老有所养。在不同路线的交错中，体验丰富而纷繁的宜居家园图景，感知在浦东人民对美好生活的向往如何获得实现，畅想人们繁衍生息的生活之道。',
				banners: [{
					"img": "/guide/2F/13-2 家门口的好学校.jpg"
				},{
					"img": "/guide/2F/12-2 生活中的好去处.jpg"
				},{
					"img": "/guide/2F/14-2 社区里的好设施.jpg"
				},{
					"img": "/guide/2F/15-1 电容墙.jpg"
				},{
					"img": "/guide/2F/15-2 电容墙.jpg"
				}],
				audio:"/guide/audio/幸福公园.mp3"
			},
			{
				name: "看不见的地下生命线",
				desc: '通过声光电展示手段与传统模型复原空间结合，直观的展示了作为城市运行“生命线”的地下综合管廊系统是如何运作的同时，认识给水、电力、通信、燃气等各类地下管线以及综合管廊打通城市地下脉络，畅通城市“血液”循环的重要作用。',
				banners: [{
					"img": "/guide/2F/17-1 看不见的地下生命线.jpg",
				}, {
					"img": "/guide/2F/17-2 看不见的地下生命线.jpg"
				}],
				tips:{
					"imgBtn": "/guide/tips/点击屏幕-btn.png",
					"imgTips": "/guide/tips/点击屏幕-tips.png",
					"txt": "点击屏幕，了解更多。"
				},
				audio:"/guide/audio/看不见的地下生命线.mp3"
			},
			{
				name: "缤纷社区体验空间——《种子》艺术装置",
				desc: '社区，承载的内涵不只是一处居所，更是一种生活方式和情感的寄托。无论是老人、小孩还是年轻人，皆能在社区生活中，找到属于自己的游憩领地。此处空间顶部采用了刘悦来老师为浦东所打造的艺术装置——《种子》。可在多媒体查询台上，更详细的了解浦东活力缤纷社区以及儿童友好空间建设。',
				banners: [{
					"img": "/guide/2F/19-1 缤纷社区体验空间——《种子》艺术装置.jpg",
				}, {
					"img": "/guide/2F/19-2 缤纷社区体验空间——《种子》艺术装置.jpg"
				}],
				tips:{
					"imgBtn": "/guide/tips/点击屏幕-btn.png",
					"imgTips": "/guide/tips/点击屏幕-tips.png",
					"txt": "点击屏幕，了解更多。"
				},
				audio:"/guide/audio/缤纷社区体验空间-《种子》艺术装置.mp3"
			},
			{
				name: "飞阅浦东",
				desc: '主题技术空间——飞行影院将多自由度动感平台设计与墙面投影影片巧妙联动。坐上悬空飞行器，身临其境翱翔于城市上空，“飞阅”大美浦东，感受超感官体验。',
				banners: [{
					"img": "/guide/2F/20-1 飞阅浦东.jpg",
				}],
				audio:"/guide/audio/飞阅浦东.mp3"
			},
			{
				name: "休息区",
				desc: '休息区位于二层中庭西侧。可在此通过多媒体查询屏了解浦东三十六个街镇发展战略及详尽信息。同时设有适合儿童娱乐&亲子娱乐的活动空间，创意打造了乐高墙，可以让儿童通过玩乐参与简易的城市规划概念的游戏行为，了解城市规划概念，尽情挥洒想象力。',
				banners: [{
					"img": "/guide/2F/21-1 二层休息区.jpg"
				},{
					"img": "/guide/2F/22-1 二层儿童区.jpg"
				}],
				audio:"/guide/audio/休息区.mp3"
			}
			]
		},
		{
			floor: "1F",
			points: [{
				name: "地球之眼",
				desc: '以“站在地球仪旁思考”为设计理念，抬眼间看巨型球幕中人类共生的蓝色星球，挥手间观立式巨幕中浦东与世界六大城市的城市形象。光影交错，两幕呼应联动，生动诠释浦东特有的城市底蕴，展现浦东开发伊始的战略定位——“开发浦东、振兴上海、服务全国、面向世界”。',
				banners: [{
					"img": "/guide/1F/地球之眼.jpg"
				}],
				audio:"/guide/audio/地球之眼.mp3"
			}]
		}
	]
	
	onLoad(options=>{
		console.log("onLoad", myPopup.value);
		let spotArr = [];
		let floorMap = {};
		getGuideSpotList().then(res=>{
			res.data.forEach(item=>{
				if(!floorMap[item.spotArea]){
					floorMap[item.spotArea] = [];
				}
				let spotObj = {
					id: item.id,
					name : item.spotName,
					desc : item.spotContent,
					audio : item.showVoices,
					tips : JSON.parse(item.tips),
					banners : item.showImgs.split("|").map(img=>{return {"img":img}})
				};
				floorMap[item.spotArea].push(spotObj);
			})
			for(let floor in floorMap){
				spotArr.push({"floor": floor,
							"points":floorMap[floor]
							})
			}
			if(options.Lz27kliI8p5j4k1L8AxW){	//是否传了ID（ID稍微作了一点复杂化
				let paramsId = options.Lz27kliI8p5j4k1L8AxW.substr(31)
				let selectSpot = res.data.filter(item=>item.id==paramsId)[0]
				if(selectSpot){
					data.selectFloor = selectSpot.spotArea
					data.selectPoint = selectSpot.spotName
				}else{
					uni.showModal({
						content: '系统繁忙，spot id is invalid',
						showCancel: false,
						complete:()=>{
						}
					});
					data.selectFloor = res.data[0].spotArea
					data.selectPoint = res.data[0].spotName
				}
			}else{
				data.selectFloor = res.data[0].spotArea
				data.selectPoint = res.data[0].spotName
			}
			data.list = spotArr
		})
		
	})
	
	
</script>

<style>
	page {
		background: #F7F9FA;
	}

</style>
<style scoped>
	@import '../common/common.css';
	@import 'guide.css';
</style>