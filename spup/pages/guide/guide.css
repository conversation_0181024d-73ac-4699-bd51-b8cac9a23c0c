	.complete{
		margin: 10px 15px 0 15px;
		border-radius: 15px 15px 0 0;
		background: #FCFEFF;
		box-shadow: 0px 0px 27px 0px rgba(247,249,250,0.8);
	}
	.hr-box{
		position: relative;
		padding: 16px 0;
	}
	.hr-twill{
		position: absolute;
		border: 0;
		width: 47px;
		height: 6px;
		margin-top: -2px;
		margin-left: 28px;
		background: linear-gradient(-45deg, #000 25%, #F7F9FA 0, #F7F9FA 50%, #000 0, #000 75%, #F7F9FA 0);
		background-size: 8px 8px;
	}
	.hr-edge-weak{
		border: 0;
		width: 100%;
		height: 1px;
		background: #000;
	}
	/* 滚动的banner ---------- start ---------*/
	.swiper-box { 
		height:500rpx;
		padding:20px 15px 0 15px;
	}
	
	.my-swiper-item {
		width: 100%;
		color: #fff;
	}
	.swiper-img {
		border-radius: 8px;
	}
	/* 滚动的banner ---------- end ---------*/
	
	/* // 上部 */
	.pd-title{
		font-size: 18px;
		font-weight: 600;
		padding: 0 15px;
	}
	.pd-text{
		font-size: 13px;
		color: #000000;
		line-height: 26px;
		text-indent: 26px;
		padding: 5px 15px 10px 15px;
	}
	
	/* 底部 ------------- start ------ */
	.footer-box{
		background: rgba(250,250,250,1);
		box-shadow: 0 4px 13px 0 rgba(0, 0, 0, 0.2);
		position: absolute;
		padding: 10px 0;
		bottom: 0;
		width: 100%;
	}
	.footer{
	
	}
	/* 导览点位 ------------- start ------ */
	.point-wrap{
		display: flex;
		/* flex-wrap: wrap; */
		width: 100%;
		padding: 10rpx 20rpx 22rpx 20rpx;
		overflow-x: auto;
	}
	.point-item{
		background: #fff;
		border: 1px solid #f2f2f2;
		padding: 4rpx 16rpx;
		border-radius: 16rpx;
		white-space: nowrap;
		margin: 2px 5px;
	}
	.point-item-selected{
		background: #8d5b6e;
		color: #fff;
	}
	/* 导览点位 ------------- end ------ */
	/* 楼层 ------------- start ------ */
	.floor-wrap{
		display: flex;
		width: 100%;
		padding-bottom: 10px;
	}
	.floor-item{
		flex : 1;
		text-align: center;
		background: #f2f2f2;
		font-weight: 500;
		padding:6px 0;
	}
	.floor-item-selected{
		background: #faf4f6;
		color: var(--button-color);
		font-weight: bold;
	}
	/* 楼层 ------------- end ------ */
	/* 底部 ------------- end ------ */
	
	/* 弹窗 */
	.popup-content{
		background-color:rgba(0, 0, 0, 0.6);
		border-radius: 20rpx ;
		color: #fff ;
		padding: 20rpx 30rpx;
		width: 406rpx;
		height: 462rpx;
	}
	.popup-title{
		text-align: center;
		font-size: 16px;
		font-weight: 600;
	}
	.popup-txt{
		font-size: 14px;
		padding: 10px 0;
	}
	.popup-img{
		width: 100%;
	}
	/* 弹窗 */