.fol-fow{
	display: flex;
}
.fol-col{
	flex: 1;
}
.complete {
	padding: 0 15px 105px 15px;
}

/* 按钮 */
.btn-box {
	width: 100%;
	/* #ifdef H5 */
	left: var(--window-left);
	right: var(--window-right);
	/* #endif */
	 position: fixed;
	bottom: 0;
	/* margin-bottom: 10px; */
}

.btn-right-site {
	text-align: center;
}


/*  */
.center {
	text-align: center;
}

.active-list {
	padding-bottom: 10px;
	padding-top: 20px;
	border-bottom: 0.8px solid #f0f0f0;
}

.active-img {
	position: relative;
	box-sizing: border-box;
	width: 150px;
}
.active-img2 {
	margin-right: 5px;
	position: relative;
	box-sizing: border-box;
	width: 20%; /* 或者其他百分比 */
	  height: calc(20% * (2/1)); /* 根据宽度的60%来计算高度 */
}
.title-head {
	font-size: 14px;
	padding-top: 10px;
	font-weight: 500;
}
.title-head2 {
	font-size: 15px;
	font-weight: 500;
}
.label{
	font-size: 12px;
	color: #815462;
	width: fit-content;
	margin: 5px 0;
}
.float-list image {
	width: 100%;
	height: 100%;
}

.tip {
	position: absolute;
	display: inline-block;
	font-size: 12px;
	color: #ffffff;
	top: 0;
	left: 0;
	padding: 0 3px;
	background-color: var(--button-color);
	z-index: 2;
}

.text-box {
	margin-left: 10px;
}



.btn {
	font-size: 14px;
	padding: 0;
	text-align: right;
	overflow: hidden;
	/* position: absolute; */
	bottom: 0;
}
.btn-s {
		font-size: 14px;
		line-height: 2;
		color: #fff;
		border: 1px solid var(--button-color);
		color:var(--button-color);
		border-radius: var(--button-small-radius);
	}
	

/*  */
.details-box {
	background: #fff;
	border-radius: 4px;
	margin-top: 15px;
}

.details-txt {
	/* text-indent: 1rem; */
}
.wrap-head{
	color: #fff;
	font-weight: 600;
	padding: 8px 15px;
	background: #b3697f;
}

>>> .details-txt  img {
  max-width: calc( 100vw - 32px ) !important;
  object-fit: scale-down;
  height: auto !important;
}

.top15 {
	margin-top: 15px;
}

.details-img {
	margin: 15px auto 10px auto;
}

/*  */
.top-line {
	border-top: 1px solid #d9d9d9;
	padding-top: 2px;
}

.bottom-line {
	border-bottom: 1px solid #d9d9d9;
	padding-bottom: 25px;
}

.left15 {
	margin-left: 15px;
}

.txt-right {
	text-align: right;
}

.line-height {
	line-height: 2.5em;
}

/* active-tip*/
.tips-box {
	text-align: center;
	margin-top: 60px;
}

.tips-icon {
	width: 132px;
	margin: auto;
	padding-bottom: 10px;
}

.tips-span {
	font-size: 14px;
	margin-top: 10px;
	color: #999999;
}
.tips-massges{
	background-color: #fff;
	border: 1px solid #d9d9d9;
	border-radius: 0.5em;
	padding:10px 15px;
	margin: 0 30px;
	text-align: left;
	font-size: 14px;
	margin-top: 25px;
}
.size-padding{
	font-size: 16px;
	padding-bottom:10px;
}
.btn-add-label{
	font-size: 12px;
	color: #999999;
}
.btn-add{
	height: 30px;
	float: right;
	font-size: 14px;
	line-height: 30px;
	border: 1px solid #000000;
}
.information-text {
	border: 1px solid #000000;
	border-radius: var(--button-small-radius);
	padding: 6px 10px;
	margin: 10px 0 10px 0;
}
.info-content{
	border: 0.8px solid #d9d9d9;
	background: #fff;
	border-radius: 10px;
	margin: 10px 0;
	padding: 15px 20px;
	cursor : pointer;
}

.time-bottom{
	border-bottom: 0.8px solid #d9d9d9;
	padding-bottom: 10px;
	font-size: 13px;
	color: #999;
}
.size-span{
	font-size: 13px;
	color: #000000;
	padding-top: 5px;
}
.size-line{
	line-height: 2;
}
.info-box{
	border-top: dashed 0.8px #d9d9d9;
	padding: 10px 15px;
}
.dashed-bottom{
	position: relative;
	line-height: 3em;
}
.dashed-bottom::before{
	content: '';
	    position: absolute;
	   left: auto;
	    top:5;
	    bottom:5 ;
		    border-bottom-width: 0.8px;
		    border-bottom-style: dashed;
		    border-bottom-color: #d9d9d9;
	    width: 100%;
	    /* background-color: rgba(0, 0, 0, 0.8500); */
}

/* 按钮 */
.btn-boxs {
		width: 100%;
		display: flex;
		position: fixed;
		padding: 15px 0;
		/* #ifdef H5 */
		left: var(--window-left);
		right: var(--window-right);
		/* #endif */
		bottom: 0;
		font-weight: 400;
		z-index: 1;
	}
	.btn-box {
		width: 100%;
		display: flex;
		position: fixed;
		padding: 15px 0;
		/* #ifdef H5 */
		left: var(--window-left);
		right: var(--window-right);
		/* #endif */
		bottom: 0;
		border-top: 1px solid #000;
	    background: #F7F9FA;
		font-weight: 400;
		z-index: 1;
	}
	
	.btn-right-site {
		text-align: right;
	}
	
	.btn-ss {
		width: 94px;
		margin-bottom: 10px;
		margin-right: 15px;
		/* padding: 3px 0; */
		font-size: 14px;
		color: #fff;
		background: var(--button-color);
		border-radius: var(--button-small-radius);
	}

.batch-wrap{
	padding-bottom: 36px;
	max-height: 500px;
	overflow-y: auto;
}
.batch-item{
	border: 0.7px #000 solid;
	border-radius: 5px;
	margin: 15px 15px;
	padding: 10px 10px;
}
.batch-name{
	flex:1;
	font-size: 16px;
	margin-bottom: 5px;
}
.batch-status{
	/* color:#ff0000; */
	margin: 0 10px 0 0;
}
.batch-count{
	color : #000;
	font-size: 13px;
	text-align: left;
}
.batch-time{
	color : #000;
	font-size: 13px;
}
.batch-btn-wrap{
	margin-top: 10px;
	display: flex;
}

.costomers-wrap{
	color : #8c8c8c;
	font-size: 12px;
	flex:1;
}
.costomer-wrap{
	border: 1px solid #d9d9d9;
	border-radius: 5px;
	margin: 4px 1px;
	padding: 4px 10px;
}
/*  */
.card-box{
	border: 0.7px solid #999;
	padding: 10px 15px;
	margin:15px;
	border-radius: var(--button-small-radius);
	background: #fafcfc;
}
.card-line {
	padding-bottom: 8px;
	margin-bottom: 8px;
	color: #000000;
	border-bottom:0.7px solid #d9d9d9;
}
.txt-box {
	line-height: 28px;
	color: #000;

}
.common-btn {
	border-top: 0.7px solid #d9d9d9;
	margin-top: 12px;
	padding-top: 10px;
}
.btn-img{
	width: 13px;
	display: inline-block;
}
.mini-btn {
	margin-left: 9px !important;
	color: #000 !important;
}
.btn-site{
	text-align: center;
}
.btn-hl{
	border-right: 0.7px solid #d9d9d9;
}
.btn-hr{
	border-left: 0.7px solid #d9d9d9;
}
.bold{
	font-weight: 500;
	color: #000;
}
.round-status {
  font-weight: 600;
  margin-left: 20px;
  color: #999;
}
.round-status-READY, .round-status-SUBMITSTOPPED, .round-status-WAITING{
	color: #fa7d01;
}
.round-status-SUBMITTING, .round-status-RUNNING{
	color: #4c9e44;
}
