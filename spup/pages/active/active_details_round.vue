<template>
<view>
	<!-- 普通弹窗 -->
	<uni-popup ref="popup" background-color="#fff" @change="change">
		<view style="">
			<view class="fol-row" style="padding: 15px 15px;border-bottom: 0.7px solid #f1f1f1;">
				<view class="fol-col" style="font-size: 18px;text-align: center;">预约场次</view>
				<view><uni-icons type="closeempty" size="20" @click="colosePopup"></uni-icons></view>
			</view>
		</view>
		<view class="batch-wrap" >
			<view  style="margin: 10px 15px;font-size: 16px;">活动场次</view>
			<view v-if="!data.detail.rounds||!data.detail.rounds.length" style="color:#ccc;margin: 10px 15px;">暂无场次</view>
			<template v-for="(item, index) in data.detail.rounds" :key="item.round.id" :itemData='item'>
				<view class="batch-item">
					<view style="display: flex;padding-top: 3px;">
						<view class="batch-name">{{item.round.actRoundInfo}}</view>
						<!-- <view class="batch-status">{{item.extInfo.buttonInfo}}</view> -->
					</view>
					<view class="batch-count" style="color:#8c8c8c;padding: 3px 0;">
						<!-- 活动名额：{{item.round.actRoundSubmitNumber}} / {{item.round.actRoundMaxSubmitNum}} -->
						活动名额：<text>{{item.round.actRoundMaxSubmitNum}}</text>
					</view>
					<view class="batch-time" style="color:#8c8c8c;padding: 3px 0;">
						活动时间：{{item.round.actRoundStartDateTime.substring(0,16)}} 至 {{item.round.actRoundEndDateTime.substring(0,16)}}
					</view>
					<view class="fol-fow">
						<view class="fol-col"></view>
						<view style="margin-top: 5px;">
							<button v-if="item.extInfo.greyButton" class="btn-color-big btn-disable" disabled="true">{{item.extInfo.buttonInfo}}</button>
							<button v-else class="btn-color-big"  @click="entry(item.round)">{{item.extInfo.buttonInfo}}</button>
						</view>
						
					</view>
					<!-- <view class="batch-time">
						报名时间：{{item.round.actRoundSubmitStartDateTime.substring(0,16)}} 至 {{item.round.actRoundSubmitEndDateTime.substring(0,16)}} 
					</view> -->
					<view class="costomers-wrap" v-if="false">
						<view style="font-weight: 600;">报名人员信息：</view>
						<view style="flex:1;">
							<view class="costomer-wrap" v-for="(costomer) in item.submitCustomers">
								<view>姓名：{{costomer.username}} {{costomer.type=='CHILD'?"（儿童）":""}}</view>
								<view v-if="costomer.type!='CHILD'">手机：{{costomer.phoneString}}</view>
								<!-- <view>{{SPUP_GLOBAL_CONFIG.idType.find(idItem=>idItem.id==costomer.passType)["name"]}}:{{costomer.passString}}</view> -->
							</view>
						</view>
					</view>
				</view>
			</template>
		</view>
	</uni-popup>
</view>
</template>

<script setup>
	import {ref,watch,reactive,computed,onMounted,getCurrentInstance,toRefs, toRef} from 'vue'
	import {useCommonStore} from '/stores/common.js'
	import dayjs from "dayjs"
	const props = defineProps(['visible', 'activityId', 'scene']);
	// const { goodsInfo, orderInfo} = toRefs(props);	//通过toRefs实现：当父组件属性变化时，子组件的相关属性也会变化 
	const emit = defineEmits(['update:visible']);
	const commonStore = useCommonStore();
	import {getActivityDetail, getActivityRound, activityCancelSubmit} from "@/common/api/server.js";
	
	
	const data = reactive({
		listTips:"数据加载中……",
		id:0,
		detail:{
			rounds:[]
		}
	})
	
	//预约弹窗
	const popup = ref();
	//通过computed实现父子组件的属性双向绑定
	const visible = computed({
		// 子组件v-model绑定 计算属性, 一旦发生变化, 就会给父组件传递值
		get: () => props.visible,
		set: (nv) => {
			emit('update:visible', nv)
		}
	})
	const change = (e) =>{
		visible.value = e.show;
		if(visible.value){
			
		}else{
			
		}
	};
	watch(visible, (value) => {
		console.log(111);
		if(value){
			data.id = props.activityId;
			getActivityRound(data.id).then(res=>{
				// let tmpArr = res.data.filter(item=>item.roundVo.actRound.status=="WAITING"||item.roundVo.actRound.status=="SUBMITTING"||item.roundVo.actRound.status=="RUNNING");
				let tmpArr = res.data.filter(item=>item.roundVo.visible);
				data.detail.rounds =  tmpArr.map(item=>{
					return {round:item.roundVo.actRound, extInfo:{buttonInfo:item.roundVo.buttonInfo, greyButton:item.roundVo.greyButton}}
				});
				// data.detail.rounds = res.data.filter(item=>item.round.status=="WAITING"||item.round.status=="SUBMITTING"||item.round.status=="RUNNING") 
				if(data.detail.rounds.length==0){
					data.listTips = "暂无场次" 
				}
			})
			popup.value.open("bottom");
		}else{
			popup.value.close();
		}
	});
	
	
	const colosePopup = ()=>{
		visible.value = false;
	}


	//报名
	const entry = (item) => {	
		if(props.scene=="preview"){
			uni.showToast({
				icon: "error",
				title: '预览模式不能报名'
			})
			const elements = document.querySelectorAll('.uni-toast');
			elements.forEach(element => {
			    element.style.width = '10em';
			    element.style.setProperty('width', '10em', 'important');
			});
			const elements2 = document.querySelectorAll('.uni-toast__content');
			elements2.forEach(element => {
			    element.style.setProperty('margin', '7px 0 15px');
			});
			
		}else{
			commonStore.curActiveRound = item
			uni.navigateTo({
				url: "/pages/active/active_signup?id="+data.id
			})
		}
	}
	//取消报名
	const cancel = item=>{
		uni.showModal({
			title: "取消报名",
			content: "确定要场次《"+item.actRoundInfo+"》的报名吗？",
			cancelText: "再想一想",
			confirmText: "取消报名",
			success: function(res0) {
				if(!res0.cancel){
					activityCancelSubmit(item.actRoundId).then(res=>{
						console.log(res);
						data.detail.rounds.forEach((round, index)=>{
							if(round.round.actRoundId==item.actRoundId){
								round.hasSubmitted = false
								round.submitCustomers = []
							}
						})
						if(res.code==0){
							uni.showToast({
								icon: "success",
								title: '取消成功'
							})
						}else{
							uni.showModal({
								content: res.message,
								showCancel: false
							})
						}
					})
				}
			}
		})
	}
	
	
</script>

<style scoped>
	@import '../common/common.css';
	@import "./active.css";
</style>
<style>
	page{
		background: #fff;
	}
	.uni-toast {
	    width: 10em !important; 
	}
	.uni-toast__content {
	    margin: 7px 0 15px;
	}
</style>