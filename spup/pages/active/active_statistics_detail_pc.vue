<template>
	<view>
		<view style="display: flex;">
			<view style="margin: 10px;font-weight: 600;flex:1">{{data.name}}</view>
			<button class="btn-color" @click="exportExcel()" style="margin: 10px 16px 0 0;">一键导出</button>
			<button class="btn-white" @click="turnBack()" style="margin: 10px 16px 0 0;">返回</button>
		</view>
		<view class="table-wrap">
			<uni-table border stripe emptyText="还没有人报名~" class="table-self">
				<!-- 表头行 -->
				<uni-tr>
					<uni-th align="center">序号</uni-th>
					<uni-th align="center">场次</uni-th>
					<uni-th align="center">姓名</uni-th>
					<uni-th align="left">手机号</uni-th>
				<!-- 	<uni-th align="left">证件类型</uni-th>
					<uni-th align="left">证件号码</uni-th> -->
					<uni-th align="left">性别</uni-th>
					<uni-th align="left">年龄</uni-th>
					<uni-th align="left">是否儿童</uni-th>
					<uni-th align="left">报名时间</uni-th>
				</uni-tr>
				<!-- 表格数据行 -->
				<uni-tr  v-for="(costomer, index) in data.list" :key="costomer.id">
					<uni-td align="center">{{index+1}}</uni-td>
					<uni-td>{{data.rounds[costomer.actRoundId].actRoundInfo}}</uni-td>
					<uni-td>{{costomer.username}}</uni-td>
					<uni-td>{{costomer.phoneString}}</uni-td>
					<!-- <uni-td>{{SPUP_GLOBAL_CONFIG.idType.find(idItem=>idItem.id==costomer.passType)["name"]}}</uni-td>
					<uni-td>{{costomer.passString}}</uni-td> -->
					<uni-td>{{costomer.gender?(costomer.gender==1?"男":"女"):""}}</uni-td>
					<uni-td>{{costomer.age==300?"":costomer.age}}</uni-td>
					<uni-td>{{costomer.type=='CHILD'?"儿童":""}}</uni-td>
					<uni-td>{{costomer.createOn}}</uni-td>
				</uni-tr>
			</uni-table>
		</view>
		
	</view>
</template>

<script setup>
	import {ref,reactive,onMounted,computed,watch} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import dayjs from "dayjs";
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {getActivityRound, getActivitySubmit} from "@/common/api/server.js";
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import * as XLSX from 'xlsx'
	
	const data = reactive({
		id:0,
		name:"",
		list:[],
		rounds:{}
	})
	
	//返回
	const turnBack = ()=>{
		uni.redirectTo({
			url: "/pages/active/active_statistics"
		})
	}
	
	//新方式，由前台导出excel
	const exportExcel = (item) => {
		let tableData = [
		  ['活动名称', '场次', '姓名', '手机号', "性别", "年龄", "是否儿童", "报名时间"]//导出表头
		] // 表格表头
		data.list.forEach((costomer, index) => {
		  let rowData = []
		  //导出内容的字段
		  rowData = [
			data.name,
			data.rounds[costomer.actRoundId].actRoundInfo,
			costomer.username,
			costomer.phoneString,
			costomer.gender?(costomer.gender==1?"男":"女"):"",
			costomer.age==300?"":costomer.age,
			SPUP_GLOBAL_CONFIG.roundSignUserType.find(typeItem=>typeItem.id==costomer.type)["name"],
			costomer.createOn
		  ]
		  tableData.push(rowData)
		})
		let workSheet = XLSX.utils.aoa_to_sheet(tableData);
		let bookNew = XLSX.utils.book_new();
		XLSX.utils.book_append_sheet(bookNew, workSheet, '报名人员') // 工作簿名称
		let name = "《"+data.name+'》报名人员' + '.xlsx'
		XLSX.writeFile(bookNew, name) // 保存的文件名
	}
	
	onLoad(options => { 
		if(options.id){
			data.id = options.id
			data.name = options.name
			getActivityRound(data.id).then(res=>{
				let roundsObj = {}
				res.data.forEach(round=>{
					roundsObj[round.roundVo.actRound.actRoundId] = round.roundVo.actRound
				})
				data.rounds = roundsObj
				getActivitySubmit(data.id).then(res=>{
					data.list = res.data
				})
			})
			
		}else{
			uni.showModal({
				content: '参数错误',
				showCancel: false
			});
		}
	})
</script>

<style>
	page{
		background: #F7F9FA;
	}
	table{
		min-width: 100px !important;
		width: 100px;
	}
</style>
<style scoped>
	@import '../common/common.css';
	@import "./active.css";
	
	.table-wrap{
		margin: 0 15px 0px 15px;
	}
	.table-self{
		/* width: calc(100% - 30px); */
		min-width: 100px !important;
	}
	
</style>