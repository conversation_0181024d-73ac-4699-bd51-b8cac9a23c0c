<template>
	<view >
		<view class="complete"> 
			<view class="title-head center" style="margin-top: 15px;font-size: 18px;">
				{{data.detail.activityName}}
			</view>
			<view class="size-regular top15 details-txt">
				<!-- <view>
					 {{ data.detail.introductionInfo }}
				</view> -->
				<div class="ck-content" v-html='data.detail.introductionInfo'></div>
				<!-- <rich-text :nodes="data.detail.introductionInfo" class="details-txt">
				</rich-text> -->
			</view>
			<!-- <view class="details-img">
				<image  mode="widthFix" :src="data.detail.converPicture"></image> 
			</view> -->
		<!-- 	<view>活动地点：{{data.detail.address}}</view>
			<view>活动时间：{{dayjs(data.detail.startTime).format("YYYY/MM/DD")}} - {{dayjs(data.detail.endTime).format("YYYY/MM/DD")}}</view>
			<view>报名截止：{{dayjs(data.entry.entryStartTime).format("YYYY/MM/DD")}} - {{dayjs(data.entry.entryEndTime).format("YYYY/MM/DD")}}</view>
			<view>剩余名额：{{data.entry.entryRemind}}/{{data.entry.entryLimit}}人</view> -->
			
			<!-- <view class="batch-wrap" >
				<view>&nbsp;</view>
				<view style="margin: 0 0 3px 9px;">场次：</view>
				<view v-if="!data.detail.rounds||!data.detail.rounds.length" style="color:#ccc;margin: 0 0 3px 9px;">暂无场次</view>
				<template v-for="(item, index) in data.detail.rounds" :key="item.round.id" :itemData='item'>
					<view class="batch-item">
						<view style="display: flex;">
							<view class="batch-name">{{item.round.actRoundInfo}}</view>
						</view>
						<view class="batch-count">
							活动名额：{{item.round.actRoundMaxSubmitNum}}
						</view>
						<view class="batch-time">
							活动时间：{{item.round.actRoundStartDateTime.substring(0,16)}} 至 {{item.round.actRoundEndDateTime.substring(0,16)}}
						</view>
						<view class="costomers-wrap" v-if="false">
							<view style="font-weight: 600;">报名人员信息：</view>
							<view style="flex:1;">
								<view class="costomer-wrap" v-for="(costomer) in item.submitCustomers">
									<view>姓名：{{costomer.username}} {{costomer.type=='CHILD'?"（儿童）":""}}</view>
									<view v-if="costomer.type!='CHILD'">手机：{{costomer.phoneString}}</view>
								</view>
							</view>
						</view>
						<view class="batch-btn-wrap" >
							<view style="flex:1"></view>
							<button v-if="item.extInfo.greyButton" class="btn-color-big btn-disable" disabled="true">{{item.extInfo.buttonInfo}}</button>
							<button v-else class="btn-color-big"  @click="entry(item.round)">{{item.extInfo.buttonInfo}}</button>
						</view>
					</view>
				</template>
			</view> -->
		
		</view>
		<!-- 按钮 -->
	<!-- 	<view class="btn-box">
			<view class="complete">
				<button class="btn-color-huge" @click="signupClick">报名</button>
			</view>
		</view> -->
		
		<view class="btn-box fol-row" style="background: #fff;">
			<view class="fol-col" ></view>
			<view class="btn-right-site">
				<!-- <button v-if="data.scene=='preview'" class="btn-ss btn-color-huge" @click="backClick">返回</button> -->
				<button class="btn-ss btn-color-huge" @click="signupClick">报名</button>
			</view>
		</view>
		
	</view>
	
	<ActiveDetailsRound v-model:visible="activeDetailsRoundVisible" :activityId="data.id" :scene='data.scene'/>
</template>

<script setup>
	import {ref,reactive,onMounted,computed} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import {onShow,onHide} from '@dcloudio/uni-app';
	import {useCommonStore} from '@/stores/common.js';
	import ActiveDetailsRound from "./active_details_round.vue"
	const commonStore = useCommonStore();
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import {getActivityDetail, getActivityRound, activityCancelSubmit} from "@/common/api/server.js";
	import dayjs from "dayjs";

	const data = reactive({
		id:0,
		scene:"",
		detail:{
			rounds:[]
		}
	})
	const activeDetailsRoundVisible = ref(false);
	// //带参数的计算属性
	// const signupStatusLabel = computed(() => (item) => {
	// 	if(!item.hasSubmitted){
	// 		let now = dayjs().valueOf()
	// 		if(now<dayjs(item.round.actRoundSubmitStartDateTime).valueOf()){
	// 			return "报名尚未开始"
	// 		}else if(now>dayjs(item.round.actRoundSubmitEndDateTime).valueOf()){
	// 			return "报名已经结束"
	// 		}else if(item.round.actRoundMaxSubmitNum<=item.round.actRoundSubmitNumber){
	// 			return "名额已满"
	// 		}
	// 	}
	// 	return ""
	// })
	
	//点击报名按钮
	const signupClick = ()=>{
		console.log("signupClick");
		activeDetailsRoundVisible.value = true;
	}
	
	//点击报名按钮
	const backClick = ()=>{
		uni.navigateTo({
			url: "/pages/active/active_preview_list"
		})
	}
	// //报名 
	// const entry = (item) => {	
	// 	commonStore.curActiveRound = item
	// 	uni.navigateTo({
	// 		url: "/pages/active/active_signup?id="+data.id
	// 	})
	// }
	// //取消报名
	// const cancel = item=>{
	// 	uni.showModal({
	// 		title: "取消报名",
	// 		content: "确定要场次《"+item.actRoundInfo+"》的报名吗？",
	// 		cancelText: "再想一想",
	// 		confirmText: "取消报名",
	// 		success: function(res0) {
	// 			if(!res0.cancel){
	// 				activityCancelSubmit(item.actRoundId).then(res=>{
	// 					console.log(res);
	// 					data.detail.rounds.forEach((round, index)=>{
	// 						if(round.round.actRoundId==item.actRoundId){
	// 							round.hasSubmitted = false
	// 							round.submitCustomers = []
	// 						}
	// 					})
	// 					if(res.code==0){
	// 						uni.showToast({
	// 							icon: "success",
	// 							title: '取消成功'
	// 						})
	// 					}else{
	// 						uni.showModal({
	// 							content: res.message,
	// 							showCancel: false
	// 						})
	// 					}
	// 				})
	// 			}
	// 		}
	// 	})
	// }
	
	onLoad(options => {
		console.log("options:", options);
		if(options.scene){
			data.scene = options.scene;
		}
		if(options.id){
			data.id = options.id
			getActivityDetail(data.id).then(res=>{
				data.detail = res.data.activity
				getActivityRound(data.detail.activityId).then(res=>{
					// let tmpArr = res.data.filter(item=>item.roundVo.actRound.status=="WAITING"||item.roundVo.actRound.status=="SUBMITTING"||item.roundVo.actRound.status=="RUNNING");
					let tmpArr = res.data.filter(item=>item.roundVo.visible);
					data.detail.rounds =  tmpArr.map(item=>{
						return {round:item.roundVo.actRound, extInfo:{buttonInfo:item.roundVo.buttonInfo, greyButton:item.roundVo.greyButton}}
					});
					// data.detail.rounds = res.data.filter(item=>item.round.status=="WAITING"||item.round.status=="SUBMITTING"||item.round.status=="RUNNING") 
				})
			})
		}else{
			uni.showModal({
				content: '参数错误',
				showCancel: false
			});
		}
		commonStore.curActiveRound = {}
	})
	
	
	onHide(()=>{
		activeDetailsRoundVisible.value = false;
	})	
</script>
<style>
	page{
		background:#fff ;
	}
</style>
<style scoped>
	@import '../common/common.css';
	@import "./active.css";
	
</style>
