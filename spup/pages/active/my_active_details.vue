<template>
	<view class="complete" style="padding-bottom: 80px;">
		<view class="details-box">
		<!-- 	<view class="title-head center">
				{{data.detail.activityName}}
			</view> -->
			<!-- <view class="size-regular top15 details-txt"> -->
				<!-- <rich-text :nodes="data.detail.introductionInfo" class="details-txt">
				</rich-text> -->
			<!-- 	<div v-html='data.detail.introductionInfo'></div> -->
			<!-- </view> -->
			<!-- <view class="details-img">
				<image  mode="widthFix" :src="data.detail.converPicture"></image> 
			</view> -->
		<!-- 	<view>活动地点：{{data.detail.address}}</view>
			<view>活动时间：{{dayjs(data.detail.startTime).format("YYYY/MM/DD")}} - {{dayjs(data.detail.endTime).format("YYYY/MM/DD")}}</view>
			<view>报名截止：{{dayjs(data.entry.entryStartTime).format("YYYY/MM/DD")}} - {{dayjs(data.entry.entryEndTime).format("YYYY/MM/DD")}}</view>
			<view>剩余名额：{{data.entry.entryRemind}}/{{data.entry.entryLimit}}人</view> -->
			
			<view class="batch-wrap" >
				<template v-for="(item, index) in curRound" :key="item.round.id" :itemData='item'>
					<!-- 活动状态 -->
					<view class="fol-row wrap-head">
						<view class="fol-col">活动状态</view>
						<view class="batch-status">{{signupStatusLabel(item)}}</view>
					</view>
					<view class="size-regular size-line" style="padding: 0 15px 8px 15px">
						<!-- 活动信息 -->
						<view class="fol-row" style="padding-top: 10px;">
							<view class="batch-count ">活动场次：</view>
							<view class="fol-col">{{item.round.actRoundInfo}}</view>
						</view>
						<!-- <view class="batch-count"> -->
							<!-- 活动名额：{{item.round.actRoundSubmitNumber}} / {{item.round.actRoundMaxSubmitNum}} -->
						<!-- 	活动名额：{{item.round.actRoundMaxSubmitNum}} -->
						<!-- </view> -->
						<view class="fol-row">
							<view class="batch-count ">活动时间：</view>
							<view class="fol-col">
								{{item.round.actRoundStartDateTime.substring(0,16)}} 至 {{item.round.actRoundEndDateTime.substring(0,16)}}
							</view>
						</view>
					</view>
						<!-- <view class="batch-time">
							报名时间：{{item.round.actRoundSubmitStartDateTime.substring(0,16)}} 至 {{item.round.actRoundSubmitEndDateTime.substring(0,16)}} 
						</view> -->
						<view class="info-box" v-if="item.submitCustomers.length">
							<view style="font-weight: 600;padding-bottom: 5px;">报名人员信息：</view>
							<view>
								<view  v-for="(costomer) in item.submitCustomers">
									<view class="fol-row dashed-bottom">
										<view class="batch-count">姓名：</view>
										<view class="fol-col">{{costomer.username}} {{costomer.type=='CHILD'?"（儿童）":""}}</view>
									</view>
									<!-- <view class="fol-row dashed-bottom" v-if="costomer.type!='CHILD'"> -->
									<view class="fol-row dashed-bottom">
										<view class="batch-count">手机号：</view>
										<view class="fol-col" >{{costomer.phoneString}}</view>
									</view>
									<!-- <view>{{SPUP_GLOBAL_CONFIG.idType.find(idItem=>idItem.id==costomer.passType)["name"]}}:{{costomer.passString}}</view> -->
								</view>
							</view>
						</view>
						<view class="btn-qx">
							<button class="btn-qx-n" v-if="item.submitCustomers.length&&item.submitCustomers[0].status=='SUBMITTED'"  @click="cancel(item.round)">取消报名</button>
							<button class="btn-qx-s" v-if="item.submitCustomers.length&&item.submitCustomers[0].status=='SUBMITTED'"  @click="checkin(item)">签到</button>
						</view>
				</template>
			</view>
		</view>
		<view style="background: #eef1f2;border-radius: 5px;margin-top: 15px;padding: 15px;">
			<view class="tip-box">温馨提示</view>
			<text>{{curRound&&curRound.length ? curRound[0].round.otherInfo.tips : ""}}</text>
		</view>
	</view>
</template>

<script setup>
	import {ref,reactive,onMounted,computed} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import {getActivityDetail, getActivityRound, activityCheckin, activityCancelSubmit} from "@/common/api/server.js";
	import dayjs from "dayjs";

	const data = reactive({
		id:0,
		actRoundId:0,
		detail:{
			rounds:[]
		}
	})
	
	const curRound = computed(()=>{
		let tempArr = []
		if(data.detail.rounds&&data.detail.rounds.length){
			tempArr = data.detail.rounds.filter(item=>data.actRoundId==item.round.actRoundId)
			console.log("tempArr:", tempArr);
		}
		console.log("data.detail.rounds:", data.detail.rounds);
		return tempArr
	})
	
	
	//带参数的计算属性
	const signupStatusLabel = computed(() => (item) => {
		console.log("signupStatusLabel:", item);
		if(item.submitCustomers.length>0){
			let roundSignupStatus = SPUP_GLOBAL_CONFIG.roundSignupStatus.find(tmpItem=>tmpItem.id==item.submitCustomers[0]["status"])
			if(roundSignupStatus){
				return roundSignupStatus["name"]
			}
		}
		return ""
	})
	
	//报名 
	const entry = (item) => {	
		commonStore.curActiveRound = item
		uni.navigateTo({
			url: "/pages/active/active_signup?id="+data.id
		})
	}
	//签到
	const checkin = (item)=>{
		let errMsg = ""
		if(dayjs().format("YYYY-MM-DD")<item.round.actRoundStartDateTime.split(" ")[0] || 
			dayjs().format("YYYY-MM-DD")>item.round.actRoundEndDateTime.split(" ")[0]){
			errMsg = "活动当天才可签到";
		}
		if(errMsg){
			uni.showToast({
				icon: "error",
				title: errMsg
			})
			return;
		}
		uni.showModal({
			content: '确定要签到吗？',
			cancelText: "取消",
			confirmText: "确定",
			success: function(res0) {
				if(!res0.cancel){
					activityCheckin(item.round.actRoundId).then(res=>{
						// item.roundVo.greyButton = true;
						item.submitCustomers.forEach(customer=>{
							customer.status = "CHECKEDIN";
						})
						uni.showToast({
							icon: "success",
							title: "签到成功"
						})
					})
				}
			}
		})
	}
	//取消报名
	const cancel = item=>{
		uni.showModal({
			title: "取消报名",
			content: "确定要场次《"+item.actRoundInfo+"》的报名吗？",
			cancelText: "再想一想",
			confirmText: "取消报名",
			success: function(res0) {
				if(!res0.cancel){
					activityCancelSubmit(item.actRoundId).then(res=>{
						console.log(res);
						if(res.code==0){
							data.detail.rounds.forEach((round, index)=>{
								if(round.round.actRoundId==item.actRoundId){
									round.submitCustomers = []
								}
							})
							data.detail.rounds = data.detail.rounds.filter(item=>item.submitCustomers.length>0) 
							uni.showModal({
								content: res.data ? res.data : "取消成功",
								showCancel: false,
								success:function(){
									uni.navigateTo({
										url: "/pages/active/my_active_list"
									})
								}
							})
						}else{
							uni.showModal({
								content: res.message,
								showCancel: false
							})
						}
					})
				}
			}
		})
	}
	
	onLoad(options => {
		console.log("options:", options);
		if(options.id&&options.actRoundId){
			data.id = options.id
			data.actRoundId = options.actRoundId
			getActivityDetail(data.id).then(res=>{
				data.detail = res.data.activity
				getActivityRound(data.detail.activityId).then(res=>{
					// data.detail.rounds = res.data.filter(item=>item.submitCustomers.length>0) 
					
					let tmpArr = res.data.filter(item=>item.submitCustomers.length>0) 
					data.detail.rounds =  tmpArr.map(item=>{
						return {round:item.roundVo.actRound, 
								submitCustomers:item.submitCustomers,
								extInfo:{buttonInfo:item.roundVo.buttonInfo, greyButton:item.roundVo.greyButton},
								}
					});
				})
			})
		}else{
			uni.showModal({
				content: '参数错误',
				showCancel: false
			});
		}
		commonStore.curActiveRound = {}
	})
</script>

<style scoped>
	@import '../common/common.css';
	@import "./active.css";
</style>
<style>
	page{
		background: #f7f9fa;
	}
	.batch-wrap{
		padding-bottom: 0 !important;
		box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
	}
	.batch-item{
		
	}
	.batch-name{
		flex:1;
	}
	.batch-status{
	/* 	color:#ff0000; */
		margin: 0 10px 0 0;
	}
	.batch-count{
		color: #808080 !important;
		text-align: left;
	}
	.batch-time{
		color : #808080;
	}
	.batch-btn-wrap{
		text-align: right;
		display: flex;
	}
	.btn-qx {
		width: 100%;
		display: flex;
		position: fixed;
		background: #f7f9fa;
		/* margin-left: -15px; */
		/* #ifdef H5 */
		left: var(--window-left);
		right: var(--window-right);
		/* #endif */
		bottom: 0;
		padding: 20px 0 30px 0;
		font-weight: 400;
		z-index: 1;
	}
	.btn-qx-n{
		width:100%;
		height: 35px;
		line-height: 35px;
		margin-bottom: 10px;
		font-size: 14px;
		border-radius: var(--button-small-radius);
		color: #000;
		background: none;
		border: 1px solid var(--button-solid-color);
		margin: 0 10px 0 15px;
	}
	.btn-qx-s{
		width: 100%;
		margin-bottom: 10px;
		/* padding: 3px 0; */
		font-size: 14px;
		color: #fff;
		background: var(--button-color);
		border-radius: var(--button-small-radius);
		margin: 0 15px 0 10px;
	}
	.tip-box{
		background: var(--button-color);
		width: 160px;
		color: #fff;
		padding-left: 10px;
		margin-bottom: 10px;
		clip-path: polygon(50% 1%, 44% 49%, 51% 100%, 0 100%, 0 100%, 0 1%);
	}
</style>