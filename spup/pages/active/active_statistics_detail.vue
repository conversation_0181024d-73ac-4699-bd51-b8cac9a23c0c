<template>
	
	<view>
		<view style="display: flex;">
			<view style="margin: 10px;font-weight: 600;flex:1">{{data.name}}</view>
			<button class="btn-color" @click="exportExcel()" style="margin: 10px 16px 0 0;">导出</button>
			<button class="btn-white" @click="turnBack()" style="margin: 10px 16px 0 0;">返回</button>
		</view>
		<view class="complete" style="padding-bottom: 100px;">
			<view class="info-content" v-for="(costomer, index) in data.list" :key="costomer.id" >
				<view >场次：{{data.rounds[costomer.actRoundId].actRoundInfo}}</view>
				<view >姓名：{{costomer.username}} {{costomer.type=='CHILD'?"（儿童）":""}}</view>
				<view>手机：{{costomer.phoneString}}</view>
				<!-- <view>{{SPUP_GLOBAL_CONFIG.idType.find(idItem=>idItem.id==costomer.passType)["name"]}}：{{costomer.passString}}</view> -->
				<view>报名时间：{{costomer.createOn}}</view>
			</view>
			<view v-if="!data.list.length" class="empty-box" style="top: 20%;">
				还没有人报名~
				<!-- <image class="empty-img" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl+'/empty/empty.png'"></image> -->
			</view>
		</view>
	</view>
</template>

<script setup>
	import {ref,reactive,onMounted,computed,watch} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import dayjs from "dayjs";
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {getActivityRound, getActivitySubmit} from "@/common/api/server.js";
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import * as XLSX from 'xlsx'
	
	const data = reactive({
		id:0,
		name:"",
		list:[],
		rounds:{}
	})
	
	//返回
	const turnBack = ()=>{
		uni.redirectTo({
			url: "/pages/active/active_statistics"
		})
	}
	
	//新方式，由前台导出excel
	const exportExcel = (item) => {
		let tableData = [
		  ['活动名称', '场次', '姓名', '手机号', "性别", "年龄", "是否儿童", "报名时间"]//导出表头
		] // 表格表头
		data.list.forEach((costomer, index) => {
		  let rowData = []
		  //导出内容的字段
		  rowData = [
			data.name,
			data.rounds[costomer.actRoundId].actRoundInfo,
			costomer.username,
			costomer.phoneString,
			costomer.gender?(costomer.gender==1?"男":"女"):"",
			costomer.age==300?"":costomer.age,
			SPUP_GLOBAL_CONFIG.roundSignUserType.find(typeItem=>typeItem.id==costomer.type)["name"],
			costomer.createOn
		  ]
		  tableData.push(rowData)
		})
		let workSheet = XLSX.utils.aoa_to_sheet(tableData);
		let bookNew = XLSX.utils.book_new();
		XLSX.utils.book_append_sheet(bookNew, workSheet, '报名人员') // 工作簿名称
		let name = "《"+data.name+'》报名人员' + '.xlsx'
		XLSX.writeFile(bookNew, name) // 保存的文件名
	}
	
	
	onLoad(options => { 
		if(options.id){
			data.id = options.id
			data.name = options.name
			getActivityRound(data.id).then(res=>{
				let roundsObj = {}
				res.data.forEach(round=>{
					roundsObj[round.roundVo.actRound.actRoundId] = round.roundVo.actRound
				})
				data.rounds = roundsObj
				getActivitySubmit(data.id).then(res=>{
					data.list = res.data
				})
			})
			
		}else{
			uni.showModal({
				content: '参数错误',
				showCancel: false
			});
		}
	})
</script>

<style>
	page{
		background: #F7F9FA;
	}
	.title{
		font-size: 40rpx;
		text-align: center;
	}
</style>
<style scoped>
	@import '../common/common.css';
	@import "./active.css";
</style>