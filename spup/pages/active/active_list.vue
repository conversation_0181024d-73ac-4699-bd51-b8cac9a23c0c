<template>
	<view>
		<view class="complete" style="padding-bottom: 128px;">
			<template v-for="(item, index) in data.list" :key="item.id" :itemData='item'>
				<view class="fol-row active-list">
					<view class="active-img">
						<view class="float-list">
							<image mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + item.picUrl"></image> 
							<!-- <i class="tip">{{activeStatusLabel(item)}}</i> -->
						</view>
					</view>
					<view class="fol-col text-box" style="position: relative;">
						<view class="title-head">{{item.activityName}}</view>
						<view class="label">{{SPUP_GLOBAL_CONFIG.activeTypeLabel[item.type]}}</view>
						<!-- <view class="size-span">报名截止 ：{{dayjs(item.startTime).format("YYYY/MM/DD")}} - {{dayjs(item.endTime).format("YYYY/MM/DD")}}</view> -->
						<!-- <view class="btn-boxs fol-row" style="position: absolute;bottom: 0;">
							<view  class="fol-col"></view>
							<view class="btn-right-site">
								<button class="btn-s " @click="showDetail(item)">参与报名</button>
							</view>
						</view> -->
						<view class="fol-row" >
							<view class="fol-col"></view>
							<button class="btn-white btn-join" @click="showDetail(item)">参与报名</button>
						</view>
						
						
						<!-- <view style="display: flex;">
							<view >&nbsp;</view>
							<view style="flex: 1;">
								<button class="btn" @click="showDetail(item)" style="color: #6d836c;">参与报名</button>
								</view>
						</view> -->
					</view>
				</view>
			</template>
			<view v-if="!data.list.length" class="empty-box" style="top: 20%;">
				<image class="empty-img" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl+'/empty/empty.png'"></image>
				<text class="empty-txt" style="">{{data.listTips}}</text>
			</view>
		</view>
	</view>
	<view class="my-active-btn-wrap" @click="myActiveBtnClick">
		我的活动
	</view>
</template>

<script setup>
	import {ref,reactive,onMounted,computed,watch} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import dayjs from "dayjs";
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {getActivityList, getMyActivityList} from "@/common/api/server.js";
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	
	const data = reactive({
		listTips:"数据加载中……",
		list:[]
	})
	
	
	//带参数的计算属性
	// const activeStatusLabel = computed(() => (item) => {
	// 	let now = dayjs().valueOf()
	// 	if(now<dayjs(item.startTime).valueOf()){
	// 		return "未开始"
	// 	}else if(now>dayjs(item.endTime).valueOf()){
	// 		return "已结束"
	// 	}else{
	// 		return "进行中"
	// 	}
	// })
	const showDetail = (item) => {
		uni.navigateTo({
			url: "/pages/active/active_details?id="+item.activityId
		})
	}
	
	const myActiveBtnClick = ()=>{
		uni.navigateTo({
			url: "/pages/active/my_active_list"
		})
	}

	onLoad(options => {
		getActivityList().then(res=>{
			data.list = res.data.filter(item=>item.status=="RUNNING");
			if(data.list.length==0){
				data.listTips = "暂无活动~"
			}
		})
	})
</script>

<style>
	page{
		background:#f7f9fa;
		padding-bottom: 76px;
	}
	.my-active-btn-wrap{
		position: fixed;
		bottom: 180rpx;
		right: 0;
		z-index: 100;
		font-size: 14px;
		background:#fff;
		color: var(--button-color);
		box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
		height:76rpx;
		padding: 5px;
		width: 160rpx;
		line-height: 76rpx;
		text-align: center;
		border-radius: 25px 0 0 25px;
		font-weight: 600;
	}
	
</style>
<style scoped>
	@import '../common/common.css';
	@import "./active.css";
</style>