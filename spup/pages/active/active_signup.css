	.txt-color {
		color: #26111a;
	}
	.box{
		padding: 0 15px;
	}
	.size-span {
		font-size: 12px;
		color: #999999;
	}
	.bt-color{
		color: var(--bt-color);
	}
	.size-span2 {
		font-size: 14px;
		color: #999999;
	}
	.size-txt{
		overflow-wrap: break-word;
		white-space: nowrap;
	}
	uni-button:after {
		content:none;
	}
	
	.add-title-hr{
		text-align: center;
		font-size: 17px;
		font-weight: 500;
		color: #000;
		padding: 20px 0;
	}
	.active-list{
		border-bottom: 0.8px solid #000;
		margin: 15px 15px 0 15px;
		padding-top: 0;
	}
	.input-box{
		font-size: 14px;
		text-align: left;
	}
	/* 表单 */
	.fol-row_box {
		font-size: 13px;
		border-bottom: 0.8px solid #000;
		height: 52px;
		line-height: 52px;
		text-align: left;
		align-items: center;
	}

	.uni-input-wrapper {
		/* #ifndef APP-NVUE */
		display: flex;
		flex: 1;
		/* #endif */
		flex-direction: column;
		flex-wrap: nowrap;
		overflow: hidden;
		text-overflow:ellipsis;
		white-space: nowrap;
		text-align: right;
	}
	.uni-column{
		text-align: left;
	}
	.uni-form-item__title {
		color: #000;
		width: 80px;
	}

	.uni-input {
		flex: 1;
		display: block;
		font-size: 13px;
		line-height: 1.4em;
		height: auto ;
		min-height: 1.4em;
		overflow: hidden;
	}
	.complete {
		padding: 10px 15px;
	}
	/* 按钮 */
	.btn-box {
		width: 100%;
		border-top: 1px solid #000;
		display: flex;
		position: fixed;
		padding: 15px 0;
		/* #ifdef H5 */
		left: var(--window-left);
		right: var(--window-right);
		/* #endif */
		bottom: 0;
		background: #F7F9FA ;
		font-weight: 400;
	}
	
	.btn-right-site {
		text-align: right;
	}
	
	.btn-s {
		width: 94px;
		margin-bottom: 10px;
		margin-right: 15px;
		/* padding: 3px 0; */
		font-size: 14px;
		color: #fff;
		background: var(--button-color);
		border-radius: var(--button-small-radius);
	}
	.contacts-box {
		
	}
	.contacts-box-baseinfo{
		font-size: 14px;
		padding:10px 15px ;
		border-bottom: 0.8px solid #000;
	}
	.contacts-box-title-wrap{
		display: flex;
		margin: 10px 0 0 0;
	}
	.contacts-box-title{
		color: #000;
		font-weight: 600;
		flex:1;
	}
	.contacts-box-delete{
		color: #ff0000;
	}