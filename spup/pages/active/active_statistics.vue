<template>
	<view>
		<view class="complete">
			<template v-for="(item, index) in data.list" :key="item.id" :itemData='item'>
				<view class="info-content"  @click="showDetail(item)">
					<view class="title-head">{{item.activityName}}</view>
					<!-- <view class="label">{{SPUP_GLOBAL_CONFIG.activeTypeLabel[item.type]}}</view> -->
					<view class="fol-row" >
						<view class="type-label">{{SPUP_GLOBAL_CONFIG.activeTypeLabel[item.type]}}</view>
						<!-- <button class="btn-white" @click="exportExcel(item)">导出</button> -->
						<button class="btn-white-big">查看</button>
					</view>
				</view>
			</template>
			<view v-if="!data.list.length" class="empty-box" style="top: 20%;">
				<image class="empty-img" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl+'/empty/empty.png'"></image>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {ref,reactive,onMounted,computed,watch} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import dayjs from "dayjs";
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {getActivityList} from "@/common/api/server.js";
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import * as XLSX from 'xlsx'
	
	const data = reactive({
		list:[],
	})
	
	//显示详情
	const showDetail = (item) => {
		const userAgent = navigator.userAgent.toLowerCase();
		if (/ipad|iphone|midp|rv:*******|ucweb|android|windows ce|windows mobile/.test(userAgent)) {
			uni.navigateTo({
				url: "/pages/active/active_statistics_detail?id="+item.activityId+"&name="+item.activityName
			})
		} else {
			uni.navigateTo({
				url: "/pages/active/active_statistics_detail_pc?id="+item.activityId+"&name="+item.activityName
			})
		}
	}
	
	//老方式，由后台导出excle
	const exportExcel0 = (item) => {
		let url = SPUP_GLOBAL_CONFIG.baseUrl + "/activityUserExport/exportByActivity/"+item.id + "?t="+dayjs().valueOf()
		console.log(url)
		window.location.href = url
	}
	
	onLoad(options => { 
		getActivityList().then(res=>{
			data.list = res.data
		})
	})
</script>

<style>
	page{
		background: #F7F9FA;
	}
</style>
<style scoped>
	@import '../common/common.css';
	@import "./active.css";
	.type-label{
		font-size: 12px;
		color: #815462;
		width: fit-content;
		margin: 5px 0;
		flex:1;
	}
</style>