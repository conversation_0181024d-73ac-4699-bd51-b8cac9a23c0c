<template>
	<view>
		<view class="complete" style="padding-bottom: 100px;">
			<template v-for="(item, index) in data.list" :key="item.id" :itemData='item'>
				<view class="info-content">
					<view class="item-head">
						<view class="item-name">{{item.activity.activityName}}</view>
						<!-- <view class="checkin-status" v-if="item.status=='CHECKEDIN'" >已签到</view> -->
					</view>
					<view class="item-text">场次：{{item.roundVo.actRound.actRoundInfo}}</view>
					<view class="item-text">时间：{{item.roundVo.actRound.actRoundStartDateTime.substr(0,16)}} 至 {{item.roundVo.actRound.actRoundEndDateTime.substr(0,16)}}</view>
					<view class="fol-row" style="margin-top: 10px;">
						<view class="fol-col"></view>
						<button class="btn-white-big" @click="showDetail(item)">查看</button>
						<button v-if="item.roundVo.greyButton" class="btn-color-big btn-disable" disabled="true">{{item.roundVo.buttonInfo}}</button>
						<button v-else class="btn-color-big"  @click="checkin(item)">{{item.roundVo.buttonInfo}}</button>
					</view>
				</view>
			</template>
			<view v-if="!data.list.length" class="empty-box" style="top: 20%;">
				<image class="empty-img" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl+'/empty/empty.png'"></image>
				<text class="empty-txt" style="">暂无报名～</text>
			</view>
		</view>
	</view>
	<ActiveSignupFeedback v-model:visible="activeSignupFeedbackVisible" v-model:itemData="data.curItem"></ActiveSignupFeedback>
</template>

<script setup>
	import {ref,reactive,onMounted,computed,watch} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import dayjs from "dayjs";
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {getActivityCheckinList, activityCheckin} from "@/common/api/server.js";
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import ActiveSignupFeedback from './active_signup_feedback.vue'
	const activeSignupFeedbackVisible = ref(false);
	
	const data = reactive({
		list:[],
		curItem:{}
	})
	
	//带参数的计算属性
	const activeTimeLabel = computed(() => (rounds) => {
		if(rounds.leaves.find(item=>item.meetingId==tabItem.id)){
			return tabItem.selected ? "tab-on-0" :"tab-0"
		}
		return tabItem.selected ? "tab-on-1" :"tab-1"
	})
	const showDetail = (item) => {
		uni.navigateTo({
			url: "/pages/active/my_active_details?id="+item.roundVo.actRound.activityId+"&actRoundId="+item.roundVo.actRound.actRoundId
		})
		// data.curItem = item
		// activeSignupFeedbackVisible.value = true;
	}
	//签到
	const checkin = (item)=>{
		uni.showModal({
			content: '确定要签到吗？',
			cancelText: "取消",
			confirmText: "确定",
			success: function(res0) {
				if(!res0.cancel){
					activityCheckin(item.roundVo.actRound.actRoundId).then(res=>{
						item.roundVo.greyButton = true;
						item.roundVo.buttonInfo = "已签";
						uni.showToast({
							icon: "success",
							title: "签到成功"
						})
					})
				}
			}
		})
	}
	
	onLoad(options => {
		getActivityCheckinList().then(res=>{
			data.list = res.data;
		})
	})
</script>

<style>
	page{
		background: #F7F9FA;
	}
</style>
<style scoped>
	@import '../common/common.css';
	@import "./active.css";
	
	.item-head{
		border-bottom: 0.8px solid #f0f0f0;
		padding-bottom: 10px;
		font-size: 14px;
		display: flex;
	}
	
	.item-name{
		flex: 1;
	}
	.checkin-status{
		color:#ff0000;
		margin: 0 10px 0 0;
	}
	
	.item-text{
		font-size: 13px;
	}
</style>