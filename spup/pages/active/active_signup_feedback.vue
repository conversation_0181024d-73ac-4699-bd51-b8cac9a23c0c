<template>
	<view>
		<uni-popup ref="popup" :mask-click="false">
			<view class="popup-content">
				<view class="title-name">您已成功报名</view>
				<view class="msg-wrap">
					<view class="msg-line">
						<view class="msg-title">名称：</view>
						<view class="msg-txt">{{activity.activityName}}</view>
					</view>
					<view class="msg-line">
						<view class="msg-title">场次：</view>
						<view v-for="(round) in rounds" style="flex:1">
							<view >{{round.round.actRoundInfo}}</view>
						</view>
					</view>
					<!-- <view class="msg-line">
						<view class="msg-title">地点：</view>
						<view class="msg-txt">{{activity.address}}</view>
					</view> -->
					<view class="msg-tips">
						<text>{{rounds&&rounds.length ? rounds[0].round.otherInfo.tips : ""}}</text>
					</view>
				</view>
				<view class="btn-wrap">
					<button class="btn-ok" @click="okClick">确定</button>
				</view>
			</view>
			
		</uni-popup>
	</view>
</template>

<script setup>
	import {ref,reactive,watch,computed,onMounted} from 'vue'
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import {useCommonStore} from '@/stores/common.js';
	const commonStore = useCommonStore()
	const props = defineProps(['visible', 'itemData']);
	const emit = defineEmits(['update:visible']);
	
	const activity = computed(() => {
		return props.itemData && props.itemData.activity ? props.itemData.activity : {};
	});
	const rounds = computed(() => {
		return props.itemData && props.itemData.rounds ? props.itemData.rounds : {};
	});
	//弹窗
	const popup = ref();
	//通过computed实现父子组件的属性双向绑定
	const visible = computed({
		// 子组件v-model绑定 计算属性, 一旦发生变化, 就会给父组件传递值
		get: () => props.visible,
		set: (nv) => {
			emit('update:visible', nv)
		}
	})
	const change = (e) => {
		visible.value = e.show;
	};
	watch(visible, (value) => {
		if (value) {
			popup.value.open("center");
		} else {
			popup.value.close();
		}
	});
	

	const okClick = ()=>{
		visible.value = false;
		if(props.itemData&&props.itemData.closeUrl){
			uni.navigateTo({
				url: props.itemData.closeUrl
			})
		}
	}
	onMounted(()=>{
		
	})
</script>

<style lang="scss" scoped>
	.popup-content {
		font-size: 15px;
		// font-weight: 500;
		align-items: left;
		justify-content: left;
		// width: 100%;
		// height: 100%;
		padding: 27px 10px;
		background-color: #fff;
		border-radius: 14px;
		margin: 0 35px;
		.title-name{
			font-size: 17px;
			font-weight: 600;
			text-align: center;
		}
		.msg-wrap{
			padding: 2px 15px;
			border-radius: 5px;
			// border: 1px solid #e5e7ee;
			margin-top: 20px;
			.msg-line{
				display: flex;
				
				.msg-title{
					
					color:#808080;
				}
				.msg-txt{
					flex: 1;
					text-align: left;
				}
			}
			.msg-tips{
				margin-top: 20rpx;
			}
		}
		
		.btn-wrap{
			margin-top: 20px;
			.btn-ok{
				margin-bottom: 10px;
				width: 70%;
				font-size: 14px;
				color: #fff;
				background: var(--button-color);
				border-radius: 6px;
			}
		}
	}
	
</style>