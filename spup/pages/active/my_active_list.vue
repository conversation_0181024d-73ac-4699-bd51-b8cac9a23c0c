<template>
	<view>
		<view class="">
			<template v-for="(item, index) in data.list" :key="item.id" :itemData='item'>
				<view class="card-box">
					<view class="fol-row card-line size-mini">
						<view class="item-head">{{item.activityName}}</view>
					</view>
					<view class="txt-box size-normal">
						<view class="item-text">场次：{{item.roundVo.actRound.actRoundInfo}}</view>
						<view class="item-text">时间：{{item.roundVo.actRound.actRoundStartDateTime.substr(0,16)}} 至 {{item.roundVo.actRound.actRoundEndDateTime.substr(0,16)}}</view>
					</view>
					<view class="common-btn fol-row">
						<view class="fol-col btn-site btn-hl"  @click="cancelSignup(item.roundVo.actRound)" v-if="item.submitCustomers[0].status=='SUBMITTED'">
							<view class="btn-img"><image mode="widthFix" src="../../static/btn/order-time.png"></image></view>
							<text class="size-normal mini-btn">取消报名</text>	
						</view>
						<view class="fol-col btn-site"  @click="showDetail(item.roundVo.actRound)">
							<view class="btn-img"><image mode="widthFix" src="../../static/btn/order-go.png"></image></view>
							<text class="size-normal mini-btn">查看详情</text>	
						</view>
						
					</view>
				</view>
			</template>
			<view v-if="!data.list.length" class="empty-box" style="top: 20%;">
				<image class="empty-img" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl+'/empty/empty.png'"></image>
				<text class="empty-txt" style="">{{data.listTips}}</text>
			</view>
		</view>
	</view>
	<ActiveSignupFeedback v-model:visible="activeSignupFeedbackVisible" v-model:itemData="data.curItem"></ActiveSignupFeedback>
</template>

<script setup>
	import {ref,reactive,onMounted,computed,watch} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import dayjs from "dayjs";
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {getMyActivityList, activityCancelSubmit} from "@/common/api/server.js";
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import ActiveSignupFeedback from './active_signup_feedback.vue'
	const activeSignupFeedbackVisible = ref(false);
	
	const data = reactive({
		searchKey: '',
		listTips:"数据加载中……",
		list:[],
		curItem:{}
	})
	
	
	
	//带参数的计算属性
	const activeTimeLabel = computed(() => (rounds) => {
		if(rounds.leaves.find(item=>item.meetingId==tabItem.id)){
			return tabItem.selected ? "tab-on-0" :"tab-0"
		}
		return tabItem.selected ? "tab-on-1" :"tab-1"
	})
	
	//取消报名
	const cancelSignup = item=>{
		uni.showModal({
			title: "取消报名",
			content: "确定要场次《"+item.actRoundInfo+"》的报名吗？",
			cancelText: "再想一想",
			confirmText: "取消报名",
			success: function(res0) {
				if(!res0.cancel){
					activityCancelSubmit(item.actRoundId).then(res=>{
						console.log(res);
						if(res.code==0){
							// data.detail.rounds.forEach((round, index)=>{
							// 	if(round.round.actRoundId==item.actRoundId){
							// 		round.submitCustomers = []
							// 	}
							// })
							// data.detail.rounds = data.detail.rounds.filter(item=>item.submitCustomers.length>0) 
							
							uni.showModal({
								content: res.data ? res.data : "取消成功",
								showCancel: false,
								success:function(){
									uni.navigateTo({
										url: "/pages/active/my_active_list"
									})
								}
							})
							for(let i=data.list.length-1; i>=0; i--){
								if(data.list[i].roundVo.actRound==item){
									data.list.splice(data.list.indexOf(i), 1);
								}
							}
						}else{
							uni.showModal({
								content: res.message,
								showCancel: false
							})
						}
					})
				}
			}
		})
	}
	const showDetail = (item) => {
		uni.navigateTo({
			url: "/pages/active/my_active_details?id="+item.activityId+"&actRoundId="+item.actRoundId
		})
		// data.curItem = item
		// activeSignupFeedbackVisible.value = true;
	}

	onLoad(options => {
		getMyActivityList().then(res=>{
			data.list = res.data
			if(data.list.length==0){
				data.listTips = "暂无报名~"
			}
		})
	})
</script>

<style>
	page{
		background: #F7F9FA;
	}
	.title{
		font-size: 40rpx;
		text-align: center;
	}
	.item-head{
	
		font-size: 14px;
	}
	
	.item-text{
		font-size: 13px;
	}
</style>
<style scoped>
	@import '../common/common.css';
	@import "./active.css";
</style>