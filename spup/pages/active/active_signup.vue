<template>
	<view style="padding-bottom: 60px;">
		<view class="fol-row active-list">
			<view class="active-img2">
				 <view class="float-list">
				   <image mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + data.detail.picUrl"></image> 
				 </view>
			</view>
			<view class="fol-col text-box" style="position: relative;">
				<view class="title-head2">{{data.detail.activityName}}</view>
				<!-- <view class="label">{{SPUP_GLOBAL_CONFIG.activeTypeLabel[data.detail.type]}}</view> -->
				<view class="size-span">{{dayjs(data.detail.startDateTime).format("YYYY-MM-DD")}} 至 {{dayjs(data.detail.endDateTime).format("YYYY-MM-DD")}}</view>
			</view>
		</view>
		<view class="contacts-box-baseinfo">
			<!-- <view class="uni-form-item uni-column fol-row fol-row_box">
				<text class="uni-form-item__title"><text class="bt-color"></text>活动名称</text>
				<view class="uni-input-wrapper">
					{{data.detail.activityName}}
				</view>
			</view> -->
			<view class="fol-row">
				<view>参与场次</view>
				<view class="fol-col txt-right" >
					<text>{{entryTime&&entryTime.actRoundInfo? entryTime.actRoundInfo:'选择活动场次'}}</text>
				</view>
				<!-- <view style="text-align: right;padding-right: 10px;">
					<uni-icons type="right" size="14"></uni-icons>
				</view> -->
			</view>
		</view>
		<view v-for="(costomer,index) in data.costomers" :key="costomer.id" style="padding:0 15px 15px 15px;">
			<view class="contacts-box-title-wrap">
				<view class="contacts-box-title">{{costomer.title}}</view>
				<view v-if="index>1&&commonStore.curActiveRound.type=='CHILD'" @click="deleteCostomer(index)" class="contacts-box-delete" ><uni-icons type="minus" size="16" color="#ff0000"></uni-icons>删除该儿童</view>
				<view v-if="index>0&&commonStore.curActiveRound.type=='NOLIMIT'" @click="deleteCostomer(index)" class="contacts-box-delete" ><uni-icons type="minus" size="16" color="#ff0000"></uni-icons>删除该同行人</view>
			</view>
			<view class="contacts-box">
				<view class="uni-form-item uni-column fol-row fol-row_box">
					<text class="uni-form-item__title"><text class="bt-color">*</text>姓名</text>
					<view class="uni-input-wrapper">
						<input class="uni-input" type="text" v-model="costomer.name" placeholder="请输入姓名" />
					</view>
				</view>
				<view class="uni-form-item uni-column fol-row fol-row_box">
					<text class="uni-form-item__title"><text class="bt-color">*</text>证件类型</text>
					<view class="uni-input-wrapper">
						<picker @change="(e)=>idTypePickerChange(e, index)" :value="costomer.idType" :range="SPUP_GLOBAL_CONFIG.idType.map(item=>item.name)">
							<view class="uni-input">{{SPUP_GLOBAL_CONFIG.idType.find(item=>item.id==costomer.idType)["name"]}} <uni-icons type="right" size="14"></uni-icons></view>
						</picker>
					</view>
				</view>
				<view class="uni-form-item uni-column fol-row fol-row_box">
					<text class="uni-form-item__title"><text class="bt-color">*</text>证件号码</text>
					<view class="uni-input-wrapper">
						<input class="uni-input" type="text" v-model="costomer.idCard" placeholder="请输入证件号码" />	
					</view>
				</view>
				<view class="uni-form-item uni-column fol-row fol-row_box" v-if="index!=0 || commonStore.curActiveRound.type!='CHILD'">
					<text class="uni-form-item__title"><text class="bt-color">*</text>性别</text>
					<view class="uni-input-wrapper">
						<view class="">
							<radio-group @change="(e) => genderChaged(e, index)">
								<label class="radio" style="margin-right: 30rpx;">
									<radio value="1" :checked="costomer.gender==1" color="#815462" style="transform:scale(0.7)"/>男
								</label>
								<label class="radio">
									<radio value="2" :checked="costomer.gender==2" color="#815462" style="transform:scale(0.7)"/>女
								</label>
							</radio-group>
						</view>
					</view>
				</view>
				<view class="uni-form-item uni-column fol-row fol-row_box" v-if="index!=0 || commonStore.curActiveRound.type!='CHILD'">
					<text class="uni-form-item__title"><text class="bt-color">*</text>年龄</text>
					<view class="uni-input-wrapper">
						<input class="uni-input" type="number" v-model="costomer.age" maxlength="2" placeholder="请输入年龄" />
					</view>
				</view>
				<view class="uni-form-item uni-column fol-row fol-row_box" v-if="index==0 || commonStore.curActiveRound.type!='CHILD'">
					<text class="uni-form-item__title"><text class="bt-color">*</text>联系方式</text>
					<view class="uni-input-wrapper">
						<input class="uni-input" type="number" @confirm="phoneConfirm(index)" v-model="costomer.phone" maxlength="11" placeholder="请输入手机号码" />
					</view>
				</view>
				
			</view>
		</view>
		<view v-if="commonStore.curActiveRound.type=='CHILD'&&data.costomers.length<3" @click="addChild" style="text-align: right; margin: 10px 20px;">
			<uni-icons type="plus" size="16" ></uni-icons>添加儿童
		</view>
		<view v-if="commonStore.curActiveRound.type=='NOLIMIT'&&data.costomers.length<3" @click="addChild" style="text-align: right; margin: 10px 20px;">
			<uni-icons type="plus" size="16" ></uni-icons>添加同行人
		</view>
	</view>
	
	
	<!-- 按钮 -->
	<view class="btn-box">
		<button class="btn-s" @click="okClick">提交</button>
	</view>
	<ActiveSignupFeedback v-model:visible="activeSignupFeedbackVisible" v-model:itemData="data.curItem"></ActiveSignupFeedback>
</template>

<script setup>
	import {ref,reactive,onMounted,computed,watch} from 'vue'
	import {onShow,onHide} from '@dcloudio/uni-app';
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import {getActivityDetail, activityEntry} from "@/common/api/server.js";
	import ActiveSignupFeedback from './active_signup_feedback.vue'
	const activeSignupFeedbackVisible = ref(false);
	import dayjs from 'dayjs'
	
	const data = reactive({
		id:0,
		detail:{
			
		},
		curItem:{},
		costomerCount:1,
		costomers:[{
			id:1,
			title:"报名人员信息",
			name:"",
			idType:"IDCARD",
			idCard:"",
			phone: "",
			age:"",
			gender: 0,
		}]
	})
	const idTypePickerChange = (e, index)=>{
		console.log('picker发送选择改变，携带值为', e.detail.value, index)
		data.costomers[index].idType = SPUP_GLOBAL_CONFIG.idType[e.detail.value].id
	}
	const phoneConfirm = index=>{
		if(index==0){
			data.costomers.forEach(item=>{
				if(!item.phone){
					item.phone = data.costomers[0].phone
				}
			})
		}
	}
	const deleteCostomer = index=>{
		uni.showModal({
			content: '确定要删除该'+(commonStore.curActiveRound.type=="CHILD"?"儿童":"同行人")+'吗？',
			cancelText: "取消",
			confirmText: "确定",
			success: function(res) {
				if(!res.cancel){
					data.costomers.splice(index,1)
				}
			}
		})
	}
	const addChild = ()=>{
		data.costomerCount++
		data.costomers.push({
			id:data.costomerCount,
			title:(commonStore.curActiveRound.type=="CHILD"?"携带儿童":"同行人") + "一二三四五六七八九十"[data.costomers.length-1] + ":",
			name:"",
			idType:"IDCARD",
			idCard:"",
			phone: data.costomers[0].phone,
			age:"",
			gender: 0,
		})
	}
	//参加时间
	const entryTime = computed(() => {
		return commonStore.curActiveRound
	});
	
	const genderChaged = (e, index)=>{
		console.log("genderChaged:", e);
		data.costomers[index].gender = e.detail.value
	}

	// watch(()=>costomer.idCard, (newValue, oldValue) => {
	// 	if(costomer.idCard.length>=10){
	// 		costomer.age = parseInt(dayjs().format('YYYY-MM-DD HH:mm:ss')) - parseInt(costomer.idCard.substr(6, 4))
	// 	}
	// })
	
	const okClick = ()=>{
		if(!entryTime.value||!entryTime.value.actRoundId){
			uni.showToast({icon: "none",title:  '请先选择活动场次'})
			return;
		}
		let msg = "";
		
		for(let i=data.costomers.length-1; i>=0; i--){
			let costomer = data.costomers[i];
			if(i>0){
				costomer.phone = data.costomers[0].phone
			}
			let title = commonStore.curActiveRound.type=="CHILD" ? costomer.title : ""
			if(!/^[1][0-9]{10}$/.test(costomer.phone)){
				msg =  title + '请输入正确的手机号码'
			}
			if((commonStore.curActiveRound.type!="CHILD"||i!=0) && !costomer.age){
				msg = title +'请输入年龄'
			}
			if((commonStore.curActiveRound.type!="CHILD"||i!=0) && !costomer.gender){
				msg = title +'请选择性别'
			}
			if(costomer.idType=="IDCARD" && !/(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}$)/.test(costomer.idCard)){
				msg =  title + '请输入正确的身份证号码'
			}
			if(!costomer.idCard){
				msg = title +'请输入证件号码'
			}
			if(!costomer.name){
				msg = title +'请输入姓名'
			}
		}
		if(msg){
			uni.showToast({icon: "none", title: msg})
			return;
		}
		
		uni.showModal({
			content: '确定要提交吗？',
			cancelText: "取消",
			confirmText: "确定",
			success: function(res) {
				if(!res.cancel){
					_doSubmit()
				}
			}
		})
	
	}
	const _doSubmit = ()=>{
		let params = data.costomers.map(costomer=>{
			return {
				username : costomer.name,
				passType :  costomer.idType,
				passString :  costomer.idCard,
				phoneString : costomer.phone,
				age : costomer.age ? parseInt(costomer.age) : -1,
				gender : parseInt(costomer.gender),
				actRoundId : entryTime.value.actRoundId,
			}
		})
		activityEntry(entryTime.value.actRoundId, params).then((res)=>{
			if(res.code==0){
				data.curItem = {
					activity:data.detail,
					rounds:[{"round":entryTime.value}],
					closeUrl:"/pages/active/my_active_list"
				}
				activeSignupFeedbackVisible.value = true;
			}
		})
	}
	
	const selectEntryTimeClick = ()=>{
		uni.navigateTo({
			url: "/pages/active/active_signup_round?activityId="+data.detail.activityId
		})
	}
	
	onLoad(options => {
		console.log("options:", options);
		if(commonStore.curActiveRound.type=="CHILD"){
			data.costomers[0].title = "监护人信息："
			addChild()
		}
		
		if(options.id){
			data.id = options.id
			getActivityDetail(data.id).then(res=>{
				data.detail = res.data.activity
			})
		}else{
			uni.showModal({
				content: '参数错误',
				showCancel: false
			});
		}
	})
</script>

<style scoped>
	@import 'active.css';
	@import 'active_signup.css';
	@import "../../pages/common/common.css";
	
</style>
<style>
	page{
		background-color: #F7F9FA;
		padding-bottom: 50px;
	}
</style>
