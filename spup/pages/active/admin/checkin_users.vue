<template>
  <view class="container">
	 <!-- 搜索框 -->
	 <view class="search-bar">
	   <image src="../../../static/search.png" mode="widthFix" class="search-icon"></image>
	   <input v-model="data.searchKey" type="text" placeholder="输入姓名或手机号进行搜索" @input="handleSearch" />
	 </view>
	 	<!-- 筛选状态 -->
		<view style="display: flex;">
			<view style="flex: 1;padding-top: 9px;">签到人数(只统计大人)：{{signupCount}}</view>
			<view class="filter-bar">
			  <picker mode="selector" :range="data.roundSignupStatus" range-key="name" @change="onPickerChange">
					 <view class="picker">
					   签到状态：<text>{{ data.roundSignupStatus.find(item=>item.id==data.curStatus).name}}
					   <uni-icons type="down" size="14" style="padding-left: 3px;color: #333;"></uni-icons>
					   </text>
					 </view>
				   </picker>
			</view>
		</view>
	  
	 
    <!-- 列表显示 -->
	<view class="list-container" v-if="filterList.length">
		<view class="list-item list-top-item gird-container" >
			<view class="gird-item">姓名</view>
			<view class="gird-item">手机号</view>
			<view class="gird-item2">是否儿童 </view>
			<view class="gird-item2">状态 </view>
		</view>
		<view class="list-item gird-container"  v-for="(item, index) in filterList"  :key="index"  >
			<view class="gird-item">{{ item.username }}</view>
			<view class="gird-item" @click="callClick(item.phoneString)">{{ maskPhoneNumber(item.phoneString) }}</view>
			<view class="gird-item2">{{ item.type=='CHILD'?"儿童":"" }}</view>
			 <view :class="'roundSignupStatus roundSignupStatus-'+item.status">
				{{ "CHECKEDIN"==item.status ? "已签到" : "未签到"}}
			</view>
		</view>
	</view>
	<view v-else class="empty-box" style="top: 25%;">
		<image class="empty-img" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl+'/empty/empty.png'"></image>
		<text class="empty-txt" style="">{{data.listTips}}</text>
	</view>
  </view>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import {onLoad, onUnload} from '@dcloudio/uni-app';
import {tempBatchGetRole, getActivityDetail, getActivitySubmitByRoundId} from "@/common/api/server.js";
import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'	
import {maskPhoneNumber, base64Decode} from '@/common/myUtil.js'
import {useCommonStore} from '@/stores/common.js'
const commonStore = useCommonStore();

const data = reactive({
	roundId:0,
	searchKey: '',
	roundSignupStatus:[
		{
			name: '全部',
			id: ""
		},{
			name: '未签到',
			id: "SUBMITTED"
		},{
			name: '已签到',
			id: "CHECKEDIN"
		}
	],
	curStatus:"",
	listTips:"数据加载中……",
	list:[  
		// { username: '张三', phone: '13812345678', age: 25, status: '已签到', statusColor: '#4c9e44' },
		// { username: '李四', phone: '13987654321', age: 30, status: '未签到', statusColor: '#999' },
	]
})

// 根据查询条件过滤列表
const onPickerChange = (event) => {
	const index = event.detail.value; // 获取选中的索引
	const selectedItem = data.roundSignupStatus[index]; // 获取选中的对象
	data.curStatus = selectedItem.id; // 更新选中的 ID
}
//过滤之后的数据
const filterList = computed(()=>{
	return data.list.filter(item=>(item.status==data.curStatus||!data.curStatus)&&(item.username.indexOf(data.searchKey.trim())!=-1||item.phoneString.indexOf(data.searchKey.trim())!=-1));
})
//签到人数统计
const signupCount = computed(()=>{
	let list = data.list.filter(item=>item.type=="ADULT");
	return list.filter(item=>item.status=='CHECKEDIN').length + "/" + list.length;
})
//打电话
import tel from "@/common/tel.js"
const { telClick } = tel();
const callClick = (telnum)=>{
	telClick(telnum);
}

onLoad(options => {
	let _init = ()=>{
		let params = null;
		try{
			params = JSON.parse(base64Decode(options.p));
		}catch(err) {
			console.log("err:", err);
		}
		if(params && params.id){
			data.roundId = params.id
			getActivitySubmitByRoundId(data.roundId).then(res=>{
				data.listTips = '暂无数据';
				data.list = res.data.filter(item=>item.status!='CANCELLED');
				uni.setNavigationBarTitle({
				　　title: params.name
				})
			})
		}else{
			uni.showModal({
				content: '参数错误',
				showCancel: false
			});
		}
	}
	//判断权限
	tempBatchGetRole().then(res=>{
		commonStore.tmpBatch.menuCode = res.data.menuCode.split(",");
		if(commonStore.tmpBatch.menuCode.indexOf("62")>=0){
			_init();
		}else{
			uni.reLaunch({
				url: "/pages/common/forbidden"
			})
		}
	}).catch(err=>{
		console.log("err:", err);
		data.msg = err.message ? err.message : "网络异常，请稍候再试"
		uni.showModal({
			title:"请求失败",
			content: "网络异常，请稍候再试",
			confirmText: "确定",
			showCancel: false,
		});
	});
})

</script>
<style scoped>
@import '../../common/common.css';
@import "../active.css";
page{
	background: #fff;
}
.container {
  padding: 15px;
}

/* 搜索开始 */
.search-bar {
  margin: 5px 0 15px 0;
  display: flex;
  justify-content: center;
  text-align: center;
  align-items: center;
}
.search-bar input {
  width: 100%;
  padding:7px 10px;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 25px;
  padding-left: 45px;
  text-align:left;
}
.search-icon {
  position: absolute;
  left:36px;
  cursor: pointer;
  width: 16px;
}
/* 搜索结束 */

.list-container {
  margin-top: 20px;
}
.gird-container{
	display: grid;
	  grid-template-columns: repeat(4, 1fr);  /* 创建三列，每列宽度均匀分配 */
	  grid-template-rows: auto;  /* 行的高度自适应 */
	  gap: 10px;  /* 元素之间的间隙 */
}
.gird-item{
	text-align: left;
}
.gird-item2{
	text-align: center;
}
.list-item {
  padding: 10px;
  border-bottom: 0.8px solid #d9d9d9;
}
.list-top-item{
	border-bottom:none;
	background: #F7F9FA;
}
.filter-bar picker{
	border: 1px solid #d9d9d9;
	background: #fff;
	text-align: right;
	padding: 5px 8px;
	border-radius: 5px;
}
.roundSignupStatus {
  text-align: center;
  color: #999;
}
.roundSignupStatus-SUBMITTED {
  color: #ff0000;
  font-weight: 600;
}
.roundSignupStatus-CHECKEDIN {
  color: #4c9e44;
}
</style>
