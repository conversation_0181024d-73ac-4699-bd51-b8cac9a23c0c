<template>
	<view>
		<!-- 搜索框 -->
		<viwe style="padding: 15px 15px 0 15px; display: block;">
			<view class="search-bar">
			  <image src="../../../static/search.png" mode="widthFix" class="search-icon"></image>
			  <input v-model="data.searchKey" type="text" placeholder="输入活动名称进行搜索" @input="handleSearch" />
			</view>
		</viwe>
		<view class="complete" style="padding-bottom: 128px;">
			<template v-for="(item, index) in filterList" :key="item.id" :itemData='item'>
				<view class="fol-row active-list">
					<view class="active-img">
						<view class="float-list">
							<image mode="widthFix" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl + item.picUrl"></image> 
							<!-- <i class="tip">{{activeStatusLabel(item)}}</i> -->
						</view>
					</view>
					<view class="fol-col text-box" style="position: relative;">
						<view class="title-head">{{item.activityName}}</view>
						<view class="label">{{SPUP_GLOBAL_CONFIG.activeTypeLabel[item.type]}}</view>
						<!-- <view class="size-span">报名截止 ：{{dayjs(item.startTime).format("YYYY/MM/DD")}} - {{dayjs(item.endTime).format("YYYY/MM/DD")}}</view> -->
						<!-- <view class="btn-boxs fol-row" style="position: absolute;bottom: 0;">
							<view  class="fol-col"></view>
							<view class="btn-right-site">
								<button class="btn-s " @click="showRounds(item)">参与报名</button>
							</view>
						</view> -->
						<view class="fol-row" >
							<view class="fol-col"></view>
							<button class="btn-white btn-join" @click="showRounds(item)">查看签到</button>
						</view>
					</view>
				</view>
			</template>
			<view v-if="!filterList.length" class="empty-box" style="top: 20%;">
				<image class="empty-img" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl+'/empty/empty.png'"></image>
				<text class="empty-txt" style="">{{data.listTips}}</text>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {ref,reactive,onMounted,computed,watch} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import dayjs from "dayjs";
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {getActivityList, tempBatchGetRole} from "@/common/api/server.js";
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	
	const data = reactive({
		searchKey: '',
		listTips:"数据加载中……",
		list:[]
	})
	const filterList = computed(()=>{
		return data.list.filter(item=>item.activityName.indexOf(data.searchKey.trim())!=-1);
	})
	const showRounds = (item) => {
		uni.navigateTo({
			url: "/pages/active/admin/checkin_rounds?id="+item.activityId
		})
	}

	
	onLoad(options => {
		let _init = ()=>{
			getActivityList().then(res=>{
				data.list = res.data.filter(item=>item.status=="RUNNING");
				data.listTips = "暂无活动"
			})
		}
		//判断权限 
		tempBatchGetRole().then(res=>{
			commonStore.tmpBatch.menuCode = res.data.menuCode.split(",");
			if(commonStore.tmpBatch.menuCode.indexOf("62")>=0){
				_init();
			}else{
				uni.reLaunch({
					url: "/pages/common/forbidden"
				})
			}
		}).catch(err=>{
			data.msg = err.message ? err.message : "网络异常，请稍候再试"
			uni.showModal({
				title:"请求失败",
				content: "网络异常，请稍候再试",
				confirmText: "确定",
				showCancel: false,
			});
		});
	})
</script>

<style>
	
	
</style>
<style scoped>
	@import '../../common/common.css';
	@import "../active.css";
	/* 搜索开始 */
	.search-bar {
	  margin: 5px 0 15px 0;
	  display: flex;
	  justify-content: center;
	  text-align: center;
	  align-items: center;
	}
	.search-bar input {
	  width: 100%;
	  padding:7px 10px;
	  font-size: 14px;
	  border: 1px solid #d9d9d9;
	  border-radius: 25px;
	  padding-left: 45px;
	  text-align:left;
	}
	.search-icon {
	  position: absolute;
	  left:36px;
	  cursor: pointer;
	  width: 16px;
	}
	/* 搜索结束 */
</style>