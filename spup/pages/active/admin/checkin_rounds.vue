<template>
  <view class="container">
    <!-- 搜索框 -->
    <view class="search-bar">
	  <image src="../../../static/search.png" mode="widthFix" class="search-icon"></image>
      <input v-model="data.searchKey" type="text" placeholder="输入场次名称进行搜索" @input="handleSearch" />
    </view>

    <!-- 列表显示 -->
    <view class="list-item"  v-for="(item, index) in filterList" :key="item.id" @click="showDetail(item)">
		<view class="fol-row list-title">
		 <view class="item-title">{{ item.actRoundInfo }}</view>
		 <view :class="'round-status round-status-'+item.status">
		   {{ SPUP_GLOBAL_CONFIG.roundStatus.find(tmp=>tmp.id==item.status)? SPUP_GLOBAL_CONFIG.roundStatus.find(tmp=>tmp.id==item.status).name : "*"}}
		 </view>  
		</view>
		<view class="size-regular">类型：{{ SPUP_GLOBAL_CONFIG.roundType.find(tmp=>tmp.id==item.type) ? SPUP_GLOBAL_CONFIG.roundType.find(tmp=>tmp.id==item.type).name : '*' }}</view>
		<view class="size-regular">报名人数：{{ item.actRoundSubmitNumber }} / {{ item.actRoundMaxSubmitNum }}</view>
		<view class="size-regular">场次时间：{{ item.actRoundStartDateTime.slice(0, -3) }} ~ {{ item.actRoundEndDateTime.slice(0, -3) }}</view>
    </view>
  </view>
  <view v-if="!filterList.length" class="empty-box" style="top: 20%;">
  	<image class="empty-img" :src="SPUP_GLOBAL_CONFIG.mediaBaseUrl+'/empty/empty.png'"></image>
  	<text class="empty-txt" style="">{{data.listTips}}</text>
  </view>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import {onLoad, onUnload} from '@dcloudio/uni-app';
import {tempBatchGetRole, getActivityDetail, getActivityRound} from "@/common/api/server.js";
import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'	
import {base64Encode} from '@/common/myUtil.js'
import {useCommonStore} from '@/stores/common.js'
const commonStore = useCommonStore();

const data = reactive({
	listTips:"数据加载中……",
	searchKey: '',
	id:0,
	detail:{
		rounds:[]
	}
})

const showDetail = (round)=>{
	let params = {
		id: round.actRoundId,
		name: round.actRoundInfo
	}
	uni.navigateTo({
		url: `/pages/active/admin/checkin_users?p=${base64Encode(JSON.stringify(params))}`
	})
}

const filterList = computed(()=>{
	return data.detail.rounds.filter(item=>item.actRoundInfo.indexOf(data.searchKey.trim())!=-1);
})

onLoad(options => {
	let _init = ()=>{
		if(options.id){
			data.id = options.id
			getActivityDetail(data.id).then(res=>{
				data.detail = res.data;
				// data.detail.rounds.forEach(item=>item.status='RUNNING');
				data.listTips = '暂无场次';
				uni.setNavigationBarTitle({
				　　title: data.detail.activity.activityName
				})
			})
		}else{
			uni.showModal({
				content: '参数错误',
				showCancel: false
			});
		}
	}
	//判断权限
	tempBatchGetRole().then(res=>{
		commonStore.tmpBatch.menuCode = res.data.menuCode.split(",");
		if(commonStore.tmpBatch.menuCode.indexOf("62")>=0){
			_init();
		}else{
			uni.reLaunch({
				url: "/pages/common/forbidden"
			})
		}
	}).catch(err=>{
		console.log("err:", err);
		data.msg = err.message ? err.message : "网络异常，请稍候再试"
		uni.showModal({
			title:"请求失败",
			content: "网络异常，请稍候再试",
			confirmText: "确定",
			showCancel: false,
		});
	});
})

</script>

<style scoped>
@import '../../common/common.css';
@import "../active.css";
page{
	background: #F7F9FA;
}
.container {
  padding: 15px;
}

/* 搜索开始 */
.search-bar {
  margin: 5px 0 15px 0;
  display: flex;
  justify-content: center;
  text-align: center;
  align-items: center;
}
.search-bar input {
  width: 100%;
  padding:7px 10px;
  font-size: 14px;
  border: 1px solid #d9d9d9;
  border-radius: 25px;
  padding-left: 45px;
  text-align:left;
}
.search-icon {
  position: absolute;
  left:36px;
  cursor: pointer;
  width: 16px;
}
/* 搜索结束 */

.list-item {
  background: #fff;
  justify-content: space-between;
  padding: 15px;
  margin-top: 20px;
  border-radius: 5px;
  border: 0.8px solid #d9d9d9;
}
.list-title{
	border-bottom: 0.8px solid #d9d9d9;
	padding-bottom: 8px;
	margin-bottom: 8px;
}
.item-title {
  font-weight: 600;
  font-size: 16px;
  flex: 1;
}

.item-type {
  font-style: italic;
  width: 80px;
  text-align: center;
}
</style>
