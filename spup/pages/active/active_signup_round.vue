<template>
	<view class="title-date">请选择场次</view>
	<view class="time-list-wrap">
		<template v-for="(itemData, index) in data.detail.rounds" :key="itemData.id" :itemData='itemData' >
			<view class="time-item-wrap" @click.stop="radioClick(itemData)" >
				<view style="display:flex;">
					<view class="time-item-left batch-name">{{itemData.actRoundInfo}}</view>
					<view class="time-item-radio">
						<text class="num-p">剩余名额：{{itemData.actRoundMaxSubmitNum-itemData.actRoundSubmitNumber>0?itemData.actRoundMaxSubmitNum-itemData.actRoundSubmitNumber:0}}</text>
						<radio @click.stop="radioClick(itemData)" :disabled='itemData.actRoundMaxSubmitNum-itemData.actRoundSubmitNumber<=0' :checked="data.curTimeId==itemData.id" class="checkbox-size" color="#815462" />
					</view>
				</view>
				<view class="batch-count">
					名额：{{itemData.actRoundSubmitNumber}} / {{itemData.actRoundMaxSubmitNum}}
				</view>
				<view class="batch-time">
					时间：{{itemData.actRoundStartDateTime.substring(0,16)}} 至 {{itemData.actRoundEndDateTime.substring(0,16)}}
				</view>
				
				
			</view>
		</template>
	</view>
	<view class="btn-wrap">
		<button class="btn-color-huge" @click="okClick">确定</button>
	</view>
</template>

<script setup>
	import {ref,reactive,onMounted,computed} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	import {useCommonStore} from '@/stores/common.js'
	const commonStore = useCommonStore();
	import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
	import {getActivityRound} from "@/common/api/server.js";
	import dayjs from "dayjs"; 
	
	const data = reactive({
		activityId:0,
		detail:{
			rounds:[]
		},
		curTimeId:0
	})
	
	const radioClick = itemData=>{
		if(itemData.left<=0){
			uni.showToast({icon: "none", title:'该场次名额已满'})
		}else{
			data.curTimeId = itemData.id;
		}
	}
	
	const okClick = ()=>{
		let selectItem = data.detail.rounds.filter(item=>item.id==data.curTimeId);
		if(selectItem.length==0){
			uni.showToast({icon: "error", title:'请选择时间'})
			return;
		}
		commonStore.curActiveRound = selectItem[0];
		uni.navigateBack({
			delta: 1
		});
	}
	onLoad(options => {
		console.log("options:", options);
		if(commonStore.curActiveRound){
			data.curTimeId = commonStore.curActiveRound.id
		}
		if(options.activityId){
			data.activityId = options.activityId
			getActivityRound(data.activityId).then(res=>{
				let tmpArr = res.data.filter(item=>item.roundVo.visible&&!item.roundVo.greyButton);
				data.detail.rounds =  tmpArr.map(item=>{
					return item.roundVo.actRound
				});
				// data.detail.rounds = res.data.map(item=>{
				// 	if(item.hasSubmitted){
				// 		item.round.hasSubmitted = item.hasSubmitted
				// 	}
				// 	return item.round
				// })
			})
		}else{
			uni.showModal({
				content: '参数错误',
				showCancel: false
			});
		}
	})
</script>

<style scoped>
	@import '../common/common.css';
	.time-list-wrap{
		/* margin: 20rpx; */
		
	}
	.title-date{
		padding: 20px 15px 10px 15px;
		font-size: 16px;
		font-weight: 500;
	}
	.time-item-left{
		flex: 1;
		font-size: 15px;
		font-weight: 500;
	}
	.time-item-wrap{
		border-bottom: 1px solid #f0f0f0;padding:10px 15px;background: #fff;
	}
	.btn-wrap{
		width: 100%;
		padding-top: 10px;
		/* display: flex; */
		position: fixed;
		/* #ifdef H5 */
		/* left: var(--window-left);
		right: var(--window-right); */
		/* #endif */
		bottom: 0;
	}
	.num-p{
		padding-right: 10px;
		font-size: 13px;
	}
	
	.batch-name{
		
	}
	.batch-count{
		color : #888;
		font-size: 12px;
	}
	.batch-time{
		color : #888;
		font-size: 12px;
	}
</style>