import {Base64} from 'js-base64';

//判断是不是PC端
export function isPc() {
	return window.innerWidth > 1024;  // 屏幕宽度小于等于 1024px 时认为是移动端
}

//以换行符分割字符串
export function splitStringToLines(text) {
	const reNewline = /\r\n|\n|\r|<br>/;
	return text.split(reNewline);
}
//将一个字符串进行 Base64 编码
export function base64Encode(str) {
	return Base64.encode(str, true);  
  // return btoa(unescape(encodeURIComponent(str)));
}
//将一个 Base64 编码的字符串解码回原始字符串
export function base64Decode(base64Str) {
	return Base64.decode(base64Str, true);
    // return decodeURIComponent(escape(atob(base64Str)));
}

//电话号码加密
export function maskPhoneNumber(phoneNumber){
  // 将输入转换为字符串
  const str = String(phoneNumber);

  // 如果号码长度小于等于 3，直接返回全掩码
  if (str.length <= 3) {
    return '*'.repeat(str.length);
  }

  // 如果号码长度大于 3 但小于 7，掩码前 3 位
  if (str.length < 7) {
    return '*'.repeat(3) + str.slice(3);
  }

  // 如果号码长度大于等于 7，掩码中间 4 位
  const prefix = str.slice(0, 3); // 前 3 位
  const middle = '*'.repeat(4);   // 中间 4 位掩码
  const suffix = str.slice(-4);   // 后 4 位

  return prefix + middle + suffix;
}

/**
 * 跳转到某个页面（默认跳转到上一页，没有上一页时跳转到url)
 * @param {Object} tips:跳转前的提示语
 * @param {Object} url:没有上一页时的跳转路径
 * @param {Object} delay:延迟跳转（毫秒)
 */
export function backOrGotoPage(tips, url, delay){
	if(getCurrentPages().length>1){
		url = getCurrentPages()[getCurrentPages().length-2].$page.fullPath;
	}
	gotoPage(tips, url, delay);
}

/**
 * 跳转到某个页面
 * @param {Object} tips:跳转前的提示语
 * @param {Object} url:跳转路径
 * @param {Object} delay:延迟跳转（毫秒)
 */
export function gotoPage(tips, url, delay){
	if(tips){
		uni.showToast({
			icon:'success',
			mask: true,
			title: tips,
		})
	}
	if(delay){
		setTimeout(function(){
			uni.navigateTo({
				url: url
			})
		}, delay);
	}else{
		uni.navigateTo({
			url: url
		})
	}
}

//拉起文件选择框
export function selectFile(count, extension, _callBack){
	uni.chooseFile({
		count: count, //默认9
		sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
		sourceType: ['album','camera'],   //album 从相册选图，camera 使用相机
		extension : extension || ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'link', 'jpg', 'jpeg', 'png', 'bmp', 'gif', 'mp3', 'mp4'],
		success: _callBack,
		fail: function(err){
			uni.showToast({
				icon:'error',
				title:"拉起文件选择窗口失败："+ JSON.stringify(err)
			})
		}
	});
}
// 用于处理姓名长度超过 15 个字符时，中间部分用 *** 替换：
export function formatName(name) {
  if (name.length > 15) {
    // 保留前6个和后6个字符，中间用***替换
    const firstPart = name.substring(0, 6);
    const lastPart = name.substring(name.length - 6);
    return `${firstPart}***${lastPart}`;
  }
  // 不超过15个字符直接返回原姓名
  return name;
}
