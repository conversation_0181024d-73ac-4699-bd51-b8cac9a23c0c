//支付公用代码
import { ref, computed } from "vue";

export default function () {
	//支付
	const payOrder = (timeStamp, nonceStr, packageStr, paySign, callback) => {
		uni.requestPayment({
			provider: 'wxpay', //支付类型-固定值
			timeStamp: timeStamp, // 时间戳（单位：秒）
			nonceStr: nonceStr, // 随机字符串
			package: packageStr, // 
			signType: 'RSA', //固定值
			paySign: paySign, //签名
			success: function(res) {
				uni.showModal({
					content: '支付成功',
					showCancel: false,
					complete: callback
				});
			},
			fail: function(err) {
				console.log('支付失败:' + JSON.stringify(err));
				uni.showToast({
					title: "支付失败",
					icon: 'error'
				});
			}
		});
	}
	return {
		payOrder
	}
}
