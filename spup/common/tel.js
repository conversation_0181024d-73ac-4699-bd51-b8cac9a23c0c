//点击电话号码 公用代码

export default function () {
	//点击电话号码 
	const telClick = (telNumber)=>{
		uni.showActionSheet({
			title: '',
			itemList: ['呼叫', '复制号码'],
			popover: {
				// 104: navbar + topwindow 高度，暂时 fix createSelectorQuery 在 pc 上获取 top 不准确的 bug
				// top: 100,
				// left: 0
			},
			success: (e) => {
				console.log(e.tapIndex);
				// uni.showToast({
				// 	title: "点击了第" + e.tapIndex + "个选项",
				// 	icon: "none"
				// })
				if(e.tapIndex===0){	
					uni.makePhoneCall({
						phoneNumber: telNumber ,//仅为示例
						success:(res)=>{
							 console.log('拨打电话makePhoneCall调用成功!', res)
						},
						fail: (res) => {
							console.log('拨打电话makePhoneCall调用失败!', res)
							uni.showToast({
								icon:'error',
								title:'拨打电话失败'
							})
						}
					});
				}else{
					uni.setClipboardData({
						data: telNumber,
						success:(res)=>{
							 console.log('复制电话到剪贴板setClipboardData调用成功!', res)
						},
						fail: (res) => {
							console.log('复制电话到剪贴板setClipboardData调用失败!', res)
							uni.showToast({
								icon:'error',
								title:'复制号码失败'
							})
						}
					});
				}
			}
		})
	}
	
	return {
		telClick
	}
}
