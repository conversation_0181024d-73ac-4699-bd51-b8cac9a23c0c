//可参考：https://blog.csdn.net/weixin_48168510/article/details/121481665

import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'

//token
const _tokenKey = "spup_token";
function setToken(token) {
	uni.setStorageSync(_tokenKey, token);
}
function getToken() {
	let token = uni.getStorageSync(_tokenKey);
	if(SPUP_GLOBAL_CONFIG.env=="dev"){	//非生产环境可以使用一个长期有效的token
		token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzM4NCJ9.eyJ1bmlvbmlkIjoib2pxekwweS1rc2xUV0hEZTdYR1NLZ284OFRjOCIsIm9wZW5pZCI6Im80R3lFNU9BaFhydk1DYmN3c2dOc19QbG5IUm8iLCJleHAiOjE3ODE4MzAyMjl9.q9jUvFpj2m7zQcXwKs5mUMIWJhk8NJwpom4C3OCThDrG9waNjETzbLe7Fu5jMOTs'
	}
	return token;
}

function removeToken() {
    uni.removeStorage({key: _tokenKey})
}

//通过code，换取 token,openid,unionid
async function _getBaseinfoByCode(code, url1, url2){	
	return new Promise((resolve,reject)=>{
		uni.request({	//客户端向腾讯服务器成功获取授权临时票据（code）后,向业务服务器发起登录请求。
			url: SPUP_GLOBAL_CONFIG.baseUrl+url1+code, 
			success: (res) => {
				console.log("请求token success:", res)
				if(res.data.status===true){	
					setToken(res.data.data.token);
					if(url2){
						_http({	
							url: url2
						}).then((res2) => {
							console.log("请求用户头像、昵称等数据：", res2);
							res2.data.isNew = res.data.data.isNew;		
							resolve(res2.data);
						}).catch(err2=>{
							console.log("请求用户头像、昵称等数据 err2:", err2);
							reject({title:"请求用户信息失败", message:err2.message ? err2.message : '网络繁忙,请稍候再试'});
						})
					}else{
						resolve(res.data);
					}
				}else{
					reject({title:"登录失败", message:res.data.message ? res.data.message : '网络繁忙,获取token失败'});
				}
			},
			fail: function (err) {
				console.log("请求token fail:", err);
				reject({title:"登录失败", message:'网络繁忙,登录api调用失败'});
			}
		});
	})
}

// //微信登录，请求token,openid,unionid
// async function _login(url1, url2){	
// 	return new Promise((resolve,reject)=>{
// 		uni.login({
// 			"provider": "weixin",
// 			"onlyAuthorize": true, // 微信登录仅请求授权认证
// 			success: function(event){
// 				//客户端成功获取授权临时票据（code）,向业务服务器发起登录请求。
// 				uni.request({
// 					url: SPUP_GLOBAL_CONFIG.baseUrl+url1+event.code, 
// 					success: (res) => {
// 						console.log("请求token success:", res)
// 						if(res.data.status===true){
// 							// unionid = res.data.data.unionid;
// 							// openid = res.data.data.openid;
// 							// token = res.data.data.token;
// 							// isNew = res.data.data.isNew;			
// 							setToken(res.data.data.token);
// 							_http({
// 								url: url2
// 							}).then((res2) => {
// 								console.log("请求用户基本数据：", res2);
// 								res2.data.isNew = res.data.data.isNew;		
// 								resolve(res2.data);
// 							});
// 						}else{
// 							uni.showModal({
// 								content: '网络繁忙,获取token失败',
// 								showCancel: false
// 							});
// 						}
// 					},
// 					fail: function (err) {
// 						console.log("请求token fail:", err);
// 						uni.showModal({
// 							content: '网络繁忙,token api调用失败',
// 							showCancel: false,
// 							complete:()=>{
// 							}
// 						});
// 					}
// 				});
// 			},
// 			fail: function (err) {
// 				console.log("uni.login 调用失败:", err);
// 				uni.showModal({
// 					content: '当前网络不稳定',
// 					showCancel: false,
// 					complete:()=>{
// 					}
// 				});
// 			}
// 		})
// 	})
// }

async function _logout(){
	removeToken();
}
// 参考:https://juejin.cn/post/6971236142310129695

async function _http(options){
	return new Promise((resolve,reject)=>{
		if(options.modal){//模态请求
			uni.showLoading({
			    title: typeof options.modal === 'string' ? options.modal : "提交中…",
			    mask: true
			});
		}
		uni.request({
			url:SPUP_GLOBAL_CONFIG.baseUrl+options.url,
			//请求url中如果没有method，默认是get
			method:options.method||'GET',
			//请求url中如果没有data，默认为空
			data: options.data || {},
			header: {'Authorization': getToken()
					 , 'content-type': 'application/json;charset:utf-8'
					},
			success:res=>{
				console.log("我的拦截器-success: ", res)
				if(options.modal){//模态请求
					uni.hideLoading()
				}
				if(res.data.code===401 || res.data.code===600 || res.data.code===601 ||res.data.code===603){//token有问题
					reject({message:"token error"});
				}else{
					if(res.data.status===true){
						resolve(res.data)
					}else{
						uni.showToast({
							icon: "none",
							title: res.data&&res.data.message ? res.data.message : '请求失败'
						})
						reject(res.data);
					}
				}
			},
			fail:err=>{
				console.log("我的拦截器-fail: ", err)
				if(options.modal){//模态请求
					uni.hideLoading()
				}
				uni.showToast({
					icon:'loading',
					title:'请求失败'
				})
			}
		})
	})
} 





export {_getBaseinfoByCode, _logout, _http}