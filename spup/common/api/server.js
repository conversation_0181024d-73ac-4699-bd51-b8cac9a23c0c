import {useCommonStore} from '@/stores/common.js'
import {configRefStore} from '@/stores/configRef.js'
import {_getBaseinfoByCode,_logout,_http} from "@/common/api/myInterceptor.js"
import {SPUP_GLOBAL_CONFIG} from '@/common/config.js'
import dayjs from "dayjs";
	
export async function login(){
	const commonStore = useCommonStore()
	if(SPUP_GLOBAL_CONFIG.env == 'dev') {
		console.log(SPUP_GLOBAL_CONFIG.env)
		console.log(SPUP_GLOBAL_CONFIG.plant);
		commonStore.userInfo.unionid = "ojqzL0-hOlek3HMyLjhvKjTfDnnA";
		commonStore.userInfo.miniOpenid = "ouHc26KEbNy6EzNOTMv-RdR1SrnA";
		commonStore.userInfo.mpOpenid = "ouHc26KEbNy6EzNOTMv-RdR1SrnA";
		commonStore.userInfo.isNew = true;
	}
	commonStore.lifeMsg +=  "【"+dayjs().format("hh:mm:ss")+"server拦截器-login:"+ SPUP_GLOBAL_CONFIG.plant+"】\n"
	return new Promise((resolve,reject)=>{
		if(SPUP_GLOBAL_CONFIG.plant=="MP-WEIXIN"){	//微信小程序登录
			uni.login({
				"provider": "weixin",
				"onlyAuthorize": true, // 微信登录仅请求授权认证
				success: function(event){
					_getBaseinfoByCode(event.code, "/spup/miniprogram/jscode2session/", '/spup/customer/get/').then(loginRes=>{
						// getNotes()
						commonStore.userInfo = loginRes;
						resolve(commonStore.userInfo);
					})
				},
				fail: function (err) {
					console.log("uni.login 调用失败:", err);
					uni.showModal({
						content: '请求超时，请稍候再试',
						showCancel: false,
						complete:()=>{
							reject(err)
						}
					});
				}
			})
		}else if(SPUP_GLOBAL_CONFIG.plant=="H5-WX"){	//微信小程序登录		//公众号
			// commonStore.urlBeforeMpLogin = window.location.hash.substr(1)
			commonStore.urlBeforeMpLogin = window.location.href
			let callbackUrl = window.location.origin + window.location.pathname + "#" + SPUP_GLOBAL_CONFIG.mpLoginRedirectUrl
			console.log('wxlogin callbackUrl', callbackUrl)
			let url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" +SPUP_GLOBAL_CONFIG.mpAppId+
                    "&redirect_uri=" + encodeURIComponent(callbackUrl)+
                    "&response_type=code" +
                    "&scope=snsapi_userinfo" +
                    "&state=mp"
			location.href = url
			resolve()
		}else if(SPUP_GLOBAL_CONFIG.plant=="H5-SSB"){	//随申办
			commonStore.urlBeforeMpLogin = window.location.href
			let YOUR_CLIENT_ID = SPUP_GLOBAL_CONFIG.ssbAppId
			let YOUR_REGISTERED_REDIRECT_URI = SPUP_GLOBAL_CONFIG.baseUrl + "/spup-mp/#" + SPUP_GLOBAL_CONFIG.ssbLoginRedirectUrl
			let timestamp = dayjs().valueOf()
			let url = "http://api.eshimin.com/api/oauth/authorize?client_id="+YOUR_CLIENT_ID
						+"&response_type=code&redirect_uri="+encodeURIComponent(YOUR_REGISTERED_REDIRECT_URI)
						+"&scope=read&timestamp="+timestamp
			commonStore.lifeMsg +=  "【"+dayjs().format("hh:mm:ss")+"- ssbIndex redirect url: "+encodeURIComponent(YOUR_REGISTERED_REDIRECT_URI)+"】\n"
			window.location.href = url
			resolve()
		}
	});
}

/**
 * 公众号通过code登录
 * @param {Object} code	
 * @param {Object} getUserInfo 是否获取用户基本信息， 1=获取 ，其它=不获取 
 */
export async function mpLoginByCode(code, getUserInfo){
	const commonStore = useCommonStore()
	return new Promise((resolve,reject)=>{	
		if(getUserInfo){
			_getBaseinfoByCode(code, "/spup/mp/mpcode2session/", '/spup/customer/get/').then(loginRes=>{
				commonStore.userInfo = loginRes;
				// getNotes()
				resolve(commonStore.userInfo);
			}).catch(err=>{
				reject(err)
			})
		}else{
			_getBaseinfoByCode(code, "/spup/mp/mpcode2session/").then(loginRes=>{
				console.log("XXXXX", loginRes);
				// getNotes()
				commonStore.userInfo.unionid = loginRes.data.unionid;
				commonStore.userInfo.miniOpenid = loginRes.data.openid;
				commonStore.userInfo.mpOpenid = loginRes.data.openid;
				commonStore.userInfo.isNew = loginRes.data.isNew;
				// commonStore.userInfo.userGender = loginRes.data.userGender;
				// commonStore.userInfo.job = loginRes.data.job;
				resolve(commonStore.userInfo);
			}).catch(err=>{
				reject(err)
			})
		}
	});
}
/**
 * 随申办通过code登录
 * @param {Object} code	
 */
export async function ssbLoginByCode(code){
	const commonStore = useCommonStore()
	return new Promise((resolve,reject)=>{	
		_getBaseinfoByCode(code, "/spup/ssb/jscode2session/").then(loginRes=>{
			console.log("/spup/ssb/jscode2session/", loginRes);
			commonStore.userInfo.unionid = loginRes.data.unionid;
			// commonStore.userInfo.miniOpenid = loginRes.data.openid;
			// commonStore.userInfo.mpOpenid = loginRes.data.openid;
			// commonStore.userInfo.isNew = loginRes.data.isNew;
			// commonStore.userInfo.userGender = loginRes.data.userGender;
			// commonStore.userInfo.job = loginRes.data.job;
			resolve(commonStore.userInfo);
		}).catch(err=>{
			reject(err)
		})
	});
}

export async function logout(){
	const commonStore = useCommonStore()
	commonStore.userInfo = {};
	await _logout();
}
		
export async function http(options){
	console.log("server拦截器：", options);
	const commonStore = useCommonStore()
	commonStore.lifeMsg +=  "【"+dayjs().format("hh:mm:ss")+"server拦截器-http:"+ JSON.stringify(options)+"】\n"
	return new Promise((resolve,reject)=>{
		if(!commonStore.userInfo.unionid){
			login().then(loginRes=>{
				options.url = options.url.replace("{{unionid}}",commonStore.userInfo.unionid);
				_http(options).then(res2=>resolve(res2)); 
			});
		}else{
			options.url = options.url.replace("{{unionid}}",commonStore.userInfo.unionid);
			_http(options).then(res=>resolve(res)).catch(err=>{
				console.log("server拦截到error：", err);
				if(err.message==="token error"){	//token无效
					login().then(loginRes=>{
						_http(options).then(res2=>resolve(res2)); 
					});
				}else{
					reject(err)
				}
			});
		}
	});
}



//请求各种须知
export async function getNotes(){
	http({url: '/spup/instructions/get'}).then(res=>{
		const configStore = configRefStore()
		try{
			let audienceNotice = JSON.parse(res.data.audienceNotice);
			configStore.yyxz = audienceNotice.yyxz;
			configStore.wmcgxz = audienceNotice.wmcgxz;
			configStore.jdwpqd = audienceNotice.jdwpqd;
		}catch(err){
			console.log("老版的：", err);
			configStore.yyxz = res.data.audienceNotice
		}
		configStore.visitNote = res.data.visitingInstructions;
		configStore.openTime = res.data.admissionNotice;
	});
}
////场馆信息
export async function noteGet(section){
	 return http({
	    url: '/spup-admin/info/get/'+section,
	    method: 'POST',
	  })
}
//获取可预约日期范围(针对临展,特展)
export async function getAvailableTimeByExhibitionNo(exhibitionNo, category){
	return http({url: '/spup/appointment/getAvailableTime/'+exhibitionNo+"/"+category});
}
//获取可预约日期范围
export async function getAvailableTime(category){
	return http({url: '/spup/appointment/getAvailableTime/'+category});
}
//获取选择日期预约情况(针对临展,特展)
export async function getDetailByDateByExhibitionNo(exhibitionNo, category, date){
	return http({url: '/spup/appointment/getDetailByDate/'+category+"/"+exhibitionNo+"/"+date});
}
//获取选择日期预约情况
export async function getDetailByDate(category, date){
	return http({url: '/spup/appointment/getDetailByDate/'+category+"/"+date});
}
//请求预约时间
export async function getReserveTime(category){
	return http({url: '/spup/appointment/getTime/'+category});
}
//下单-个人票
export async function createOrder(batchNo, contacts){
	return http({
		url: '/spup/order/createOrder',
		method: 'POST',
		data: {
			"batchNo": batchNo,
			"contacts": JSON.stringify(contacts)
		}
	});
}
//下单-团体票
export async function createTeamOrder(data){
	return http({
		url: '/spup/teamOrder/createOrder',
		method: 'POST',
		data: data
	});
}
//下单-展项票
export async function createExhibitionOrder(batchNo, contacts){
	return http({
		url: '/spup/itemOrder/createOrder',
		method: 'POST',
		data: {
			"batchNo": batchNo,
			"contacts": JSON.stringify(contacts)
		}
	});
}
//下单-临展票
export async function createTmpExhibitionOrder(batchNo, contacts, exhibitionNo, category){
	return http({
		url: '/spup/order/createOrder',
		method: 'POST',
		data: {
			"batchNo": batchNo,
			"contacts": JSON.stringify(contacts),
			"exhibitionNo": exhibitionNo,
			"category":category
		}
	});
}
//请求订单列表-个人预约
export async function getOrderList(){
	return http({url: '/spup/order/list'});
}
//取消预约-个人预约
export async function cancelOrder(orderNo){
	return http({url: '/spup/order/cancel/'+orderNo});
}
//删除预约记录-个人预约
export async function deleteOrder(orderNo){
	return http({url: '/spup/order/delete/'+orderNo});
}
//请求订单列表-团体预约
export async function getTeamOrderList(){
	return http({url: '/spup/teamOrder/list'});
}
//取消预约-团体预约
export async function cancelTeamOrder(orderNo){
	return http({url: '/spup/teamOrder/cancel/'+orderNo});
}
//删除预约记录-团体预约
export async function deleteTeamOrder(orderNo){
	return http({url: '/spup/teamOrder/delete/'+orderNo});
}
//请求订单列表-展项预约
export async function getExhibitionOrderList(){
	return http({url: '/spup/itemOrder/list'});
}
//取消预约-展项预约
export async function cancelExhibitionOrder(orderNo){
	return http({url: '/spup/itemOrder/cancel/'+orderNo});
}
//删除预约记录-展项预约
export async function deleteExhibitionOrder(orderNo){
	return http({url: '/spup/itemOrder/delete/'+orderNo});
}


//取得联系人
export async function getContacts(){
	return http({url: '/spup/customer/getContacts'});
}
//取得可以预约的联系人
export async function getAvaildContacts(){
	return http({url: '/spup/customer/getAvaildContacts'});
}
//删除联系人
export async function deleteContacts(contactId){
	return http({url: '/spup/customer/deleteContacts/'+contactId});
}
//添加联系人
export async function addContacts(name, phone, idcardCategory, idcardNo, remark){
	return http({
		url: '/spup/customer/addContacts',
		method: 'POST',
		data: {
			"idcardCategory": idcardCategory,
			"idcardNo": idcardNo,
			// "idcardNo": "310000888888888888",
			"name": name,
			"phone": phone,
			"remark": remark
		}
	});
}
//修改联系人
export async function modifyContacts(id, name, phone, idcardCategory, idcardNo, remark){
	return http({
		url: '/spup/customer/modifyContacts',
		method: 'POST',
		data: {
			"id": id, 
			"idcardCategory": idcardCategory,
			"idcardNo": idcardNo,
			// "idcardNo": "310000888888888888",
			"name": name,
			"phone": phone,
			"remark": remark
		}
	});
}

//留言 
export async function comment(purpose, content, visit_date, visit_time){
	return http({
		url: '/spup/comment/save',
		method: 'POST',
		data: {
			"purpose": purpose, 
			"content": content,
			"visitTime": visit_date+" "+visit_time
		}
	});
}

//请求统计数据
export async function getStatistics(){
	return http({url: '/spup/analysis/realtime'});
}
//请求统计数据
export async function getNewTeamOrder(){
	return http({url: '/spup/analysis/getNewTeamOrder'});
}
//请求特展团队统计数据
export async function getTmpExhibitionTeamStatistics(exhibitionNo, startDate, endDate, orderStatus){
	let params = {exhibitionNo:exhibitionNo,
					startDate:startDate,
					endDate:endDate
				}
	if(orderStatus){
		params["orderStatus"] = orderStatus
	}
	return http({
		url: '/spup/analysis/getTeamOrderList',
		method: 'POST',
		data: params
	});
}
//特展团队-确认接待
export async function tmpExhibitionTeamConfirmReception(id, orderRemark, orderStatus){
	let params = {id:id,
					orderRemark:orderRemark,
					orderStatus:orderStatus
				}
	return http({
		url: '/spup/analysis/confirmTeam',
		method: 'POST',
		data: params
	});
}



//更新我的用户信息
export async function saveCustomer(name, phone, idcardCategory, idcardNo, gender, job){
	const commonStore = useCommonStore()
	return http({
		url: '/spup/customer/saveCustomer',
		method: 'POST',
		data: {
			"unionid": commonStore.userInfo.unionid,
			"cardCategory": idcardCategory, //证件类型
			// "cardNo": idcardNo, //身份证号
			"cardNo": "310000888888888888",
			"job": job, //职业
			"phone": phone, //手机号
			"realName": name, //姓名
			"userGender": gender, //性别，0未知，1男，2女
		}
	});
}

// //取得活动列表
// export async function getActivityList(){
// 	return http({url: '/spup/activity/list'});
// }
// //取得我已报名的活动列表
// export async function getMyActivityList(){
// 	return http({url: '/spup/activity/myEntry'});
// }
// //取得活动报名情况
// export async function getActivityDetail(id){
// 	return http({url: '/spup/activity/viewActivity/'+id});
// }

// //查询报名情况
// export async function getActivityEntry(id){
// 	return http({url: '/spup/activity/viewEntry/'+id});
// }

// //活动报名
// export async function activityEntry(params){
// 	return http({
// 		url: '/spup/activity/entry',
// 		method: 'POST',
// 		data: params
// 	});
// }


//取得活动列表
export async function getActivityList(){
	return http({url: '/spup/app/activity/list', modal:"数据请求中"});
}
//取得活动预览列表
export async function getActivityPreviewList(){
	return http({url: '/spup-admin/manage/activity/preview/list', modal:"数据请求中", method:"post"});
}
//取得我已报名的活动列表
export async function getMyActivityList(){
	return http({url: '/spup/app/activityRound/mySubmitted', modal:"数据请求中"});
}
//取得活动详情
export async function getActivityDetail(id){
	return http({url: '/spup/app/activity/detail/'+id, modal:"数据请求中"});
}
//获取活动场次
export async function getActivityRound(activityId){
	return http({url: '/spup/app/activityRound/'+activityId, modal:"数据请求中"});
}
//活动报名
export async function activityEntry(actRoundId, params){
	return http({
		url: '/spup/app/activityRound/submit/'+actRoundId,
		method: 'POST',
		data: params,
		alertType: "showModal",
		modal:"提交中……",
	});
}
//活动-取消报名
export async function activityCancelSubmit(actRoundId){
	return http({url: '/spup/app/activityRound/cancel/'+actRoundId,
				alertType: "showModal",
				modal:"提交中……"
			});
}
//活动-可签到场次
export async function getActivityCheckinList(){
	return http({url: '/spup/app/activityRound/checkin/list', modal:"数据请求中"});
}
//活动-签到
export async function activityCheckin(actRoundId){
	return http({
				url: '/spup/app/activityRound/checkin/'+actRoundId,
				alertType: "showModal",
				modal:"签到中……",
			});
}
//活动-统计-按活动ID取得报名列表
export async function getActivitySubmit(activityId){
	return http({url: '/spup/app/activity/submit/list/'+activityId, modal:"数据请求中"});
}

//活动-获取场次报名情况(含签到信息)
export async function getActivitySubmitByRoundId(actRoundId) {
  return http({
    url: '/spup-admin/manage/activityRound/'+actRoundId,
    method: 'POST',
	modal:"数据请求中"
  })
}


//取得线上导览数据
export async function getGuideSpotList(){
	return http({url: '/spup/guide/getData'});
}
//浏览展项（方便后台统计展项浏览数据而埋的点）
export async function viewGuide(id){
	return http({url: '/spup/guide/read/'+id});
}


//取得意见反馈（问卷调查）数据
export async function getQuestion(id){
	if(id){
		return http({url: '/spup/questionnare/get/'+id});
	}
	return http({url: '/spup/questionnare/get'});
}


//提交意见反馈（问卷调查）
export async function postQuestion(questionId, answer){
	let params = {answer: JSON.stringify(answer)}
	if(questionId){
		params["questionnaireId"] = questionId
	}
	return http({
		url: '/spup/questionnare/save',
		method: 'POST',
		data: params,
		modal:"提交中……",
	});
}

//取得文创商品列表
export async function getShopGoodsList(){
	return http({url: '/spup/goods/listByPage?pageNum=1&pageSize=999999'});
}
//取得文创商品详情
export async function getShopGoodsDetail(id){
	return http({url: '/spup/goods/viewGoods/'+id});
}

//取得临展列表
export async function getTmpExhibitionList(){
	return http({url: '/spup/exhibition/list'});
}
//通过展项编号取得临展详情
export async function getTmpExhibitionDetail(exhibitionNo){
	return http({url: '/spup/exhibition/detail/'+exhibitionNo});
}


//临时场次，获取用户角色
export async function tempBatchGetRole(){
	return http({url: '/spup/tempBatch/user/role', modal:true, method: 'POST'});
}
//临时场次，场次时刻列表
export async function tempBatchList(){
	return http({url: '/spup/tempBatch/time/list', modal:true, method: 'POST'});
}
//临时场次，开启场次
export async function tempBatchOpen(params){
	return http({url: '/spup/tempBatch/open',
				modal:true, 
				data: params,
				method: 'POST',
			});
}
