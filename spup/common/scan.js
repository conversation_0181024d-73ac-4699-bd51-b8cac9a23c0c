//扫一扫公用代码 
import { ref} from "vue";
import permision from "@/common/permission.js";
import {scanSignIn} from "@/common/api/server.js";
import {useCommonStore} from '@/stores/common.js'
const commonStore = useCommonStore()
export default function () {
	const scanTxt = ref("");
	const scanClick = async ()=>{
		// #ifdef APP-PLUS
		let status = await checkPermission();
		if (status !== 1) {
			return;
		}
		// #endif
		// 需要注意的是小程序扫码不需要申请相机权限
		uni.scanCode({
			success: (res) => {
				scanTxt.value = res.result;
				let params = {};
				let errorFlag = false;
				try{
					params = JSON.parse(res.result)
					if(!params.orderId || !params.waiterId){
						errorFlag = true;
					}
				}catch (err){
					errorFlag = true;
				}
				if(errorFlag){
					uni.showModal({
						title: '不是这个二维码',
						content:"请到店后扫描店员展示的二维码",
						confirmText: "确定",
						success: function(res) {
							
						}
					})
				}else{
					uni.showModal({
						title: '扫描成功',
						content: params.goodsName +"（"+ params.reserveTime +"）已就绪,是否开始您的服务？",
						cancelText: "再等一等",
						confirmText: "马上开始",
						success: function(res) {
							if (!res.cancel) {
								scanSignIn(params.orderId, params.waiterId).then((res) => {
									if(res.status===true){
										uni.showModal({
											title: '签到成功',
											content: "马上开始您的服务",
											confirmText: "确定",
											success: function(res) {
												
											}
										})
									}else{
										uni.showModal({
											title: '签到失败',
											content: res.message,
											confirmText: "确定",
											success: function(res) {
												
											}
										})
									}
								});
							}
						}
					})
				}
			},
			fail: (err) => {
				uni.showModal({
					title: '扫描失败',
					content:"请到店后扫描店员展示的二维码",
					confirmText: "确定",
					success: function(res) {
						
					}
				})
			}
		});
	}
	return {
		scanTxt,
		scanClick
	}
}
