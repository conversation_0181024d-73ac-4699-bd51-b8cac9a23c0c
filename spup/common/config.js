let ENV = "dev"; 	//dev 开发环境, test=测试环境，pro=生产环境

const SPUP_GLOBAL_CONFIG = {
	env: ENV,
	baseUrl: ENV=="pro" ? 'https://pduppac.org.cn' :'https://supec.douwifi.cn',
	// baseUrl: ENV=="pro" ? 'https://pduppac.org.cn' :'http://localhost:8080',
	// baseUrl: ENV=="pro" ? 'https://pduppac.org.cn' :'https://pduppac.org.cn',
	mediaBaseUrl: ENV=="pro" ? 'https://pduppac.org.cn/spup-media' :'https://supec.douwifi.cn/spup-media',
	plant : "MP-WEIXIN", 				//运行平台：MP-WEIXIN=微信小程序，H5=网页
	mpAppId : ENV=="pro" ? 'wxb971efaed01158f4' : 'wx1fe676e844bb15e6' , //公众号appId   spup：hd
	mpLoginRedirectUrl : "/pages/index/mpLogin",	//公众号OAuth2.0登录重定向地址
	ssbAppId :  ENV=="pro" ? "b2uKQt06mL" : "OGfqiUeAvl",	//随申办appId
	ssbLoginRedirectUrl : "/pages/index/ssbLogin",			//随申办OAuth2.0登录重定向地址
	refreshInterval: ENV=="pro" ? 300000: 30000,
	orderStatus: {
		"reserving": 1,
		"reserved": 2,
		"completed": 4,
		"canceled": 8,
		"breakPromise":16
	},
	orderStatusLabel: {
		1: "预约中",
		2: "已预约",
		4: "已完成",
		8: "已取消",
		16: "爽约"
	},
	orderType: {
		"person": 1,
		"team": 2,
		"exhibition": 4,
		"tmpExhibition": 8,
		"tmpExhibitionTeam": 16,
		"fypdTmpBatch": 32,
		"activy": 1024
	},
	orderTypeLabel: {
		1: "个人预约",
		2: "团体预约",
		4: "展项预约",
		8: "临时展项",
		16: "临时展项团体预约",
		32: "飞阅浦东临时场次",
		1024: "活动报名"
	},
	exhibitionType: {
		"model": 1,
		"pudong": 2
	},
	exhibitionTypeLabel: {
		1: "大模型厅影片",
		2: "飞阅浦东"
	},
	idType0:[{
				name: '身份证',
				id: 1
			},{
				name: '护照',
				id: 2
			},{
				name: '港澳通行证',
				id: 3
			},{
				name: '台湾通行证',
				id: 4
			},{
				name: '台胞证',
				id: 5
			},{
				name: '外国人永久居留身份证',
				id: 6
			}
	],
	// activeTypeLabel: {
	// 	1:"展馆活动",
	// 	2:"公教活动"
	// },
	// activeTimeType: {
	// 	"last": 1,
	// 	"recent": 2
	// },
	// activeTimeTypeLabel: {
	// 	1: "往期活动",
	// 	2: "最新活动"
	// },
	
	idType:[{
				name: '身份证',
				id: "IDCARD" , 
			},{
				name: '护照',
				id: "PASSPORT"
			}
			// ,{
			// 	name: '其它证件',
			// 	id: "OTHER"
			// }
	],
	roundType:[{
				name: '公众场（无同行人）',
				id: "NORMAL"
			},{
				name: '公众场（有同行人）',
				id: "NOLIMIT"
			},{
				name: '亲子场',
				id: "CHILD"
			},{
				name: '特殊',
				id: "SPECIAL"
			}
	],
	roundSignUserType:[{
				name: '普通',
				id: "NORMAL"
			},{
				name: '成人',
				id: "ADULT"
			},{
				name: '儿童',
				id: "CHILD"
			},{
				name: '特殊',
				id: "SPECIAL"
			}
	],		
	
	//活动状态：READY, RUNNING, CLOSED, DEPRECATED
	activityStatus:[{
			name: '预览中',
			id: "PREVIEW"
		},{
			name: '准备中',
			id: "READY"
		},{
			name: '进行中',
			id: "RUNNING"
		},{
			name: '已结束',
			id: "CLOSED"
		},{
			name: '已下线',
			id: "DEPRECATED"
		}
	],
	//场次状态：READY, SUBMITTING, WAITING, RUNNING, CLOSED, DEPRECATED;
	roundStatus:[{
			name: '准备中',
			id: "READY"
		},{
			name: '报名中',
			id: "SUBMITTING"
		},{
			name: '暂停报名',
			id: "SUBMITSTOPPED"
		},{
			name: '报名已结束',
			id: "WAITING"
		},{
			name: '进行中',
			id: "RUNNING"
		},{
			name: '已结束',
			id: "CLOSED"
		},{
			name: '已下线',
			id: "DEPRECATED"
		}
	],
	
	//场次报名状态：SUBMITTED=已报名； CHECKEDIN=已签到
	roundSignupStatus:[{
			name: '已报名',
			id: "SUBMITTED"
		},{
			name: '已签到',
			id: "CHECKEDIN"
		},{
			name: '已取消',
			id: "CANCELLED"
		}
	],
	activeTypeLabel: {
		"NORMAL":"公众场",
		"CHILD":"亲子场",
		// "LECTURE":"讲座",
		// "CITY_LAB":"城市实验室",
		// "SPECIAL":"特殊"
	},
	
	
	goodsCategory:{
		type1:1,
		type2:2,
		type3:3,
		type4:4,
		type0:100,
	},
	goodsCategoryLabel:{
		// 1:"书籍",
		2:"日用文具",
		3:"工艺品",
		4:"图案印刷品",
		5:"包装产品"
	},
	permanentExhibition:{
		id:99999, 
		exhibitionTitle:"浦东城市规划展", 
		detailBannerPic:"/tmpExhibition/99999.png?v=8", 
		exhibitionAddress:"上海市浦东新区高科西路2499号",
		category:1
	}
};

export {SPUP_GLOBAL_CONFIG};