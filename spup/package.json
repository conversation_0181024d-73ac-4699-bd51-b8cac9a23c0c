{"name": "spup-frontend", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "dev:h5": "vite", "build:h5": "vite build"}, "dependencies": {"dayjs": "^1.11.7", "js-base64": "^3.7.7", "pinia": "^2.0.29", "pinia-plugin-persist-uni": "^1.2.0", "qrcode-base64": "^1.0.1", "vue": "^3.5.17"}, "devDependencies": {"@dcloudio/uni-cli-i18n": "^2.0.2-4070520250711001", "@dcloudio/uni-cli-shared": "^2.0.2-4070520250711001", "@dcloudio/vite-plugin-uni": "^3.0.0-alpha-3000020210521001", "@vitejs/plugin-vue": "^6.0.0", "sass-embedded": "^1.89.2", "vite": "^7.0.4"}}