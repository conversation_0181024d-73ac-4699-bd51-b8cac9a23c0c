// 简单的路由配置，用于Vite开发环境
import { createRouter, createWebHashHistory } from 'vue-router'

// 动态导入页面组件
const routes = [
  {
    path: '/',
    redirect: '/pages/home/<USER>'
  },
  {
    path: '/pages/home/<USER>',
    name: 'Home',
    component: () => import('./pages/home/<USER>')
  },
  {
    path: '/pages/home/<USER>',
    name: 'HomePreview', 
    component: () => import('./pages/home/<USER>')
  },
  {
    path: '/pages/tmpExhibition/tmpExhibition_list',
    name: 'TmpExhibitionList',
    component: () => import('./pages/tmpExhibition/tmpExhibition_list.vue')
  },
  {
    path: '/pages/buyPerson/buyPerson',
    name: 'BuyPerson',
    component: () => import('./pages/buyPerson/buyPerson.vue')
  },
  {
    path: '/pages/order/orderList',
    name: 'OrderList',
    component: () => import('./pages/order/orderList.vue')
  },
  {
    path: '/pages/my/my_index',
    name: 'MyIndex',
    component: () => import('./pages/my/my_index.vue')
  },
  {
    path: '/pages/about/about_page',
    name: 'AboutPage',
    component: () => import('./pages/about/about_page.vue')
  },
  {
    path: '/pages/common/404',
    name: '404',
    component: () => import('./pages/common/404.vue')
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router
