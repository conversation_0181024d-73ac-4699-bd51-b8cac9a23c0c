<template>
  <view class="audio-card">
    <view class="audio">
      <view class="audio-main">
        <slider
          max="100"
          block-size="12"
          class="progress"
          :block-color="props.sliderColor"
          :activeColor="props.activeColor"
          :backgroundColor="props.backgroundColor"
          :value="data.progress"
          @change="handleChange"
          @changing="handleProgress"/>
        <view class="time" :style="{color: timeTxtColor}">
          <view>{{ dateFormart(data.currentTime) }}</view>
          <view>{{ dateFormart(data.duration) }}</view>
        </view>
      </view>
	  <image
	    v-if="!data.isPlay"
	    class="play-icon"
	    mode="scaleToFill"
	    :src="props.playIcon"
	    @click="audioPlay"/>
	  <image
	    v-if="data.isPlay"
	    class="play-icon"
	    mode="scaleToFill"
	    :src="props.pauseIcon"
	    @click="audioPause"/>
    </view>
  </view>
</template>

<script setup>
	import {reactive,onMounted,computed,watch} from 'vue'
	import {onLoad, onUnload} from '@dcloudio/uni-app';
	
	 const props = defineProps({
		audio: {
		  type: String,
		  default: ''
		},
		playIcon: {
		  type: String,
		  default: '/static/icons/audio-play.png'
		},
		pauseIcon: {
		  type: String,
		  default: '/static/icons/audio-pause.png'
		},
		sliderColor: {
		  type: String,
		  default: '#009EFF'
		},
		activeColor: {
		  type: String,
		  default: '#009EFF'
		},
		backgroundColor: {
		  type: String,
		  default: '#EFEFEF'
		},
		timeTxtColor: {
		  type: String,
		  default: '#CCC'
		}
	  })


	const data =  reactive({	
		innerAudioContext: null,
		isPlay: false,
		timer: null,
		currentTime: 0,
		duration: 0,
		progress: 0,
		intervalID: null
	})
	
	watch(()=>props.audio, (newValue, oldValue) => {
		console.log("src变化 :", newValue);
		creatAudio()
	})
	
	const innerAudioContext = uni.createInnerAudioContext();
	
	const creatAudio = ()=> {
	  innerAudioContext.onError((res) => {
		console.log("creatAudio报错");
	    console.log(res.errMsg);
	    console.log(res.errCode);
	  });
	  data.progress = 0;
	  data.isPlay = false
	  innerAudioContext.src = encodeURI(props.audio)
	  innerAudioContext.sessionCategory = "soloAmbient"
	  innerAudioContext.autoplay = false
	  innerAudioContext.volume = 1
	  innerAudioContext.startTime = 0
	  innerAudioContext.play()
	  innerAudioContext.onTimeUpdate(() => {})
	  // innerAudioContext.onCanplay(() => {
	  //   data.duration = innerAudioContext.duration
	  // })
	  innerAudioContext.onCanplay(() => {
	    data.intervalID = setInterval(() => {
	      if (innerAudioContext.duration !== 0) {
	        clearInterval(data.intervalID);
	        data.duration = innerAudioContext.duration
	      }
	    }, 100);
	  })
	  setTimeout(() => {
	    innerAudioContext.pause()
	  }, 100)
	}
	// 开始播放
	const audioPlay = ()=>{
	  if (dateFormart(data.currentTime) === dateFormart(data.duration)) {
	    data.progress = 100
	  }
	  innerAudioContext.src = encodeURI(props.audio)
	  innerAudioContext.startTime = 0
	  innerAudioContext.volume = 1
	  innerAudioContext.sessionCategory = "soloAmbient"
	  innerAudioContext.seek(Number(data.progress) === 100 ? 0 : (data.currentTime ? data.currentTime : 0))
	  innerAudioContext.startTime = Number(data.progress) === 100 ? 0 : (data.currentTime ? data.currentTime : 0)
	  innerAudioContext.play()
	  onCanplay()
	  data.isPlay = true
	}
	const onCanplay = ()=> {
	  innerAudioContext.onCanplay(() => {
	    data.currentTime = innerAudioContext.currentTime
	  })
	  innerAudioContext.onTimeUpdate(() => {
	    data.currentTime = innerAudioContext.currentTime
	    data.progress = (innerAudioContext.currentTime / innerAudioContext.duration * 100).toFixed(2)
	  })
	  innerAudioContext.onEnded(() => {
	    if (data.isPlay) {
	      data.isPlay = false
	    }
	  })
	}
	// 暂停播放
	const audioPause = ()=> {
	  innerAudioContext.pause()
	  data.isPlay = !data.isPlay
	}
	// 拖动进度条
	const handleProgress = (event)=> {
	  innerAudioContext.pause()
	}
	// 拖动进度条结束
	const handleChange = (event) =>{
	  data.progress = event.detail.value
	  data.currentTime = data.duration * (event.detail.value / 100)
	  audioPlay()
	}
	// 处理时间格式(总时长装换时分秒)
	const dateFormart = (value) =>{
	  if(isNaN(Number(value))){
		  return "--:--"
	  }
	  value = Number(value).toFixed(0)
	  let hour = Math.floor(value / 3600)
	  let minute = Math.floor((value - hour * 3600) / 60)
	  let second = value - hour * 3600 - minute * 60
	  hour = hour < 10 ? `0${hour}` : hour
	  minute = minute < 10 ? `0${minute}` : minute
	  second = second < 10 ? `0${Math.floor(second)}` : Math.floor(second)
	  return `${minute}:${second}`
	}
	
	onMounted(() => {
		creatAudio() 
	})
	
	onUnload(() => {
		innerAudioContext.destroy()
	})
	
</script>

<style lang="scss" scoped>
.audio-card {
  height: 100rpx;
  display: flex;
  flex-direction: column;
  padding: 10rpx 24rpx;
  .title {
    color: #333333;
    font-size: 26rpx;
    font-weight: 500;
  }
  .audio {
    margin-top: 15rpx;
    display: flex;
    .play-icon {
      width: 60rpx;
      height: 60rpx;
	  margin-left: 20rpx;
    }
    .audio-main {
      height: 48rpx;
      flex: 1;
      margin-left: 15rpx;
    }
    .progress {
      margin: 0;
    }
    .time {
      width: 100%;
      height: 50rpx;
      font-size: 24rpx;
      font-weight: 400;
      color: #666666;
      margin-top: -15rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
}
</style>