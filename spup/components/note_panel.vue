<template>
	<uni-popup ref="popup" background-color="#fff" :mask-click="false">
		<view class="popup-content">
			<view class="">
				<view class="flex-row justify-between">
					<view class="popup-title">预约须知</view>
				</view>
				<view class="content" >
					{{configStore.yyxz}}<br>
				<!-- 	•《参观指南》(<text style="text-decoration:underline;" @click="visitorNoteClick">{{visitorNoteVisible?"点击关闭详情":"点击查看详情"}}</text>）。<br>
					<view v-if="visitorNoteVisible" class="visit-note-wrap">
						{{configStore.visitNote}}
					</view>
					• 预约成功即视为已认可上述须知，感谢您的支持与配合，祝您参观愉快！<br> -->
				</view>
				<view class="flex-row justify-between">
					<view class="popup-title">文明参观须知</view>
				</view>
				<view class="content" >
					<view v-if="configStore.wmcgxz.indexOf('《禁带物品清单》')!=-1">
						{{configStore.wmcgxz.split("《禁带物品清单》")[0]}}《禁带物品清单》(<text style="text-decoration:underline;color:#8d5b6e;" @click="visitorNoteClick">{{visitorNoteVisible?"点击关闭详情":"点击查看详情"}}</text>）。
						<view v-if="visitorNoteVisible" class="visit-note-wrap">
							{{configStore.jdwpqd}}
						</view>
						{{configStore.wmcgxz.split("《禁带物品清单》")[1]}}
					</view>
					<view v-else>{{configStore.wmcgxz}}</view>
				</view>
				<!-- 按钮 -->
				<view class="btns">
					<!-- <navigator style="margin: 0 5%;font-size: 16px;" class="btn-n" open-type="exit" target="miniProgram">不同意</navigator> -->
					<button :class="countdown>0?'btn-n':'btn-s'" type="default"
						@click="agreeClick">我已阅读并同意{{countdownLabel}}</button>
					<!-- <button class="btn-s" :type="default" @click="agreeClick">同意{{countdownLabel}}</button> -->
				</view>
			</view>
		</view>
	</uni-popup>
</template>

<script setup>
	import {ref, reactive, onMounted, computed} from 'vue'
	import {configRefStore} from '@/stores/configRef.js'
	const configStore = configRefStore();
	import {getNotes} from "@/common/api/server.js";
	
	const visitorNoteVisible = ref(false)
	const visitorNoteClick = ()=>{
		visitorNoteVisible.value = !visitorNoteVisible.value
	}
	//倒计时
	const countdown = ref(0)
	const countdownLabel = computed(() => {
		return countdown.value <= 0 ? '' : '(' + countdown.value + 's)'
	});
	const popup = ref();
	let timer;
	const agreeClick = () => {
		if(countdown.value<=0){
			popup.value.close()
		}
	}
	const clearTimer = ()=>{
		if(timer){
			clearInterval(timer);
		}
	}
	const noteClick = (needCountdown)=>{
		popup.value.open("bottom")
		if(needCountdown){
			countdown.value = 3
			clearTimer()
			timer = setInterval(() => {
				if (countdown.value <= 0) {
					clearInterval(timer)
				} else {
					countdown.value--
				}
			}, 1000)
		}
	}
	
	onMounted(()=>{
		getNotes();
		noteClick(true);
	})
</script>

<style lang="scss" scoped>
	.popup-content {
		height: 75vh;
		overflow-y: auto;
		font-size: 14px;
		align-items: center;
		justify-content: center;
		width: 100%;
		// height: 100%;
		background-color: #fff;
		.content{
			padding: 0 15px;
			white-space: pre-wrap;
		}
		.visit-note-wrap{
			margin: 20rpx;
			background: #9999992e;
			padding: 20rpx; 
			border-radius: 10rpx;
		}
		.popup-title {
			width: 100%;
			text-align: center;
			margin: auto;
			font-size: 18px;
			font-weight: 500;
			color: rgba(0, 0, 0, 0.85);
			line-height: 24px;
			padding: 15px 0;
		}
		.subTitle{
			font-weight: 600;
			font-size:15px;
		}
		.btns{
			width: 100%;
			text-align: center;
			display: flex;
			padding: 10px 0 35px 0;
		}
		.btn-n{	
			font-size: 14px;
			width: 40%;
			height: 36px;
			// margin: 10px 6px;
			border-radius: 25px;
			border: 1px solid #ccc;
			color: var(--button-color);
			background: #none;
		}
		.btn-s{
			font-size: 14px;
			width: 40%;
			height: 36px;
			// margin: 10px 6px;
			border-radius: 25px;
			border: 1px solid var(--button-color);
			color: var(--button-color);
			background: #F4F2F3;
		}
		
	}
</style>